html {
  box-sizing: border-box;
  font-size: 62.5%;
}
.password-modal__content-heading {
  cursor: pointer;
  text-align: center;
  font-family: var(--heading-font);
  font-size: var(--body-font-size, 14px);
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  letter-spacing: -0.28px;
  text-decoration-line: underline;
}

.password-modal__content-heading .text {
  padding-left: 5px;
  font-size: 16px;
  text-decoration: underline;
  text-underline-offset: 5px;
}
.password-header {
  margin: 40px 0;
}
@media only screen and (max-width: 749px) {
  .password-header {
    text-align: center;
    margin: 20px 0px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
  }

  .password-modal__content-heading {
    justify-content: center;
  }

  .password__footer {
    margin-bottom: 30px;
  }
}

.password-link {
  display: flex;
  align-items: center;
  font-size: 1.4px;
  font-weight: 400;
  white-space: nowrap;
}

.password-link svg {
  width: 16px;
  height: 16px;
  margin-right: 10px;
}
.password-logo {
  width: 100%;
}
@media only screen and (max-width: 420px) {
  .password-header {
    justify-content: center;
  }
}

@media only screen and (min-width: 750px) {
  .password-logo {
    margin-bottom: 0;
  }
}

.password__footer-caption a {
  padding: 0;
  color: rgb(var(--color-link));
}

@media only screen and (min-width: 750px) {
  .password-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

.password-content {
  text-align: center;
}

@media only screen and (max-width: 749px) {
  .password-content {
    margin-bottom: 18px;
    margin-top: 10px;
  }
}

.password-modal__content {
  display: none;
}

#password0_0 .dlg-heading {
  padding: 30px;
}
.password-logo-layout {
  display: flex;
  align-items: center;
}
.password-logo-layout svg {
  margin: 0 20px;
}
.password-logo-layout p {
  color: var(--Heading, #111);
  text-align: center;
  font-family: var(--body-font);
  font-size: 16px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
  letter-spacing: -0.32px;
}
.password-modal .password-form {
  max-width: 50rem;
}

.password-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin-top: 4rem;
  margin-bottom: 2rem;
  width: 100%;
}

.password-field.field {
  flex: 1 20rem;
}

.password-field .form__message {
  margin-top: 1.5rem;
}

.password-button {
  margin-top: 1.5rem;
  width: 100%;
}
@media only screen and (max-width: 767.98px) {
  .password-field--error + .password-button {
    margin-top: 1.5rem;
  }
}

body .tingle-modal {
  --overlay-bg: rgba(0, 0, 0, 0.6);
  background: var(--overlay-bg);
  padding-top: 0;
  display: none;
  backdrop-filter: none;
  -webkit-backdrop-filter: none;
  display: none;
  opacity: 1;
  visibility: visible;
  --swiper-pagination-mt: 2rem;
}
body .tingle-modal__close {
  right: 10px;
  top: 10px;
  width: 40px;
  height: 40px;
}
.tingle-modal__closeIcon {
  color: var(--dark-grey, #666);
  font-size: 0;
}
.tingle-modal .tingle-modal-box {
  max-width: var(--popup-max-width, unset);
  width: 500px;
  margin: auto;
  display: none;
}
.tingle-modal .tingle-modal-box__content {
  max-height: 80vh;
  overflow: auto;
}
body .tingle-modal--visible {
  animation: fadeIn var(--duration-long, 0.5s) forwards;
  display: flex;
}
body .tingle-modal--visible .tingle-modal-box {
  animation: translateIn var(--duration-long, 0.5s) ease forwards;
  display: block;
}
body .remove-loading {
  animation: fadeOut var(--duration-long, 0.5s) forwards;
}
body .remove-loading .tingle-modal-box {
  animation: translateOut var(--duration-long, 0.5s) forwards;
}
.rounded-style .tingle-modal-box {
  border-radius: 15px;
  overflow: hidden;
}
body .tingle-modal-box__content {
  padding: var(--tingle-padding, 3rem);
}
@media (max-width: 767.98px) {
  body .tingle-modal-box__content {
    padding: var(--tingle-padding, 2rem);
  }
}
@keyframes translateIn {
  from {
    transform: translateY(15px);
  }
  to {
    transform: translateY(0);
  }
}
@-webkit-keyframes translateIn {
  from {
    transform: translateY(15px);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes translateOut {
  from {
    transform: translateY(0);
  }
  to {
    transform: translateY(15px);
  }
}
@-webkit-keyframes translateOut {
  from {
    transform: translateY(0);
  }
  to {
    transform: translateY(15px);
  }
}
.login-popup-modal {
  --popup-max-width: 50rem;
  --duration-long: 0.4s;
}
#login-popup [aria-hidden="true"] {
  display: none;
}
#login-popup [aria-hidden="false"] {
  animation: 0.2s fadeIn forwards;
}
.show-pass-word.text .icon-hide {
  display: none;
}
.show-pass-word.text .icon-view {
  display: inline-block;
  color: var(--color-heading);
}
@media (max-width: 575.98px) {
  .tingle-modal .tingle-modal-box {
    width: 100%;
  }
  .shopable-video__product-information {
    opacity: 1;
    visibility: visible;
    pointer-events: auto;
    width: 100%;
    transform: translateY(0);
    transition: var(--transition);
  }
  .video-item__popup-media--ratio {
    min-height: 60vh;
  }
  body .tingle-modal__close {
    background-color: var(--color-white);
    box-shadow: 5px 0px 30px 0px rgba(0, 0, 0, 0.08);
    border-radius: 50%;
    top: 1rem;
    right: 1rem;
  }
  .tingle-modal__closeIcon svg {
    width: 11px;
    height: 11px;
  }
}
.password-modal-title {
  --font-h3: 2.4rem;
  margin: 0;
  font-size: clamp(1.6rem, 3.5vw, var(--font-h3));
  line-height: 1;
  text-transform: capitalize;
  font-family: var(--heading-font);
  text-align: center;
}
.field__input-pass {
  width: 100%;
}
.password__footer-text a {
  text-decoration: underline;
  text-underline-offset: 5px;
}
.password__footer-text {
  font-family: var(--body-font);
  font-size: var(--body-font-size, 14px);
}
.bls-main-password {
  padding: 0 15px;
  gap: 15px;
  display: flex;
  margin-bottom: 80px;
}
.image_banner {
  width: 50%;
}
.main-content {
  width: 50%;
}
.bls__password-banner {
  aspect-ratio: 1;
}
@media (max-width: 1024px) {
  .password-logo-layout svg {
    margin: 0 10px;
  }
  .bls-main-password {
    padding: 0px 15px;
  }
  .image_banner {
    display: none;
  }
  .main-content {
    width: 100%;
  }
  .bls__password-with-banner {
    width: 100%;
  }
}
@media (min-width: 1440px) {
  .wrapper {
    margin-left: auto;
    width: 72%;
  }
}
.wrapper {
  height: 100%;
}
