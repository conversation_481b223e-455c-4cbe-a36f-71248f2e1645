<div
  class="swiper-actions flex block-md content-center gap-10 mb-30 mb-md-0 {{ class }} {{ class_prodcut_mobile }}"
>
  <div
    class="swiper-arrow {{ class }} transition swiper-button-prev bg-white btn-hover inline-flex content-center border {% if settings.rounded_corner > 1 %} rounded-50 overflow-hidden{% endif %}"
  >
    <svg width="6" height="11" fill="none">
      <use href="#icon-back" />
    </svg>
  </div>
  <div
    class="swiper-arrow {{ class }} transition swiper-button-next bg-white btn-hover inline-flex content-center border {% if settings.rounded_corner > 1 %} rounded-50 overflow-hidden{% endif %}"
  >
    <svg width="6" height="11" fill="none">
      <use href="#icon-next" />
    </svg>
  </div>
</div>
