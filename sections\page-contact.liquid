{{ 'page-contact.css' | asset_url | stylesheet_tag }}
{%- liquid
  assign section_st = section.settings
  assign section_width = section_st.section_width
  assign color_scheme = section_st.color_scheme
  assign heading = section_st.heading
  assign description = section_st.description
  assign column_gap = section_st.column_gap
  if section_st.reset_spacing
    assign reset_spacing = ' remove_spacing'
  endif
  assign iframe = section_st.iframe
  assign scroll_animation = settings.scroll_animation
-%}
{%- capture style -%}
  --section-pt: {{ section_st.padding_top }}; --section-pb: {{ section_st.padding_bottom }};
  --column_gap: {{ column_gap }}px;
{%- endcapture -%}
<div
  class="section sec__collection-list color-{{ color_scheme }} gradient{{ reset_spacing }}"
  style="{{ style | strip | strip_newlines }}"
>
  <div class="{{ section_width }}">
    {% if iframe != null %}
      <div class=" w-full map rounded mb-50" style="{{ style | strip | strip_newlines }}">
        {{ iframe }}
      </div>
    {% endif %}
    <div class="flex flex-wrap contact-container">
      {% for block in section.blocks %}
        {%- liquid
          assign block_st = block.settings
          assign name = block_st.name | default: 'Your Name'
          assign email = block_st.email | default: 'Your Email'
          assign your_comment = block_st.your_comment | default: 'Enter please your message'
          assign button_label = block_st.button_label | default: 'Send'
          assign show_privacy = block_st.show_privacy
          assign heading = block_st.heading
          assign description = block_st.description
          assign heading_text_block = block_st.heading_text_block
          assign description_text_block = block_st.description_text_block
          assign content = block_st.content
        -%}
        {% case block.type %}
          {% when 'contact' %}
            <motion-element
              data-motion="fade-up-sm"
              {% if scroll_animation != 'slide_in' %}
                hold
              {% endif %}
              data-motion-delay="150"
              class="contact_form flex-1 {% if scroll_animation != 'none' %} scroll-trigger {{ scroll_animation }}{% endif %}"
              {%- if scroll_animation != 'none' -%}
                style="--animation-order: {{ forloop.index0 }};"
              {% endif %}
            >
              <div class="contact_header">
                {% if heading != blank %}
                  <h3 class="section__header-heading h4 heading-letter-spacing mt-0">{{ heading }}</h3>
                {% endif %}
                {% if description != blank %}
                  <div class="section__header-des rich__text-m0 mb-25">
                    {{ description }}
                  </div>
                {% endif %}
              </div>
              {% form 'contact' %}
                {% if form.posted_successfully? %}
                  <p class="success inline-flex gap-10 align-center lh-normal">
                    <svg width="18" height="18" fill="none">
                      <g stroke="#137F24" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.3"><path d="m6.033 8.992 1.972 1.98 3.952-3.96"/><path d="M7.973 1.178c.565-.482 1.49-.482 2.062 0l1.293 1.113c.245.213.704.385 1.03.385h1.392c.867 0 1.579.712 1.579 1.579v1.39c0 .32.172.786.384 1.032l1.113 1.292c.483.565.483 1.49 0 2.062l-1.113 1.293a1.813 1.813 0 0 0-.384 1.03v1.392c0 .867-.712 1.579-1.58 1.579h-1.39c-.32 0-.786.172-1.031.384l-1.293 1.113c-.564.483-1.489.483-2.062 0L6.681 15.71a1.813 1.813 0 0 0-1.031-.384H4.234c-.867 0-1.579-.712-1.579-1.58v-1.398c0-.32-.172-.778-.376-1.023l-1.105-1.301c-.474-.565-.474-1.48 0-2.045L2.28 6.677c.204-.246.376-.704.376-1.023V4.247c0-.868.712-1.58 1.58-1.58H5.65c.319 0 .785-.171 1.03-.384l1.293-1.105Z"/></g>
                    </svg>
                    {{ 'general.contact.success' | t }}
                  </p>
                {% else %}
                  {{ form.errors | default_errors }}
                {% endif %}
                <div
                  class="flex flex-cols flex-wrap gap-20"
                  style="
                    --col-number: 1;
                    --col-desktop: 2;
                  "
                >
                  <div class="full-name flex-1 form-floating">
                    <input
                      type="text"
                      class="form-input form-input-placeholder w-full form-control"
                      name="contact[name]"
                      {% if form.name %}
                        value="{{ form.name }}"
                      {% elsif customer %}
                        value="{{ customer.name }}"
                      {% endif %}
                      id="inputName"
                      required
                      placeholder="{{ name }}"
                    >
                    <label class="field__label" for="customer_email">
                      {{ name | default:  'Name' }}
                    </label>
                  </div>
                  <div class="email flex-1 form-floating">
                    <input
                      type="email"
                      name="contact[email]"
                      placeholder="{{  email }}"
                      class="form-input form-input-placeholder w-full form-control"
                      id="email"
                      required
                    >
                    <label class="field__label" for="customer_email">
                      {{ email | default:  'Email' }}
                    </label>
                  </div>
                </div>
                <div class="form-floating textarea">
                  <textarea
                    class="w-full form-control"
                    name="contact[body]"
                    placeholder="{{ your_comment }}"
                    id="message"
                  ></textarea>
                  <label class="form__label" for="message">
                    {{ your_comment | default:  'Your_comment' }}
                  </label>
               </div>
                <terms-conditions class="form_action">
                  {% if show_privacy %}
                    <div class="flex">
                      <div class="checkbox-group relative">
                        <input
                          class="input w-20 h-20 m-0 opacity-0 absolute inset-0 pointer conditions_form_minicart"
                          type="checkbox"
                          name="conditions"
                          id="privacy"
                        >
                        <span class="checkmark relative me-10  pointer-none pointer inline-flex"></span>
                      </div>
                      <label for="privacy">{{ 'general.policies_html' | t }} </label>
                    </div>
                  {% endif %}

                  <button
                    class="block privacy_btn btn-checkout btn btn-primary {% if show_privacy == false %} active{% endif %} mt-30"
                    type="submit"
                    {% if show_privacy == true %}
                      disabled
                    {% endif %}
                  >
                    {{ button_label }}
                  </button>
                </terms-conditions>
              {% endform %}
            </motion-element>
          {% when 'text_block' %}
            <motion-element
              data-motion="fade-up-sm"
              {% if scroll_animation != 'slide_in' %}
                hold
              {% endif %}
              data-motion-delay="150"
              class="support_customer w-full {% if scroll_animation != 'none' %} scroll-trigger {{ scroll_animation }}{% endif %}"
              {%- if scroll_animation != 'none' -%}
                style="--animation-order: {{ forloop.index0 }};"
              {% endif %}
            >
              <div class="contact_header">
                {% if heading_text_block != blank %}
                  <h3 class="section__header-heading h4 heading-letter-spacing mt-0">{{ heading_text_block }}</h3>
                {% endif %}
                {% if description_text_block != blank %}
                  <p class="section__header-des">
                    {{ description_text_block }}
                  </p>
                {% endif %}
              </div>
              {% if content != blank %}
                {{ content }}
              {% endif %}
            </motion-element>
        {% endcase %}
      {% endfor %}
    </div>
  </div>
</div>
<script src="{{ 'contact.js' | asset_url }}" defer="defer"></script>

{% schema %}
{
  "name": "t:sections.contact.name",
  "tag": "section",
  "class": "overlay-section contact-us",
  "limit": 1,
  "settings": [
    {
      "type": "select",
      "id": "section_width",
      "label": "t:sections.all.section_width.label",
      "default": "fluid_container",
      "options": [
        {
          "value": "container",
          "label": "t:sections.all.section_width.container.label"
        },
        {
          "value": "fluid_container",
          "label": "t:sections.all.section_width.fluid_container.label"
        },
        {
          "value": "stretch_width",
          "label": "t:sections.all.section_width.stretch_width.label"
        },
        {
          "value": "full_width",
          "label": "t:sections.all.section_width.full_width.label"
        }
      ]
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.color_scheme.label",
      "default": "scheme-1"
    },
    {
      "type": "textarea",
      "id": "iframe",
      "label": "t:sections.contact.settings.iframe.label",
      "default": "<iframe src=\"https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d50941.0851952626!2d-95.78197131269138!3d37.06182424549097!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x87b778b9da9b5b1f%3A0x3df3aefa017361ac!2sFawn%20Creek%20Township%2C%20KS%2C%20USA!5e0!3m2!1sen!2s!4v1666059952368!5m2!1sen!2s\" height=\"450\" style=\"border:0;\" allowfullscreen=\"\" loading=\"lazy\" referrerpolicy=\"no-referrer-when-downgrade\"></iframe>"
    },
    {
      "type": "range",
      "id": "column_gap",
      "label": "t:sections.all.items.column_gap.label",
      "min": 0,
      "max": 120,
      "step": 5,
      "default": 100,
      "unit": "px"
    },
    {
      "type": "header",
      "content": "t:sections.all.section_padding.label"
    },
    {
      "type": "range",
      "id": "padding_top",
      "label": "t:sections.all.section_padding.top.label",
      "default": 0,
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px"
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "label": "t:sections.all.section_padding.bottom.label",
      "default": 0,
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px"
    },
    {
      "type": "checkbox",
      "id": "reset_spacing",
      "label": "t:sections.all.section_padding.reset_spacing.label",
      "default": false
    }
  ],
  "blocks": [
    {
      "type": "contact",
      "name": "t:sections.contact.name",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "heading",
          "label": "t:sections.all.section_header.heading.label"
        },
        {
          "type": "richtext",
          "id": "description",
          "label": "t:sections.all.section_header.description.label"
        },
        {
          "type": "checkbox",
          "id": "show_privacy",
          "label": "t:sections.contact.block.contact.settings.show_privacy.label",
          "default": true
        },
        {
          "type": "text",
          "id": "name",
          "label": "t:sections.contact.block.contact.settings.name.label"
        },
        {
          "type": "text",
          "id": "email",
          "label": "t:sections.contact.block.contact.settings.email.label"
        },
        {
          "type": "text",
          "id": "your_comment",
          "label": "t:sections.contact.block.contact.settings.your_comment.label"
        },
        {
          "type": "text",
          "id": "button_label",
          "label": "t:sections.contact.block.contact.settings.button_label.label"
        }
      ]
    },
    {
      "type": "text_block",
      "name": "t:sections.contact.block.text_block.label",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "heading_text_block",
          "label": "t:sections.all.section_header.heading.label"
        },
        {
          "type": "richtext",
          "id": "description_text_block",
          "label": "t:sections.all.section_header.description.label"
        },
        {
          "type": "richtext",
          "id": "content",
          "label": "t:sections.contact.block.text_block.settings.content.label"
        }
      ]
    }
  ]
}
{% endschema %}
