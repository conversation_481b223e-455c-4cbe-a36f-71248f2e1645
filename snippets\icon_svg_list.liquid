{%- if icon != 'none' -%}
  {%- case icon -%}
    {%- when 'free_shipping' -%}
      <svg viewBox="0 0 32 32" width="{{ custom_width | default: 20 }}" fill="none">
        <path d="M20.249 15.55v-4.575L8.386 4.125" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M2.962 8.3 14 14.687l10.962-6.35M14 26.013V14.675" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M11.412 2.1 4.737 5.813c-1.512.837-2.75 2.937-2.75 4.662v7.063c0 1.724 1.238 3.825 2.75 4.662l6.675 3.713c1.425.787 3.763.787 5.188 0l6.675-3.713c1.512-.837 2.75-2.938 2.75-4.662v-7.063c0-1.725-1.238-3.825-2.75-4.662L16.6 2.1c-1.438-.8-3.763-.8-5.188 0Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    {%- when 'check_badge' -%}
      <svg viewBox="0 0 32 32" fill="none" width="{{ custom_width | default: 20 }}">
        <path d="m10.5 15 3 3 6-6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M13.4 3c.9-.7 2.3-.7 3.2 0l2 1.8q.6.5 1.5.6h2.2c1.3 0 2.4 1 2.4 2.4v2q0 1 .6 1.7l1.7 2c.7.8.7 2.2 0 3l-1.7 2q-.6.7-.6 1.6v2.2c0 1.3-1.1 2.4-2.4 2.4H20c-.4 0-1.2.2-1.5.6l-2 1.7c-.9.7-2.3.7-3.2 0l-2-1.7q-.6-.6-1.5-.6H7.7a2.4 2.4 0 0 1-2.4-2.4V20q0-.9-.6-1.5l-1.6-2c-.8-.9-.8-2.3 0-3.2l1.6-2q.6-.6.6-1.5v-2c0-1.4 1.1-2.5 2.4-2.5H10q.9 0 1.6-.5z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    {%- when 'message_communications' -%}
      <svg viewBox="0 0 32 32" width="{{ custom_width | default: 20 }}" fill="none">
        <path d="M22.475 13.488v5c0 .325-.013.637-.05.937-.288 3.375-2.275 5.05-5.938 5.05h-.5c-.312 0-.612.15-.8.4l-1.5 2c-.662.887-1.737.887-2.4 0l-1.5-2c-.162-.212-.525-.4-.8-.4h-.5c-3.987 0-5.987-.988-5.987-5.988v-5c0-3.662 1.688-5.65 5.05-5.937.3-.037.613-.05.938-.05h8c3.987 0 5.987 2 5.987 5.988Z" stroke="currentColor" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M27.475 8.488v5c0 3.675-1.688 5.65-5.05 5.937.037-.3.05-.613.05-.938v-5c0-3.987-2-5.987-5.988-5.987h-8c-.325 0-.637.013-.937.05.287-3.362 2.275-5.05 5.937-5.05h8c3.988 0 5.988 2 5.988 5.988Z" stroke="currentColor" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M16.87 16.563h.01M12.494 16.563h.012M8.12 16.563h.01" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    {%- when 'boat' -%}
      <svg
        width="{{ custom_width | default: 25 }}"
        height="{{ custom_width | default: 25 }}"
        fill="none"
      >
        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M20.42 12.87c.87.35 1.41 1.38 1.21 2.29l-.41 1.86c-.71 3.2-3.22 5.48-6.84 5.48H9.62c-3.62 0-6.13-2.28-6.84-5.48l-.41-1.86c-.2-.91.34-1.94 1.21-2.29L5 12.3l5.51-2.21c.96-.38 2.02-.38 2.98 0L19 12.3l1.42.57ZM12 22.501v-12"/><path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M19 8.501v3.8l-5.51-2.21c-.96-.38-2.02-.38-2.98 0L5 12.301v-3.8c0-1.65 1.35-3 3-3h8c1.65 0 3 1.35 3 3ZM14.5 5.501h-5v-2c0-.55.45-1 1-1h3c.55 0 1 .45 1 1v2Z"/>
      </svg>
    {%- when 'truck' -%}
      <svg width="{{ custom_width | default: 24 }}" height="{{ custom_height | default: 25 }}" fill="none">
        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 14.501h1c1.1 0 2-.9 2-2v-10H6c-1.5 0-2.81.83-3.49 2.05"/><path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M2 17.501c0 1.66 1.34 3 3 3h1c0-1.1.9-2 2-2s2 .9 2 2h4c0-1.1.9-2 2-2s2 .9 2 2h1c1.66 0 3-1.34 3-3v-3h-3c-.55 0-1-.45-1-1v-3c0-.55.45-1 1-1h1.29l-1.71-2.99a2.016 2.016 0 0 0-1.74-1.01H15v7c0 1.1-.9 2-2 2h-1"/><path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M8 22.501a2 2 0 1 0 0-4 2 2 0 0 0 0 4ZM16 22.501a2 2 0 1 0 0-4 2 2 0 0 0 0 4ZM22 12.501v2h-3c-.55 0-1-.45-1-1v-3c0-.55.45-1 1-1h1.29l1.71 3ZM2 8.501h6M2 11.501h4M2 14.501h2"/>
      </svg>
    {%- when 'question' -%}
      <svg width="{{ custom_width | default: 30 }}" height="{{ custom_height | default: 30 }}" fill="none">
        <g stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"><path d="m23.088 21.038.488 3.95c.125 1.037-.988 1.762-1.875 1.224L16.463 23.1c-.575 0-1.137-.038-1.687-.113.925-1.087 1.475-2.462 1.475-3.95 0-3.55-3.075-6.424-6.875-6.424-1.45 0-2.788.412-3.9 1.137a7.923 7.923 0 0 1-.05-.95c0-5.688 4.937-10.3 11.037-10.3 6.1 0 11.038 4.612 11.038 10.3 0 3.375-1.738 6.363-4.413 8.238Z"/><path d="M16.25 19.038c0 1.488-.55 2.863-1.475 3.95-1.238 1.5-3.2 2.463-5.4 2.463l-3.263 1.937c-.55.338-1.25-.125-1.175-.762l.313-2.463c-1.675-1.162-2.75-3.025-2.75-5.125 0-2.2 1.175-4.137 2.975-5.287a7.104 7.104 0 0 1 3.9-1.138c3.8 0 6.875 2.875 6.875 6.425Z"/></g>
      </svg>
    {%- when 'secure' -%}
      <svg width="{{ custom_width | default: 30 }}" height="{{ custom_height | default: 30 }}" fill="none">
        <g stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"><path d="M26.138 13.9c0 6.113-4.437 11.838-10.5 13.513a2.45 2.45 0 0 1-1.275 0c-6.062-1.675-10.5-7.4-10.5-13.513V8.413c0-1.025.775-2.188 1.738-2.575l6.962-2.85a6.484 6.484 0 0 1 4.888 0l6.962 2.85c.95.387 1.738 1.55 1.738 2.575l-.013 5.487Z"/><path stroke-miterlimit="10" d="M15 15.625a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5ZM15 15.625v3.75"/></g>
      </svg>
    {%- when 'protection' -%}
      <svg width="{{ custom_width | default: 30 }}" height="{{ custom_height | default: 30 }}" fill="none">
        <g stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"><path d="m10.475 15 3.012 3.025 6.038-6.05"/><path d="M13.437 3.063c.863-.738 2.275-.738 3.15 0l1.975 1.7c.375.325 1.075.587 1.575.587h2.125a2.422 2.422 0 0 1 2.413 2.413v2.125c0 .487.262 1.2.587 1.575l1.7 1.975c.738.862.738 2.275 0 3.15l-1.7 1.975c-.325.375-.587 1.075-.587 1.575v2.125a2.422 2.422 0 0 1-2.413 2.412h-2.125c-.487 0-1.2.263-1.575.588l-1.975 1.7c-.862.737-2.275.737-3.15 0l-1.975-1.7c-.375-.325-1.075-.588-1.575-.588H7.725a2.422 2.422 0 0 1-2.413-2.412v-2.138c0-.487-.262-1.187-.575-1.562L3.05 16.575c-.725-.862-.725-2.262 0-3.125l1.687-1.987c.313-.375.575-1.075.575-1.563V7.75a2.422 2.422 0 0 1 2.413-2.412h2.162c.488 0 1.2-.263 1.575-.588l1.975-1.687Z"/></g>
      </svg>
    {%- when 'location' -%}
      <svg
        width="{{ custom_width | default: 30 }}"
        height="{{ custom_width | default: 30 }}"
        width="28"
        height="40"
        viewBox="0 0 28 40"
        fill="none"
      >
        <path d="M11.8766 31.2582C12.5045 31.755 13.2472 32 13.9966 32C14.7461 32 15.4955 31.755 16.1167 31.2582C19.5398 28.5427 27.5 21.39 27.5 13.6044C27.5 5.8188 21.4505 0 14.0034 0C6.55626 0 0.5 6.10464 0.5 13.6112C0.5 21.1178 8.46024 28.5496 11.8766 31.265V31.2582ZM14.0034 2.15738C20.2689 2.15738 25.3665 7.29562 25.3665 13.6112C25.3665 20.4509 17.9801 27.0455 14.8001 29.5636C14.3275 29.9379 13.6725 29.9379 13.1999 29.5636C10.0266 27.0455 2.63353 20.4441 2.63353 13.6112C2.64029 7.29562 7.73781 2.15738 14.0034 2.15738Z" fill="#111111"/>
        <path d="M14 19C17.031 19 19.5 16.531 19.5 13.5C19.5 10.469 17.031 8 14 8C10.969 8 8.5 10.469 8.5 13.5C8.5 16.531 10.969 19 14 19ZM14 10.008C15.9259 10.008 17.4983 11.5741 17.4983 13.5063C17.4983 15.4386 15.9323 17.0046 14 17.0046C12.0677 17.0046 10.5017 15.4386 10.5017 13.5063C10.5017 11.5741 12.0677 10.008 14 10.008Z" fill="#111111"/>
        <path d="M22.9924 33.0497C22.3668 32.8643 21.7115 33.2129 21.5253 33.836C21.3391 34.4591 21.6892 35.1118 22.3147 35.2973C23.2456 35.5791 23.7222 35.8313 23.968 35.9945C23.2679 36.4618 21.0562 37.2555 16.9156 37.5374C14.8974 37.6709 13.1101 37.6709 11.0919 37.5374C6.9513 37.2555 4.7395 36.4692 4.03947 36.0019C4.27778 35.8387 4.76184 35.5866 5.70018 35.3047C6.32574 35.1192 6.6832 34.4665 6.48957 33.8434C6.29595 33.2203 5.6406 32.8717 5.02249 33.0572C4.15118 33.3168 1.5 34.103 1.5 35.9945C1.5 38.8206 7.40557 39.644 10.9281 39.8887C11.993 39.9629 12.9984 40 13.9963 40C14.9942 40 15.9996 39.9629 17.0719 39.8887C20.5944 39.6514 26.5 38.828 26.5 35.9945C26.5 34.103 23.8637 33.3094 22.9999 33.0572L22.9924 33.0497Z" fill="#111111"/>
      </svg>
    {%- when 'leaf' -%}
      <svg
        width="{{ custom_width | default: 30 }}"
        height="{{ custom_width | default: 30 }}"
        width="42"
        height="40"
        viewBox="0 0 42 40"
        fill="none"
      >
        <path fill-rule="evenodd" clip-rule="evenodd" d="M6.23742 37.6885C6.23742 37.6885 6.18492 37.626 6.16391 37.5948C5.9014 37.3032 5.65988 36.9909 5.46037 36.7826C4.37879 35.6477 3.0242 34.1691 2.22615 32.7322C4.99834 29.9834 8.16955 26.8701 12.3173 26.8181C14.533 26.7868 16.7381 27.2658 18.9433 27.4116C20.4869 27.5157 22.4295 27.3283 23.8471 28.0259C24.6977 28.4424 24.5716 29.5253 23.8891 30.0979C23.2801 30.6081 22.3665 30.6186 21.6 30.6081C20.1824 30.6081 18.7753 30.2437 17.3577 30.275C16.7696 30.2958 16.2971 30.7852 16.3181 31.3682C16.3391 31.9513 16.8326 32.4199 17.4207 32.3991C18.8173 32.3574 20.1929 32.7218 21.5894 32.7322C22.839 32.7322 24.2986 32.5448 25.2857 31.7118C27.1338 30.1396 27.1128 27.245 24.7922 26.11C23.1226 25.2979 20.8754 25.402 19.0798 25.2771C16.8221 25.1313 14.5435 24.6523 12.2753 24.6836C7.4345 24.746 3.65425 28.2862 0.44103 31.4724C0.357024 31.5557 0.29402 31.639 0.241516 31.7431C0.0420028 32.1283 0.0210014 32.17 0.0210014 32.17C0.0210014 32.17 0.178512 33.4715 0 33.0967C0.819055 34.8876 2.53067 36.8243 3.89576 38.2403C4.12678 38.4902 4.4313 38.8963 4.75682 39.2191C5.11384 39.5731 5.50237 39.8334 5.86989 39.9375C6.28992 40.0625 6.70995 40 7.11948 39.7501C7.466 39.5419 7.81252 39.0837 8.06454 38.8442C8.53707 38.3965 8.9571 38.1154 9.61865 38.105C12.9264 38.0529 16.1606 38.6152 19.4683 38.157C23.1016 37.6468 26.6928 35.8976 29.9795 34.367C32.5207 33.18 35.1984 31.9826 37.4035 30.2437C38.6111 29.2962 39.8187 27.953 39.9132 26.3391C40.0287 24.163 37.7605 22.8406 35.8494 23.1842C33.2662 23.6528 30.1685 25.6311 27.7534 26.7348C27.2178 26.9847 26.9763 27.6094 27.2283 28.1509C27.4803 28.6819 28.1104 28.9214 28.6459 28.6715C30.9246 27.6406 33.8123 25.7144 36.2379 25.2771C36.91 25.1521 37.813 25.4645 37.771 26.2246C37.7185 27.2346 36.826 27.9843 36.0699 28.5778C33.9908 30.2229 31.4601 31.3266 29.066 32.4407C25.9682 33.888 22.587 35.5748 19.1638 36.0538C15.9401 36.5015 12.7899 35.9288 9.57664 35.9809C8.31656 36.0017 7.4765 36.4494 6.57344 37.3136C6.48944 37.3865 6.35293 37.5427 6.22692 37.6781L6.23742 37.6885ZM8.56857 0.766615C8.47407 0.912386 8.42157 1.07898 8.41107 1.2664C8.32706 3.05731 8.09604 5.0044 8.49507 6.77449C9.28262 10.2105 12.1598 12.6158 15.583 13.0843C17.2842 16.2184 17.5152 19.769 17.6097 23.2883C17.6202 23.8714 18.1137 24.34 18.7123 24.3191C19.3003 24.3087 19.7728 23.8194 19.7623 23.2259C19.7413 22.3825 19.7098 21.5391 19.6468 20.7061C19.7413 20.6124 19.8253 20.5083 19.8883 20.3833C20.5919 18.8944 21.4319 17.6553 22.3875 16.5932C23.1016 17.3013 23.9206 17.9052 24.7817 18.4466C25.6532 18.9881 26.6298 19.4983 27.6694 19.644C28.6879 19.7898 29.738 19.6649 30.7146 19.3733C34.9148 18.1134 36.931 14.2401 38.6426 10.5854C39.2831 9.23178 39.8397 7.78448 40.9423 6.72243C40.9843 6.69119 41.0158 6.64954 41.0473 6.60789C41.1313 6.57665 41.2258 6.54542 41.3098 6.51418C41.8663 6.30594 42.1393 5.69161 41.9293 5.13976C41.7193 4.58791 41.0893 4.31719 40.5432 4.52544C39.3251 4.99399 38.1386 4.78575 36.868 4.61915C34.0538 4.24431 30.011 3.31762 26.7243 3.84864C24.2251 4.25472 22.1355 5.44172 21.0539 8.06561C20.6234 9.10683 20.3924 10.1793 20.2979 11.2934C20.2454 11.8765 20.6759 12.3971 21.2744 12.4492C21.8625 12.5012 22.3875 12.0743 22.44 11.4808C22.524 10.575 22.7025 9.71074 23.049 8.86735C24.0151 6.51418 26.1993 5.87903 28.5829 5.81656C31.3341 5.74367 34.3688 6.44129 36.595 6.73284C37.1095 6.80572 37.624 6.8682 38.1281 6.89943C37.582 7.78448 37.1515 8.7424 36.721 9.67951C35.2509 12.8032 33.6968 16.2496 30.116 17.3325C29.4335 17.5408 28.7089 17.6345 27.9949 17.5408C27.2598 17.4366 26.5668 17.041 25.9472 16.6453C25.2332 16.1976 24.5506 15.6978 23.9521 15.1147C25.8947 13.4904 28.2259 12.3242 30.8931 11.158C31.4391 10.9186 31.6806 10.2938 31.4391 9.75239C31.1976 9.21095 30.5676 8.97147 30.0215 9.21095C25.4327 11.2101 21.8205 13.2821 19.2373 17.1972C18.8908 15.3646 18.3342 13.5841 17.3997 11.9181C17.7777 10.627 18.0192 9.13807 17.9037 7.73241C17.7672 6.00398 17.1161 4.40049 15.6356 3.31762C14.722 2.64082 13.7034 2.21392 12.6849 1.74537C11.8553 1.37053 11.0362 0.97486 10.3537 0.308475C9.93367 -0.108015 9.25112 -0.0976025 8.83109 0.308475C8.70508 0.443835 8.61058 0.600019 8.55807 0.766615H8.56857ZM10.4587 3.00525C10.3852 4.11936 10.3537 5.24389 10.5952 6.30594C11.1412 8.71117 13.0944 10.4188 15.4465 10.8977C15.6881 9.93981 15.8456 8.88818 15.7616 7.8886C15.6776 6.77449 15.2995 5.71244 14.3545 5.01482C13.5669 4.43173 12.6744 4.07771 11.7923 3.68205C11.3408 3.4738 10.8892 3.25514 10.4587 3.01566V3.00525Z" fill="#111111"/>
      </svg>
    {%- when 'blossom' -%}
      <svg
        width="{{ custom_width | default: 30 }}"
        height="{{ custom_width | default: 30 }}"
        width="40"
        height="40"
        viewBox="0 0 40 40"
        fill="none"
      >
        <path d="M19.0158 1.59304V0.973913C19.0158 0.438261 19.4567 0 20.0035 0C20.5503 0 20.9913 0.434783 20.9913 0.973913V1.59304C20.9913 2.1287 20.5503 2.56696 20.0035 2.56696C19.4603 2.56696 19.0158 2.13217 19.0158 1.59304ZM9.04273 4.74783C9.22618 5.06087 9.55779 5.23478 9.89645 5.23478C10.0623 5.23478 10.2316 5.19304 10.3868 5.10609C10.8595 4.83826 11.0218 4.24348 10.7466 3.77739L10.4327 3.24174C10.161 2.77565 9.55779 2.61565 9.08507 2.88696C8.61234 3.15478 8.45007 3.74957 8.72523 4.21565L9.03921 4.7513L9.04273 4.74783ZM6.78496 14.8765C7.29296 15.0713 7.86446 14.8209 8.05848 14.32C9.94937 9.49217 14.7471 6.24696 20 6.24696C25.2529 6.24696 30.0506 9.49217 31.9415 14.32C32.0932 14.7061 32.4672 14.9426 32.8623 14.9426C32.9787 14.9426 33.1022 14.9217 33.2186 14.8765C33.7266 14.6817 33.9806 14.1217 33.783 13.6209C32.7212 10.9078 30.8761 8.5913 28.449 6.91826C25.9655 5.20696 23.0445 4.30261 20.0035 4.30261C16.9626 4.30261 14.0416 5.20696 11.558 6.91826C9.13093 8.5913 7.2859 10.9078 6.22404 13.6209C6.02649 14.1217 6.28049 14.6852 6.78849 14.8765H6.78496ZM37.5013 12.5287C37.6671 12.5287 37.84 12.487 37.9952 12.3965L38.5385 12.087C39.0112 11.8191 39.1699 11.2243 38.8983 10.7583C38.6267 10.2922 38.0234 10.1357 37.5507 10.4035L37.0074 10.713C36.5347 10.9809 36.3759 11.5757 36.6476 12.0417C36.831 12.3548 37.1626 12.5287 37.5013 12.5287ZM29.6097 5.10609C29.7649 5.19304 29.9342 5.23826 30.1035 5.23826C30.4457 5.23826 30.7774 5.06435 30.9573 4.7513L31.2712 4.21565C31.5429 3.74957 31.3806 3.15478 30.9114 2.88696C30.4387 2.61913 29.8354 2.77913 29.5638 3.24174L29.2498 3.77739C28.9782 4.24348 29.1405 4.83826 29.6097 5.10609ZM18.2538 23.7739C18.6771 23.4365 18.7476 22.8278 18.4054 22.407C17.9398 21.833 17.4283 21.2765 16.8885 20.7548C12.313 16.3304 6.04765 14.6991 1.29927 16.6957C1.05938 16.7965 0.86888 16.9878 0.770102 17.2278C-1.15959 21.9478 0.618408 28.0939 5.19393 32.5183C7.94207 35.1757 11.3464 36.8765 14.7824 37.3078C14.8248 37.3148 14.8671 37.3148 14.9094 37.3148C15.3998 37.3148 15.8231 36.9565 15.8866 36.4661C15.9536 35.9339 15.5726 35.447 15.0329 35.3774C12.0202 34.9983 9.01804 33.4922 6.57682 31.1304C2.68921 27.3739 1.06996 22.2887 2.45638 18.3374C6.43571 16.8904 11.6215 18.3896 15.5091 22.1461C15.9924 22.6157 16.4511 23.113 16.8673 23.6278C17.2095 24.0452 17.8269 24.1148 18.2538 23.7774V23.7739ZM34.8096 32.5148C30.4845 36.6957 24.6531 38.3826 20.0141 36.8696C19.6225 37.6765 19.2698 38.5078 18.9593 39.3565C18.8147 39.753 18.4337 40 18.0315 40C17.9221 40 17.8093 39.9826 17.6964 39.9409C17.1848 39.76 16.9167 39.2 17.1037 38.6957C17.5023 37.6035 17.968 36.5391 18.4901 35.513C16.8956 30.8696 18.7053 25.0122 23.1115 20.7513C27.687 16.327 33.9523 14.6957 38.7007 16.6922C38.9406 16.793 39.1311 16.9843 39.2299 17.2243C41.1596 21.9478 39.3816 28.0904 34.8061 32.5148H34.8096ZM37.5471 18.3339C33.5678 16.887 28.382 18.3861 24.4944 22.1426C21.2206 25.3078 19.5555 29.4122 19.94 32.9913C21.1677 31.0817 22.6106 29.3252 24.2404 27.753L25.0094 24.2991C25.1259 23.7739 25.6515 23.4435 26.1842 23.5583C26.7169 23.673 27.052 24.1913 26.9356 24.7165L26.7381 25.6C27.9692 24.6539 29.278 23.8017 30.6574 23.0574L31.2254 20.4974C31.3418 19.9722 31.8674 19.6417 32.4001 19.7565C32.9328 19.8713 33.268 20.3896 33.1515 20.9148L32.9187 21.9548C33.2644 21.8052 33.6172 21.6591 33.97 21.52C34.478 21.3217 35.0495 21.5652 35.2506 22.0661C35.4517 22.567 35.2047 23.1304 34.6967 23.3287C33.776 23.6904 32.8799 24.0974 32.0121 24.5461C31.9909 24.5565 31.9697 24.5704 31.9486 24.5809C31.0243 25.0644 30.1318 25.6 29.2745 26.1809L31.303 26.1739C31.8463 26.1739 32.2872 26.6087 32.2908 27.1443C32.2908 27.68 31.8533 28.1183 31.3065 28.1217L26.7204 28.1391C26.4347 28.3861 26.1524 28.64 25.8773 28.8974C25.8349 28.9461 25.7891 28.9913 25.7362 29.0296C25.0941 29.6417 24.4803 30.2852 23.8982 30.9565L26.1313 30.9252C26.1313 30.9252 26.1419 30.9252 26.1454 30.9252C26.6851 30.9252 27.1226 31.353 27.1296 31.8852C27.1367 32.4209 26.7028 32.8626 26.156 32.8696L22.3495 32.9217C21.8415 33.6313 21.3652 34.3687 20.9278 35.127C24.8225 36.2435 29.7084 34.7165 33.4126 31.1339C37.3002 27.3774 38.9195 22.2922 37.533 18.3409L37.5471 18.3339ZM1.46507 12.087L2.00835 12.3965C2.16357 12.4835 2.33291 12.5287 2.50224 12.5287C2.84443 12.5287 3.17604 12.3548 3.35596 12.0417C3.6276 11.5757 3.46532 10.9809 2.99613 10.713L2.45285 10.4035C1.98013 10.1357 1.37688 10.2957 1.10524 10.7583C0.833602 11.2209 0.995879 11.8191 1.46507 12.087ZM20 9.53043C16.9449 9.53043 14.1651 11.047 12.7434 13.4887C12.4717 13.9548 12.6375 14.5496 13.1103 14.8174C13.583 15.0852 14.1862 14.9217 14.4579 14.4557C15.5268 12.6191 17.6505 11.4748 20 11.4748C22.3495 11.4748 24.4767 12.6157 25.5421 14.4557C25.7256 14.7687 26.0572 14.9461 26.3994 14.9461C26.5652 14.9461 26.7345 14.9043 26.8897 14.8174C27.3625 14.5496 27.5247 13.9548 27.2566 13.4887C25.8385 11.047 23.0586 9.53043 20.0035 9.53043H20ZM9.98818 27.4539C9.4449 27.4539 9.00393 27.8852 9.0004 28.4209C9.0004 28.9565 9.43784 29.3948 9.98112 29.3983L13.3008 29.4122C13.6782 29.8365 14.0416 30.2817 14.3838 30.7374C14.5778 30.9948 14.8741 31.1304 15.1775 31.1304C15.3821 31.1304 15.5868 31.0678 15.7631 30.9391C16.2006 30.6191 16.2923 30.0104 15.9713 29.5791C14.3485 27.4122 12.3553 25.5687 10.094 24.1252L9.37787 20.9043C9.26145 20.3791 8.73582 20.0487 8.20312 20.1635C7.67043 20.2783 7.33529 20.7965 7.45171 21.3217L7.79037 22.8383C7.26473 22.5843 6.72499 22.3478 6.17818 22.1357C5.67371 21.9374 5.09868 22.1809 4.8976 22.6817C4.69652 23.1826 4.94346 23.7461 5.45146 23.9443C6.57329 24.3826 7.64573 24.9148 8.66173 25.5339C8.68643 25.5513 8.71112 25.5652 8.73582 25.5791C9.64246 26.1357 10.4997 26.7617 11.3076 27.4539L9.98818 27.447C9.98818 27.447 9.98818 27.447 9.98465 27.447L9.98818 27.4539Z" fill="#111111"/>
      </svg>
    {%- when 'payment' -%}
      <svg
        width="{{ custom_width | default: 30 }}"
        height="{{ custom_height | default: 30 }}"
        fill="none"
      >
        <g stroke="#111" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"><path stroke-miterlimit="10" d="M4.912 19.85 19.85 4.911M13.877 22.849l1.5-1.5M17.242 19.486l2.988-2.988"/><path d="m4.502 12.799 8.3-8.3c2.65-2.65 3.975-2.663 6.6-.038l6.137 6.138c2.625 2.625 2.613 3.95-.037 6.6l-8.3 8.3c-2.65 2.65-3.975 2.662-6.6.037L4.464 19.4c-2.625-2.625-2.625-3.938.038-6.6ZM2.5 27.498h25"/></g>
      </svg>
    {%- when 'discount' -%}
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="{{ custom_width | default: 30 }}"
        height="{{ custom_height | default: 30 }}"
        viewBox="0 0 20 20"
        fill="none"
      >
        <path d="M3.32408 12.2187L2.05742 10.952C1.54076 10.4353 1.54076 9.5853 2.05742 9.06863L3.32408 7.80195C3.54075 7.58528 3.71575 7.16028 3.71575 6.86028V5.06859C3.71575 4.33526 4.31575 3.73528 5.04909 3.73528H6.84075C7.14075 3.73528 7.56575 3.5603 7.78242 3.34364L9.04908 2.07695C9.56574 1.56029 10.4158 1.56029 10.9324 2.07695L12.1991 3.34364C12.4158 3.5603 12.8407 3.73528 13.1407 3.73528H14.9324C15.6658 3.73528 16.2657 4.33526 16.2657 5.06859V6.86028C16.2657 7.16028 16.4407 7.58528 16.6574 7.80195L17.9241 9.06863C18.4408 9.5853 18.4408 10.4353 17.9241 10.952L16.6574 12.2187C16.4407 12.4353 16.2657 12.8603 16.2657 13.1603V14.9519C16.2657 15.6852 15.6658 16.2853 14.9324 16.2853H13.1407C12.8407 16.2853 12.4158 16.4603 12.1991 16.677L10.9324 17.9437C10.4158 18.4603 9.56574 18.4603 9.04908 17.9437L7.78242 16.677C7.56575 16.4603 7.14075 16.2853 6.84075 16.2853H5.04909C4.31575 16.2853 3.71575 15.6852 3.71575 14.9519V13.1603C3.71575 12.852 3.54075 12.427 3.32408 12.2187Z" stroke="#111111" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M7.5 12.502L12.5 7.50195" stroke="#111111" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M12.0788 12.0856H12.0862" stroke="#111111" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M7.91209 7.91862H7.91957" stroke="#111111" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    {%- when 'phone' -%}
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 28 28"
        fill="none"
        width="{{ custom_width | default: 30 }}"
        height="{{ custom_height | default: 30 }}"
      >
        <path d="M25.0039 20.963C25.0039 21.359 24.9158 21.766 24.7285 22.162C24.5412 22.558 24.2989 22.932 23.9794 23.284C23.4396 23.878 22.8447 24.307 22.1727 24.582C21.5117 24.857 20.7956 25 20.0244 25C18.9008 25 17.7 24.736 16.4331 24.197C15.1662 23.658 13.8993 22.932 12.6434 22.019C11.3765 21.095 10.1757 20.072 9.02995 18.939C7.89524 17.795 6.87071 16.596 5.95634 15.342C5.05298 14.088 4.32589 12.834 3.7971 11.591C3.2683 10.337 3.00391 9.138 3.00391 7.994C3.00391 7.246 3.1361 6.531 3.4005 5.871C3.6649 5.2 4.08353 4.584 4.6674 4.034C5.37246 3.341 6.14362 3 6.95884 3C7.2673 3 7.57577 3.066 7.85118 3.198C8.13761 3.33 8.39099 3.528 8.58929 3.814L11.1451 7.411C11.3434 7.686 11.4866 7.939 11.5858 8.181C11.6849 8.412 11.74 8.643 11.74 8.852C11.74 9.116 11.6629 9.38 11.5087 9.633C11.3655 9.886 11.1561 10.15 10.8917 10.414L10.0545 11.283C9.9333 11.404 9.87822 11.547 9.87822 11.723C9.87822 11.811 9.88924 11.888 9.91127 11.976C9.94432 12.064 9.97737 12.13 9.9994 12.196C10.1977 12.559 10.5392 13.032 11.0239 13.604C11.5197 14.176 12.0485 14.759 12.6213 15.342C13.2162 15.925 13.7891 16.464 14.373 16.959C14.9458 17.443 15.4195 17.773 15.7941 17.971C15.8492 17.993 15.9153 18.026 15.9924 18.059C16.0805 18.092 16.1687 18.103 16.2678 18.103C16.4551 18.103 16.5983 18.037 16.7195 17.916L17.5567 17.091C17.8322 16.816 18.0966 16.607 18.3499 16.475C18.6033 16.321 18.8567 16.244 19.1321 16.244C19.3414 16.244 19.5617 16.288 19.8041 16.387C20.0465 16.486 20.2999 16.629 20.5753 16.816L24.2217 19.401C24.5082 19.599 24.7065 19.83 24.8276 20.105C24.9378 20.38 25.0039 20.655 25.0039 20.963Z" stroke="#111111" stroke-width="2" stroke-miterlimit="10"/>
        <path d="M21.0039 11C21.0039 10.3143 20.4668 9.26286 19.6668 8.40571C18.9353 7.61714 17.9639 7 17.0039 7" stroke="#D0473E" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M25.0039 11C25.0039 6.57714 21.4268 3 17.0039 3" stroke="#D0473E" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    {% when 'email' %}
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="{{ custom_width | default: 30 }}"
        height="{{ custom_height | default: 30 }}"
        viewBox="0 0 28 28"
        fill="none"
      >
        <path d="M26.0039 12.2353V18.1176C26.0039 22.2353 23.6039 24 20.0039 24H8.00391C4.40391 24 2.00391 22.2353 2.00391 18.1176V9.88235C2.00391 5.76471 4.40391 4 8.00391 4H16.4039" stroke="#111111" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M8 10L11.8834 13.2103C13.1613 14.2632 15.2581 14.2632 16.536 13.2103L18 12.0032" stroke="#111111" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M23 9C24.6569 9 26 7.65685 26 6C26 4.34315 24.6569 3 23 3C21.3431 3 20 4.34315 20 6C20 7.65685 21.3431 9 23 9Z" stroke="#D0473E" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    {%- when 'chat' -%}
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="{{ custom_width | default: 30 }}"
        height="{{ custom_height | default: 30 }}"
        viewBox="0 0 28 28"
        fill="none"
      >
        <path d="M25.6654 11.6673V15.1673C25.6654 19.834 23.332 22.1673 18.6654 22.1673H18.082C17.7204 22.1673 17.3704 22.3423 17.1487 22.634L15.3987 24.9673C14.6287 25.994 13.3687 25.994 12.5987 24.9673L10.8487 22.634C10.662 22.3773 10.2304 22.1673 9.91537 22.1673H9.33203C4.66536 22.1673 2.33203 21.0006 2.33203 15.1673V9.33398C2.33203 4.66732 4.66536 2.33398 9.33203 2.33398H16.332" stroke="#111111" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M22.7487 8.16732C24.3595 8.16732 25.6654 6.86148 25.6654 5.25065C25.6654 3.63982 24.3595 2.33398 22.7487 2.33398C21.1379 2.33398 19.832 3.63982 19.832 5.25065C19.832 6.86148 21.1379 8.16732 22.7487 8.16732Z" stroke="#D0473E" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M18.6629 12.8333H18.6733" stroke="#111111" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M13.9949 12.8333H14.0054" stroke="#111111" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M9.32693 12.8333H9.3374" stroke="#111111" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    {% when 'bag' %}
      <svg
        width="{{ custom_width | default: 50 }}"
        height="{{ custom_height | default: 50 }}"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <mask id="a" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="0" y="0" width="50" height="50"><path d="M0 0h50v50H0V0z" fill="#fff"/></mask><g mask="url(#a)" fill-rule="evenodd" clip-rule="evenodd" fill="#111"><path d="M8.129 45.864A4.136 4.136 0 0 0 12.265 50h25.474a4.136 4.136 0 0 0 4.136-4.136V22.63c0-9.319-7.554-16.873-16.873-16.873S8.13 13.31 8.13 22.629v23.235zm4.136 2.183a2.183 2.183 0 0 1-2.183-2.183V22.63c0-8.24 6.68-14.92 14.92-14.92s14.92 6.68 14.92 14.92v23.235a2.183 2.183 0 0 1-2.183 2.183H12.265z"/><path d="M13.402 23.605c.54 0 .977-.437.977-.976 0-5.857 4.766-10.623 10.623-10.623 5.857 0 10.623 4.766 10.623 10.623a.977.977 0 0 0 1.953 0c0-6.936-5.64-12.576-12.576-12.576-6.935 0-12.576 5.64-12.576 12.576 0 .54.437.976.976.976zM39.922 42.557c0 .539.437.976.976.976h2.032a3.538 3.538 0 0 0 3.538-3.538V29.953a3.538 3.538 0 0 0-3.538-3.539h-2.032a.977.977 0 0 0-.976.977v15.166zm1.953-.977V28.367h1.055c.875 0 1.585.71 1.585 1.585v10.043c0 .875-.71 1.585-1.585 1.585h-1.055zM3.535 39.995a3.538 3.538 0 0 0 3.538 3.538h2.032c.54 0 .976-.437.976-.976V27.39a.976.976 0 0 0-.976-.977H7.073a3.538 3.538 0 0 0-3.538 3.538v10.043zm3.538 1.585c-.875 0-1.585-.71-1.585-1.585V29.953c0-.876.71-1.586 1.585-1.586h1.055V41.58H7.073zM20.574 8.167c.54 0 .977-.437.977-.977V5.41A3.454 3.454 0 0 1 25 1.953a3.454 3.454 0 0 1 3.452 3.457v1.78a.977.977 0 0 0 1.953 0V5.41A5.407 5.407 0 0 0 25.002 0a5.407 5.407 0 0 0-5.404 5.41v1.78c0 .54.437.977.976.977zM12.426 44.218c0 .54.437.976.976.976h23.2c.54 0 .976-.437.976-.976v-11.3a4.338 4.338 0 0 0-4.338-4.338H16.764a4.338 4.338 0 0 0-4.338 4.339v11.299zm1.953-.977V32.92a2.385 2.385 0 0 1 2.385-2.386H33.24a2.385 2.385 0 0 1 2.385 2.386V43.24H14.38z"/></g>
      </svg>
    {% when 'support' %}
      <svg
        width="{{ custom_width | default: 50 }}"
        height="{{ custom_height | default: 50 }}"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path d="M32.172 20.29H30.59a.977.977 0 0 1 0-1.953h1.582a1.538 1.538 0 1 0 0-3.075H30.59a.977.977 0 1 1 0-1.953h1.582a3.491 3.491 0 1 1 0 6.982zM8.335 50a.977.977 0 0 1-.888-.569c-2.174-4.736-1.04-7.565-.293-9.436l.053-.133c.138-.345.15-.793.15-1.3v-3.66a.977.977 0 0 1 1.953 0v3.66c0 .683-.023 1.356-.293 2.024l-.048.137c-1.574 3.919-.56 6.122.253 7.892A.977.977 0 0 1 8.335 50z" fill="#111"/><path d="M38.071 50H1.926a.977.977 0 0 1-.977-.976v-6.656c0-2.312.73-4.243 2.173-5.738 1.319-1.367 3.178-2.31 5.528-2.8l5.925-1.242v-3.274a.977.977 0 0 1 1.953 0v4.067a.976.976 0 0 1-.782.956L9.05 35.742c-2.805.586-6.147 2.21-6.147 6.626v5.68h34.192v-5.68c0-4.415-3.341-6.04-6.146-6.626l-6.702-1.405a.976.976 0 0 1-.776-.956v-4.067a.977.977 0 0 1 1.953 0v3.274l5.925 1.242c2.344.489 4.207 1.433 5.526 2.8 1.442 1.495 2.174 3.426 2.174 5.738v6.656a.977.977 0 0 1-.977.976zM6.387 31.632a.976.976 0 0 1-.969-.847c-.13-.966-.283-1.952-.432-2.904-.597-3.826-1.214-7.783-.63-12.227.373-2.828 1.248-5.413 2.601-7.683a16.575 16.575 0 0 1 5.079-5.383C15.878.038 20.578-.656 25.266.633c1.79.488 2.93 1.35 3.398 2.549.526 1.36.15 3.02-1.15 5.078-3.261 5.157-9.794 11.794-14.563 16.643-2.627 2.662-5.337 5.419-5.676 6.16a.977.977 0 0 1-.888.57zm14.29-29.679a13.574 13.574 0 0 0-7.565 2.26C9.379 6.69 6.959 10.844 6.29 15.908c-.548 4.166.047 7.981.623 11.672l.104.666c1.012-1.126 2.51-2.657 4.539-4.717C16.263 18.743 22.711 12.2 25.859 7.213c.92-1.455 1.259-2.606.977-3.328-.228-.586-.933-1.051-2.094-1.367a15.308 15.308 0 0 0-4.066-.565zM33.468 26.706H22.082a.976.976 0 1 1 0-1.953h11.386a.251.251 0 0 0 .245-.245v-7.711a.977.977 0 0 1 1.953 0v7.715a2.2 2.2 0 0 1-2.198 2.194z" fill="#111"/><path d="M31.661 50a.977.977 0 0 1-.887-1.385c1.828-3.973.956-6.146.256-7.893l-.053-.135c-.269-.673-.293-1.367-.293-2.024v-3.66a.977.977 0 0 1 1.953 0v3.659c0 .488.011.949.151 1.3l.054.132c.749 1.872 1.883 4.7-.293 9.437a.978.978 0 0 1-.888.57zM19.998 31.275a11.54 11.54 0 0 1-9.738-5.34.978.978 0 0 1 1.644-1.056 9.608 9.608 0 0 0 17.711-5.172v-5.58a9.532 9.532 0 0 0-2.935-6.9.976.976 0 0 1 1.358-1.404 11.469 11.469 0 0 1 3.53 8.301v5.583a11.582 11.582 0 0 1-11.57 11.568zM19.996 43.933a.976.976 0 0 1-.879-.556c-.39-.827-1.274-1.43-2.353-1.616-1.21-.208-2.397.133-3.175.911a.976.976 0 0 1-1.667-.69V33.94a.977.977 0 0 1 1.953 0v6.24a5.805 5.805 0 0 1 3.223-.344 5.38 5.38 0 0 1 2.904 1.465 5.396 5.396 0 0 1 2.911-1.465 5.809 5.809 0 0 1 3.213.345v-6.24a.976.976 0 1 1 1.953 0v8.042a.976.976 0 0 1-1.668.69c-.78-.782-1.96-1.118-3.169-.912-1.082.185-1.963.79-2.359 1.618a.978.978 0 0 1-.887.554z" fill="#111"/>
      </svg>
    {% when 'tree' %}
      <svg
        width="{{ custom_width | default: 50 }}"
        height="{{ custom_height | default: 50 }}"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <mask id="a" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="0" y="0" width="50" height="50"><path d="M0 0h50v50H0V0z" fill="#fff"/></mask><g mask="url(#a)" fill="#111"><path fill-rule="evenodd" clip-rule="evenodd" d="M32.617 38.672c-3.556 0-6.64-2.891-6.64-6.348 0-3.02 2.423-5.626 5.382-6.22a.977.977 0 0 0 .775-1.107 1.911 1.911 0 0 0-.005-.04 4.882 4.882 0 0 1 9.765 0 1.911 1.911 0 0 0-.014.17c-.01.473.321.884.784.977 2.96.594 5.383 3.2 5.383 6.22 0 3.457-3.085 6.348-6.64 6.348-1.427 0-2.735-.49-3.806-1.3a.977.977 0 0 0-1.178 0c-1.072.81-2.38 1.3-3.806 1.3zm-8.594-6.348c0 4.633 4.06 8.301 8.594 8.301a8.177 8.177 0 0 0 4.395-1.287 8.177 8.177 0 0 0 4.394 1.287c4.534 0 8.594-3.668 8.594-8.3 0-3.796-2.78-6.921-6.18-7.943a6.832 6.832 0 0 0-6.808-6.218 6.836 6.836 0 0 0-6.809 6.218c-3.399 1.022-6.18 4.147-6.18 7.942z"/><path fill-rule="evenodd" clip-rule="evenodd" d="M8.594 38.672c-3.556 0-6.64-2.891-6.64-6.348 0-3.02 2.422-5.626 5.382-6.22a.976.976 0 0 0 .775-1.107 1.916 1.916 0 0 0-.005-.04 4.885 4.885 0 0 1 4.882-4.84 4.882 4.882 0 0 1 4.883 4.84 1.911 1.911 0 0 0-.014.17c-.01.473.32.884.784.977 2.96.594 5.382 3.2 5.382 6.22 0 3.457-3.084 6.348-6.64 6.348-1.426 0-2.734-.49-3.806-1.3a.977.977 0 0 0-1.178 0c-1.071.81-2.38 1.3-3.805 1.3zM0 32.324c0 4.633 4.06 8.301 8.594 8.301a8.177 8.177 0 0 0 4.394-1.287 8.177 8.177 0 0 0 4.395 1.287c4.534 0 8.594-3.668 8.594-8.3 0-3.796-2.781-6.921-6.18-7.943a6.836 6.836 0 0 0-9.177-5.796 6.84 6.84 0 0 0-4.44 5.796C2.78 25.404 0 28.53 0 32.324zM25.882 16.045a.977.977 0 0 0-.462-1.302l-6.152-2.93a.977.977 0 1 0-.84 1.764l6.152 2.93a.976.976 0 0 0 1.302-.462z"/><path fill-rule="evenodd" clip-rule="evenodd" d="M25 50c.54 0 .977-.437.977-.977V9.766a.977.977 0 0 0-1.954 0v39.257c0 .54.438.977.977.977zM12.988 50c.54 0 .977-.437.977-.977V27.93a.977.977 0 1 0-1.953 0v21.093c0 .54.437.977.976.977z"/><path fill-rule="evenodd" clip-rule="evenodd" d="M13.862 34.226a.977.977 0 0 0-.437-1.31l-5.86-2.93a.977.977 0 1 0-.873 1.747l5.86 2.93a.977.977 0 0 0 1.31-.437zM37.012 50c.54 0 .976-.437.976-.977V25a.977.977 0 0 0-1.953 0v24.023c0 .54.437.977.977.977z"/><path fill-rule="evenodd" clip-rule="evenodd" d="M36.139 34.226a.977.977 0 0 0 1.31.436l5.859-2.93a.977.977 0 1 0-.873-1.746l-5.86 2.93a.977.977 0 0 0-.437 1.31zM24.119 18.974a.977.977 0 0 0 1.301.462l6.152-2.93a.977.977 0 1 0-.84-1.763l-6.152 2.93a.976.976 0 0 0-.461 1.301zM37.702 31.55a.977.977 0 0 0 0-1.381l-2.93-2.93a.977.977 0 1 0-1.38 1.381l2.93 2.93a.977.977 0 0 0 1.38 0zM3.223 49.023c0 .54.437.977.976.977h41.602a.977.977 0 0 0 0-1.953H4.199a.977.977 0 0 0-.976.976zM12.298 37.41a.977.977 0 0 0 1.38 0l2.93-2.93a.977.977 0 1 0-1.38-1.381l-2.93 2.93a.977.977 0 0 0 0 1.38z"/><path d="M37.5 10.645a.977.977 0 1 1 .001-1.955.977.977 0 0 1-.001 1.955z"/><path fill-rule="evenodd" clip-rule="evenodd" d="M38.611 20.378a.977.977 0 0 0 1.308-.445c.641-1.3 1-2.764 1-4.308 0-.82-.101-1.618-.293-2.38a.977.977 0 0 0-1.894.477c.153.607.234 1.244.234 1.903a7.768 7.768 0 0 1-.799 3.445.977.977 0 0 0 .444 1.308zM11.39 20.378a.976.976 0 0 0 .444-1.308 7.768 7.768 0 0 1-.799-3.445c0-3.903 2.875-7.115 6.63-7.69a.976.976 0 0 0 .812-.788c.536-2.91 3.37-5.194 6.523-5.194 3.153 0 5.987 2.284 6.523 ************.402.726.813.789.405.062.8.154 1.181.275a.977.977 0 1 0 .59-1.862 9.772 9.772 0 0 0-.817-.22C32.297 2.565 28.793 0 25 0c-3.793 0-7.297 2.566-8.29 6.13-4.357.975-7.628 4.834-7.628 9.496a9.72 9.72 0 0 0 1 4.308.977.977 0 0 0 1.308.445z"/></g>
      </svg>
  {%- endcase -%}
{%- endif -%}
