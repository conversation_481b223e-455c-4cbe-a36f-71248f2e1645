.breadcrumb-truncate{
  display: -webkit-box;
  line-clamp: 1;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}
.product-next-prev {
  --product-item__price-top: 0.8rem;
}
.nav-back .tooltip-content {
  z-index: 20;
}
.hover-event .product-item {
  min-width: 28rem;
  max-width: 28rem;
  top: calc(100% + 1.5rem);
}
.hover-event:hover .product-item {
  opacity: 1;
  visibility: visible;
  top: 100%;
}
.copied .active-hide {
  display: none;
}
.copied .active-show {
  display: block;
}
.compare__items .compare__items-inner {
  margin-top: 3rem;
}
.compare__items img {
  border-radius: var(--rounded-radius);
}
.compare__items span {
  display: block;
  margin-top: 1rem;
  color: var(--color-heading);
  font-weight: var(--heading-weight);
}

.progressbar-stock {
  width: var(--percent, 100%);
  background-color: var(--color-primary);
  transition: width .6s cubic-bezier(.7,0,.3,1) .1s;
}

/* Media gallery */
.thumbnail-slide :where(.swiper-actions:not(.show-arrow) .swiper-arrow),
.thumbnail-slide
  :is(.swiper-actions:not(.show-arrow) .swiper-arrow.swiper-button-disabled) {
  --swiper-width: 3.5rem;
  --swiper-size: 1rem;
  --swiper-navigation-sides-offset: 15px;
}
.thumbnail-slide.swiper:hover
  .swiper-actions:not(.show-arrow)
  :is(.swiper-button-next, .swiper-button-prev) {
  --swiper-navigation-sides-offset: 5px;
}

.thumb-bottom.swiper:not([class*="initialized"]) .swiper-wrapper .swiper-slide {
  width: calc((100% - (var(--gap) * 3)) / 4);
}
.thumbnail-slide [class*="media-gallery__"]:after {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  pointer-events: none;
  border: 1px solid transparent;
  border-radius: inherit;
  overflow: hidden;
}
.thumbnail-slide [class*="media-gallery__"].swiper-slide-thumb-active:after {
  border-color: 1px solid var(--color-heading);
}
[data-type="open_lightbox"] .media-main-swiper .media-gallery__image {
  cursor: zoom-in;
}
@media (min-width: 768px) {
  .stacked {
    grid-template-columns: repeat(2, 1fr);
  }
  .stacked > *:nth-child(3n + 1) {
    grid-column: 1 / 3;
  }
  .thumbnail_left .media-main-swiper {
    width: calc(100% - 6rem);
  }
  .thumbnail_left .media-thumb-swiper,
  .thumbnail_left .swiper-vertical {
    height: 100%;
  }
  .thumbnail_left .media-thumb-swiper {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    width: 5rem;
    order: -1;
    margin-top: 0;
    --swiper-navigation-sides-offset: 0;
    --arrows-offset-top: 1.5rem;
  }
  .thumbnail_left .swiper-slide {
    height: auto !important;
  }
}
/* Custom upload */
product-property + product-property {
  margin-top: -1rem;
}
input#custom-image-upload {
  font-family: var(--base-font-family);
}
input#custom-image-upload::-webkit-file-upload-button {
  border: none;
  margin: 0;
  padding: 0;
  -webkit-appearance: none;
  width: 0;
}
.btn-upload-cloud {
  height: var(--input-height);
  align-items: center;
}
.btn-upload-cloud .custom-file-upload {
  border-radius: 0 var(--btn-radius) var(--btn-radius) 0;
}
.btn-upload-cloud:focus {
  filter: brightness(95%);
}
/* Product meta */
.product-detail__meta-label {
  min-width: clamp(9rem, 5vw, 12rem);
}
.product-detail__meta-value a {
  text-decoration: none;
}
.product-detail__meta-value a:not(:hover) {
  color: var(--color-heading);
}
.drift-zoom-pane {
  /* background: rgba(0, 0, 0, 0.5); */
  /* This is required because of a bug that causes border-radius to not
  work with child elements in certain cases. */
  background: rgba(0, 0, 0, 0.5);
  transform: translate3d(0, 0, 0);
  -webkit-transform: translate3d(0, 0, 0);
  z-index: 3;
  height: 550px;
}
.drift-zoom-pane.drift-opening {
  animation: drift-fadeZoomIn 180ms ease-out;
  -webkit-animation: drift-fadeZoomIn 180ms ease-out;
}
.drift-zoom-pane.drift-closing {
  animation: drift-fadeZoomOut 210ms ease-in;
  -webkit-animation: drift-fadeZoomOut 210ms ease-in;
}
.drift-zoom-pane.drift-inline {
  position: absolute;
  width: 150px;
  height: 150px;
  border-radius: 75px;
  box-shadow: 0 6px 18px rgba(0, 0, 0, 0.3);
}
.drift-loading .drift-zoom-pane-loader {
  display: block;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  width: 66px;
  height: 20px;
  animation: drift-loader-rotate 1800ms infinite linear;
  -webkit-animation: drift-loader-rotate 1800ms infinite linear;
}
.drift-zoom-pane-loader:before,
.drift-zoom-pane-loader:after {
  content: "";
  display: block;
  width: 20px;
  height: 20px;
  position: absolute;
  top: 50%;
  margin-top: -10px;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.9);
}
.drift-bounding-box {
  background-color: rgba(0, 0, 0, 0.4);
  box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.4) inset;
  z-index: 6;
  width: 150px !important;
  height: 150px !important;
}
sticky-add-cart {
  transform: translateY(100%);
}
.show-sticky-cart {
  transform: translateY(0);
  opacity: 1;
  visibility: visible;
}
.sticky__item-info .product-detail__variant-picker select{
  width: 100%;
}
sticky-add-cart .product-item__wrapper {
  --col-width: 6.7rem;
}
sticky-add-cart .product-item__media--ratio {
  height: 6.7rem;
  border-radius: 50%;
}
.pswp .pswp__top-bar {
  top: clamp(2rem, 3vw, 4rem);
  right: clamp(2rem, 3vw, 4rem);
  left: auto;
}
button.pswp__button.pswp__button--bls--close {
  width: 50px;
  height: 50px;
  border: 1px solid var(--border-color-base);
  border-radius: 50%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: var(--duration-short);
}
button.pswp__button {
  color: var(--color-heading);
}
button.pswp__button.pswp__button--bls--close:hover {
  border-color: var(--color-heading);
}
.pswp__bottom-bar {
  position: absolute;
  bottom: clamp(1.5rem, 2vw, 3rem);
  left: 50%;
  transform: translate(-50%);
  display: inline-flex;
  flex-direction: row-reverse;
  align-items: center;
  border-radius: 30px;
  background-color: var(--color-white);
  transition: none;
  border: 1px solid var(--border-color-base);
}
.pswp__bottom-bar .pswp__button {
  height: 45px;
}
body .pswp__counter {
  height: 30px;
  font-size: var(--body-font-size, 14px);
  line-height: 30px;
  color: var(--color-heading);
  margin: 0 1rem;
  text-shadow: unset;
  font-weight: var(--heading-weight);
}
.product-detail__information collapsible-block:first-of-type h3 {
  border-top: 1px solid var(--border-color-base);
}
.product-detail__information .product__badges {
  flex-direction: row;
}
.product-detail__information .product__badges > div {
  margin-bottom: 1rem;
}
.trust-badge {
  margin-bottom: 3.6rem;
}
.product_description :is(p, ul) {
  margin-block-start: 1rem;
  margin-block-end: 1rem;
}
.product_description p:first-of-type {
  margin-top: 0;
}
.product_description ul {
  padding-inline-start: 1.7rem;
}
.product_description img{
  width: revert-layer;
}
variant-radios-sticky .select__sticky {
  font-size: var(--body-font-size);
  color: var(--color-heading);
}
sticky-add-cart .button_buy-now {
  min-width: 20rem;
}
sticky-add-cart .product__submit-form-cart {
  flex-wrap: nowrap;
}
sticky-add-cart variant-group-sticky select {
  width: 100%;
  text-transform: capitalize;
}
@media (max-width: 767.98px) {
  sticky-add-cart .product-form__quantity {
    display: none;
  }
  sticky-add-cart .button_buy-now {
    min-width: unset;
  }
  html:not(.open-drawer, .nav-open) .mobi-navigation-bar sticky-add-cart {
    z-index: 15;
  }
}
.product-group-list .product-item:not(:last-child) {
  padding-bottom: 1.5rem;
  border-bottom: 1px solid var(--border-color-base);
  margin-bottom: 1.5rem;
}
.product-group-list .product-item__wrapper {
  gap: 1.5rem;
}
.product-group-list .product-item__inner {
  width: 9rem;
}
.more-colors-product_item:hover {
  border-color: var(--color-heading);
}
.delivery-return.vertical {
  flex-direction: column;
}
.delivery-return.vertical > div {
  flex: 0 0 auto;
  align-items: center;
}
.delivery-return.vertical > div:not(:last-child) {
  border-bottom: 1px dashed var(--color-border);
}
.delivery-return.vertical svg {
  margin-inline-end: 1rem;
  color: var(--color-heading);
}
@media (min-width: 768px) {
  .delivery-return.vertical > div:first-child {
    padding-top: 0;
  }
  .delivery-return.vertical > div:last-child {
    padding-bottom: 0;
  }
}
@media (max-width: 767.98px) {
  .thumbnail-slide .swiper-actions {
    display: none;
  }
  .delivery-return > div {
    flex: 0 0 auto;
    width: 100%;
  }
  .thumbnail-slide__mobile--hidden {
    display: none;
  }
  body .swiper-pagination-custom {
    --swiper-pagination-bottom: 1.5rem;
    --swiper-pagination-position: absolute;
    --swiper-pagination-mt: 0;
    right: 1.5rem;
    bottom: 1.5rem;
    left: auto;
    top: auto;
    background-color: var(--color-white);
    border-radius: var(--btn-radius);
    border: 1px solid var(--color-border);
    font-weight: var(--subheading-weight);
    color: var(--color-heading);
    width: auto;
    padding: 8px 17px;
    display: inline-flex;
    align-items: center;
    line-height: 1;
  }
  .main-product {
    position: relative;
  }
  .main-product .zoom_light_box {
    display: none;
  }
  .product-detail__information .sticky {
    position: static;
  }
  .product-detail__information button-compare {
    display: none;
  }
  .featured-product__content {
    position: relative;
  }
  .product-detail__media:not(.product-featured) button-wishlist {
    position: absolute;
    right: 15px;
    top: 15px;
  }
  .product-detail__information:not(.product-featured) button-wishlist {
    display: none;
  }
  body
    .skeleton
    .product-detail__information
    :is(button-wishlist.skeleton-loading) {
    position: absolute;
    background-color: var(--color-white) !important;
    border: 1px solid var(--color-border) !important;
  }
}
.appstle_sub_widget {
  margin-bottom: 2rem;
}
.appstle_tooltip_content {
  --color-heading: var(--color-white);
}
media-gallery
  :is(
    .plyr--video,
    .shopify-model-viewer-ui,
    .external_video iframe,
    .deferred-media
  ) {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}
media-gallery .shopify-model-viewer-ui model-viewer,
media-gallery .external_video iframe model-viewer {
  width: 100%;
  height: 100%;
}
.product_media-model-icon {
  top: 3px;
  right: 3px;
  z-index: 1;
}
address {
  font-style: normal;
}
swatch-dropdown.active {
  --color-border: var(--color-dark);
}

[data-type="dropdown"]
  .product-form__input
  swatch-dropdown
  input[type="radio"]:not(.option-disabled):checked
  + label {
  background-color: var(--grey-color);
  border-color: var(--grey-color);
  color: var(--color-dark);
}
[data-type="dropdown"] .select-custom.active .select-custom__content {
  top: calc(100% - 1px);
}
pickup-availability-drawer.open + .overlay:after {
  opacity: 1;
  pointer-events: auto;
  visibility: visible;
}
.product-bought-image-item {
  flex: 0 0 21%;
  width: 21%;
}
.product-bought-together-item.main-product .product-item-checkbox {
  opacity: 0.7;
}
@media (max-width: 767.98px) {
  product-bought-together .box-total {
    width: 100%;
  }
  .info-bought-together {
    order: -1;
  }
  .media-main-swiper .swiper-actions.show-on-mobile {
    margin-bottom: 0;
  }
}
@media (min-width: 768px) {
  product-bought-together .grow-1 {
    flex: 0 0 calc(100% - 36rem);
    width: calc(100% - 36rem);
  }
  .product-bought-image-item {
    flex: 0 0 235px;
    width: 235px;
  }
}
.product-bought-image-item:not(.select) .product-image {
  opacity: 0.6;
}
.checkbought.checkmark:before {
  border-radius: 50%;
  width: 20px;
  height: 20px;
}
.checkbought.checkmark::after {
  border-width: 0 1px 1px 0;
  height: 9px;
  left: 8px;
  top: 6px;
}
.jdgm-paginate:empty,
.review_sold:empty {
  display: none !important;
}
.cloudimage-360-icons-container {
  right: 15px;
  top: 15px;
  width: 5rem;
}
.cloudimage-360-fullscreen-icon {
  background: url("data:image/svg+xml,%3Csvg%20width%3D%2214%22%20height%3D%2214%22%20viewBox%3D%220%200%2014%2014%22%20fill%3D%22none%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cpath%20d%3D%22M0.000357628%2013.3636L0.000596046%2010.1813C0.000596046%209.82984%200.28544%209.54512%200.636787%209.54512C0.988253%209.54512%201.27286%209.82984%201.27286%2010.1814V11.8261L4.96974%208.12603C5.09417%208.00148%205.25721%207.93963%205.42013%207.93963C5.58281%207.93963%205.7455%208.0016%205.8698%208.12591C6.1183%208.37416%206.11853%208.77699%205.87016%209.02549L2.17208%2012.7271H3.81643C4.16789%2012.7271%204.45274%2013.0121%204.45274%2013.3637C4.45274%2013.715%204.16777%2014%203.81643%2014H0.636787C0.467907%2014%200.306178%2013.9329%200.186758%2013.8134C0.067338%2013.6941%200.000357628%2013.532%200.000357628%2013.3636ZM0.636668%204.45524C0.988253%204.45524%201.27286%204.16992%201.27286%203.81869V2.17399L4.90777%205.77791C5.1565%206.02641%205.57638%206.02665%205.82487%205.77815C6.07348%205.53002%206.08206%205.12694%205.83381%204.87857L2.23561%201.27286H3.88174H3.88305C4.23452%201.27286%204.51972%200.988133%204.51984%200.636548C4.51995%200.285439%204.23559%200.000356674%203.884%200.000356674L0.70484%200C0.53584%200%200.339906%200.0670996%200.220843%200.186399C0.101542%200.3057%200.000238419%200.467548%200.000238419%200.636189V3.81881C0.000357628%204.17004%200.285321%204.45524%200.636668%204.45524ZM9.09271%205.80592L12.7273%202.17375V3.81881C12.7273%204.17028%2013.0065%204.45452%2013.3579%204.45452H13.3552C13.7067%204.45452%2013.9902%204.16992%2013.9902%203.81881L13.99%200.636667C13.99%200.467787%2013.9227%200.305939%2013.8034%200.186638C13.6838%200.0672178%2013.5217%200.000237465%2013.353%200.000237465H10.1732C9.82174%200.000237465%209.5369%200.285201%209.5369%200.636548C9.5369%200.988253%209.82186%201.2731%2010.1732%201.2731H11.8171L8.18705%204.90646C7.93832%205.15483%207.94153%205.55826%208.19003%205.8064C8.43852%206.05453%208.84409%206.0543%209.09271%205.80592ZM11.8283%2012.6698H10.1842C9.8327%2012.6698%209.54798%2012.9544%209.54798%2013.3058C9.54798%2013.6574%209.83282%2013.9423%2010.1842%2013.9423L13.3636%2013.9426H13.3637C13.5326%2013.9426%2013.6942%2013.8758%2013.8137%2013.7565C13.9329%2013.6372%2014%2013.4755%2014%2013.3064L13.9996%2010.124C13.9996%209.77299%2013.7148%209.48767%2013.3635%209.48767C13.012%209.48767%2012.7273%209.77299%2012.7273%2010.124V11.7689L9.05934%208.09802C8.93503%207.97359%208.77199%207.91138%208.60907%207.91138C8.4465%207.91138%208.28358%207.97335%208.1594%208.09766C7.91079%208.34592%207.91043%208.74911%208.15904%208.99784L11.8283%2012.6698Z%22%20fill%3D%22%23111111%22%3E%3C/path%3E%3C/svg%3E")
    no-repeat center;
  background-color: #fff;
  border: 1px solid #ebebeb;
  border-radius: 50%;
  width: 5rem;
  height: 5rem;
}
.cloudimage-360 .cloudimage-360-left {
  cursor: pointer;
  border-right: 1px solid var(--color-border);
  position: unset;
  background: none;
  padding: 0;
  border-radius: 0;
  width: 40px;
}
.cloudimage-360 .cloudimage-360-right {
  position: unset;
  cursor: pointer;
  background: none;
  padding: 0;
  width: 40px;
}
.cloudimage-360 :is(.cloudimage-360-right, .cloudimage-360-left):hover:before {
  opacity: 1;
}
.cloudimage-360 .cloudimage-360-left:before {
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="8" height="12" viewBox="0 0 8 12" fill="none"><path d="M7.02321 10.5327C7.18908 10.7069 7.27202 10.9146 7.27202 11.1558C7.27202 11.3836 7.18908 11.5779 7.02321 11.7387C6.8701 11.9129 6.67872 12 6.44905 12C6.21939 12 6.028 11.9129 5.87489 11.7387L0.975366 6.59296C0.809497 6.43216 0.726562 6.23786 0.726562 6.01005C0.726562 5.76884 0.809497 5.56114 0.975366 5.38693L5.87489 0.241206C6.028 0.080402 6.21939 0 6.44905 0C6.67872 0 6.8701 0.080402 7.02321 0.241206C7.18908 0.41541 7.27202 0.623116 7.27202 0.864322C7.27202 1.09213 7.18908 1.28643 7.02321 1.44724L2.69785 5.98995L7.02321 10.5327Z" fill="%23111111"/></svg>')
    no-repeat center;
  margin: auto;
  opacity: 0.4;
  transition: var(--transition);
}
.cloudimage-360 .cloudimage-360-right:before {
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="8" height="12" viewBox="0 0 8 12" fill="none"><path d="M2.12369 11.7387C1.97058 11.9129 1.77919 12 1.54953 12C1.31986 12 1.12848 11.9129 0.975366 11.7387C0.809497 11.5779 0.726562 11.3836 0.726562 11.1558C0.726562 10.9146 0.809497 10.7069 0.975366 10.5327L5.30072 5.98995L0.975366 1.44724C0.809497 1.28643 0.726562 1.09213 0.726562 0.864322C0.726562 0.623116 0.809497 0.41541 0.975366 0.241206C1.12848 0.080402 1.31986 0 1.54953 0C1.77919 0 1.97058 0.080402 2.12369 0.241206L7.02321 5.38693C7.18908 5.56114 7.27202 5.76884 7.27202 6.01005C7.27202 6.23786 7.18908 6.43216 7.02321 6.59296L2.12369 11.7387Z" fill="%23111111"/></svg>')
    no-repeat center;
  margin: auto;
  opacity: 0.4;
  transition: var(--transition);
}
.control_360 {
  display: flex;
  right: 50%;
  bottom: 20px;
  transform: translateX(50%);
  position: absolute;
  color: #999999;
  z-index: 1;
  height: 44px;
  background: var(--color-white);
  border: 1px solid var(--border-color-base);
  border-radius: 5px;
  -webkit-box-shadow: inset 0 0 5px rgba(255, 255, 255, 0.5);
  box-shadow: inset 0 0 5px rgba(255, 255, 255, 0.5);
}
.zoom_light_box {
  background-color: #fff;
  border: 1px solid #ebebeb;
  border-radius: 50%;
  width: 5rem;
  height: 5rem;
  position: absolute;
  right: 15px;
  top: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
.cloudimage-360-close-fullscreen-icon {
  width: 2rem;
  height: 2rem;
}
.product__xr-button {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 1.5rem;
}
collapsible-block + product-recommendations {
  margin-top: 3rem;
}
.pickup_avaiability:has(pickup-availability > template) {
  display: none;
}
.ask-question .text-area {
  height: 12rem;
  min-height: 12rem;
}
.appstle_widget_title {
  color: var(--color-heading);
  font-weight: var(--heading-weight);
}
.product__submit-form-cart:has(.appstle_sub_widget) {
  width: 100%;
  gap: 0;
  margin-bottom: 10px;
}
.product__submit-form-cart:has(.appstle_sub_widget) .button_buy-now {
  width: 100%;
}
.product-form__buttons #appstle_subscription_widget0 .appstle_subscribe_option {
  margin-top: 15px;
  font-size: 13px;
}
.model-avatar{
  width: 8rem;
  height: 8rem;
}
.model-avatar,
.model-info-left {
  margin-right: 2rem;
}
.product_set.grid {
  grid-template-columns: repeat(4, minmax(0, 1fr));
  grid-template-rows: 1fr 1fr;
  gap: 5px;
}
.product_set.grid .product-item:first-child {
  grid-area: span 2 / span 2;
}
.product_set.grid .product-item:first-child .product-item__action {
  display: none;
}
.product_set.grid .product-item {
  grid-area: span 1 / span 1;
}
.product_set.grid .product-item__information {
  display: none;
}
.label-set {
  background-color: #666;
}
.product_set.grid.grid-4 .product-item:last-child {
  grid-area: 2 / 3 / 3 / 5;
}
.product_set.grid.grid-4 .product-item:last-child .product-item__wrapper {
  --btn-padding-y: 1.4rem;
}
.product_set.grid.grid-4
  .product-item:last-child
  [style*="--aspect-ratio"]:before {
  padding: 0;
}
.product_set.grid
  .product-item
  :is(.product-item__wrapper, .product-item__inner, .product__media) {
  height: 100%;
}
.product_set.grid .product-item:not(:first-child) .product-item__wrapper {
  --btn-padding-y: 1rem;
}
.sec__products-set .swiper-pagination {
  width: auto;
}
@media (max-width: 767px) {
  .product_set.grid.reset-layout-mobile {
    grid-template-columns: repeat(4, minmax(0, 1fr));
    grid-template-rows: 1fr 1fr 1fr 1fr;
  }
  .product_set.grid.reset-layout-mobile .product-item:first-child {
    grid-area: 1 / 1 / 3 / 5;
  }
  .product_set.grid.reset-layout-mobile .product-item:nth-child(2) {
    grid-area: 3 / 1 / 4 / 3;
  }
  .product_set.grid.reset-layout-mobile .product-item:nth-child(3) {
    grid-area: 3 / 3 / 4 / 5;
  }
  .product_set.grid.grid-4.reset-layout-mobile .product-item:last-child {
    grid-area: 4 / 1 / 5 / 5;
  }
  .product_set.grid.reset-layout-mobile .product-item {
    grid-area: span 2 / span 2;
  }
}
variant-radios-detail fieldset:last-of-type {
  margin-bottom: 0;
}

/* featured product */
.feature_product-center .product-detail__information{
    text-align: center;
}
.feature_product-center .card-product-price,
.feature_product-center .review_sold,
.feature_product-center .product-detail__information .product__badges{
  justify-content: center;
}
.product-featured .product-detail__variant-picker{
  margin-bottom: 2.3rem;
}
.product-featured .select-custom{
  border-radius: var(--btn-radius);
  height: 50px;
}
.product-featured .product-detail__variant-picker variant-radios-detail{
  padding-left: 0;
  padding-right: 0;
}