{%- liquid
  assign section_st = section.settings
  assign section_width = section_st.section_width
  assign color_scheme = section_st.color_scheme
  if section_st.reset_spacing
    assign reset_spacing = ' remove_spacing'
  endif
  assign column_gap = section_st.column_gap
  assign items_per_row_mobile = section_st.items_per_row_mobile
  assign heading = section_st.heading
  assign description = section_st.description
  assign header_size = ''
  if section_st.header_size == 'small'
    assign header_size = 'h3'
  elsif section_st.header_size == 'large'
    assign header_size = 'h1-size'
  endif

  if section_st.items_per_row_mobile > 1 and section_st.items_per_row_mobile < 2
    assign data_free_scroll = 'true'
  endif

  assign image_ratio = section_st.image_ratio
  assign custom_ratio = section_st.custom_ratio
  assign display_mode = section_st.display_mode
  assign items_per_row = section_st.items_per_row
  assign column_gap = section_st.column_gap

  assign show_arrow = section_st.show_arrow
  assign carousel_pagination = section_st.carousel_pagination
  assign infinite = section_st.infinite
  assign autoplay = section_st.autoplay
  assign autorotate_speed = section_st.autorotate_speed
  assign reveal = section_st.reveal
  assign carousel_on_mobile = section_st.carousel_on_mobile
  assign scroll_animation = settings.scroll_animation
-%}
{%- capture style -%}
  --section-pt: {{ section_st.padding_top }}; --section-pb: {{ section_st.padding_bottom }};
{%- endcapture -%}
{%- capture col_style -%}
--col-desktop: {{ items_per_row }};--col-number: {{ items_per_row_mobile }};{% if items_per_row > 3 %}--col-tablet: 3;{% else %}--col-tablet: {{ items_per_row }};{% endif %}
{% if column_gap < 15 %}--col-gap: {{ column_gap }}px;{% else %}--col-gap: 15px;--col-gap-desktop: {{  column_gap }}px;{% endif %}--col-mobile: {{ items_per_row_mobile }}
{%- endcapture -%}
<div
  class="section{% if section_st.padding_top < 30 %} pt-min{% endif %}{% if section_st.padding_bottom < 30 %} pb-min{% endif %} sec__instagram color-{{ color_scheme }} gradient{{ reset_spacing }} {{ settings.hover_effect }}{% if reveal %} overflow-hidden{% endif %}"
  style="{{ style | strip | strip_newlines }}"
>
  <div class="{{ section_width }}">
    {%- if heading != blank or description != blank -%}
      <div class="section__header mb-33 mb-sm-20 text-{{ section_st.header_alignment }}{% if section_st.section_width == 'full_width' %} px-20{% endif %}">
        {%- if section_st.heading != blank -%}
          <motion-element
            data-motion="fade-up-lg"
            data-motion-delay="50"
            {% if scroll_animation != 'slide_in' %}
              hold
            {% endif %}
            class="block  {% if scroll_animation != 'none' %} scroll-trigger {{ scroll_animation }}{% endif %}"
            style="
              {%- if scroll_animation != 'none' -%}
                --animation-order: 0;
              {% endif %}
            "
          >
            <h2
              class="section__header-heading heading-letter-spacing {{ header_size  }} mt-0{% if section_st.description != blank %} mb-10{% else %} mb-0{% endif %}"
            >
              {{ section_st.heading }}
            </h2>
          </motion-element>
        {% endif %}
        {%- if section_st.description != blank -%}
          <motion-element
            data-motion="fade-up-lg"
            data-motion-delay="150"
            {% if scroll_animation != 'slide_in' %}
              hold
            {% endif %}
            class="section__header-des block {% if scroll_animation != 'none' %} scroll-trigger {{ scroll_animation }}{% endif %} rich__text-m0"
            style="
              {%- if scroll_animation != 'none' -%}
                --animation-order: 1
              {% endif %}
            "
          >
            {{ section_st.description }}
          </motion-element>
        {% endif %}
      </div>
    {% endif %}
    <svg hidden>
      <symbol id="icon-instagram">
        <path fill="currentColor" d="M14.959 4.41c-.035-.797-.164-1.345-.349-1.82a3.66 3.66 0 0 0-.867-1.33 3.691 3.691 0 0 0-1.328-.864c-.477-.185-1.022-.314-1.82-.35C9.794.01 9.539 0 7.502 0 5.465 0 5.21.009 4.41.044c-.797.035-1.345.164-1.82.349a3.66 3.66 0 0 0-1.33.867c-.38.375-.677.83-.864 1.327-.185.478-.314 1.023-.35 1.82C.01 5.21 0 5.465 0 7.501c0 2.037.009 2.292.044 3.092.035.797.164 1.345.349 1.82a3.7 3.7 0 0 0 .867 1.33c.375.38.83.677 1.327.864.478.185 1.023.314 1.82.349.8.035 1.055.044 3.092.044 2.036 0 2.291-.009 3.091-.044.797-.035 1.345-.164 1.82-.349a3.837 3.837 0 0 0 2.194-2.194c.185-.478.314-1.023.35-1.82.034-.8.043-1.055.043-3.092 0-2.036-.003-2.291-.038-3.091Zm-1.35 6.124c-.033.733-.156 1.128-.259 1.392a2.489 2.489 0 0 1-1.424 1.424c-.264.103-.662.226-1.392.258-.79.035-1.028.044-3.03.044-2 0-2.241-.009-3.03-.044-.732-.032-1.128-.155-1.391-.258a2.308 2.308 0 0 1-.862-.56 2.331 2.331 0 0 1-.56-.86c-.102-.265-.225-.663-.257-1.393-.036-.79-.044-1.028-.044-3.03 0-2.001.008-2.241.044-3.03.032-.732.155-1.128.257-1.391.12-.326.311-.622.563-.862.243-.249.536-.44.862-.56.263-.102.662-.225 1.391-.257.792-.036 1.029-.044 3.03-.044 2.005 0 2.242.008 3.03.044.733.032 1.128.155 1.392.257.325.12.621.311.862.56.249.243.44.536.56.862.102.263.225.662.257 1.391.035.792.044 1.029.044 3.03 0 2.002-.009 2.236-.044 3.027Z"/><path fill="currentColor" d="M7.506 3.648a3.854 3.854 0 0 0 0 7.707 3.854 3.854 0 0 0 0-7.707Zm0 6.353a2.5 2.5 0 1 1 0-5 2.5 2.5 0 0 1 0 5ZM12.405 3.496a.9.9 0 1 1-1.8 0 .9.9 0 0 1 1.8 0Z"/>
        </symbol>
    </svg>
    {% if section.blocks.size > 0 %}
      {% if display_mode == 'grid' %}
        <grid-custom
          data-enable="{{ carousel_on_mobile }}"
          class="collection-list--grid instagram--grid grid grid-cols gap {% if section_st.items_per_row_mobile > 1 and section_st.items_per_row_mobile < 2 %} grid_scroll{% endif %}"
          style="{{ col_style | strip | strip_newlines }}"
          data-mobile="{{ items_per_row_mobile }}"
          data-free-scroll="{{ data_free_scroll }}"
          data-spacing="{{ column_gap }}"
        >
          {% for block in section.blocks %}
            {% liquid
              assign block_st = block.settings
              assign image = block_st.image
              assign local_video = block_st.local_video
              assign link = block_st.link
              assign ratio = ''
              if image_ratio != 'adapt'
                case image_ratio
                  when 'square'
                    assign ratio = '1/1'
                  when 'landscape'
                    assign ratio = '4/3'
                  when 'portrait'
                    assign ratio = '3/4'
                  else
                    if custom_ratio != empty
                      assign ratio = custom_ratio | replace: ':', '/'
                    else
                      assign ratio = '3/4'
                    endif
                endcase
              else
                if image != blank
                  assign ratio = image.aspect_ratio
                else
                  assign ratio = '3/4'
                endif
              endif
            %}
            <motion-element
              data-motion="fade-up-lg"
              {% if scroll_animation != 'slide_in' %}
                hold
              {% endif %}
              data-motion-delay="{{ forloop.index0 | times: 50 }}"
              {% if link == blank %}
                role="link" aria-disabled="true"
              {% else %}
                href="{{ link | default: "#" }}"
              {% endif %}
              aria-label="{{ 'sections.instagram.label' | t }}"
              class="grid-custom-item {% if scroll_animation != 'none' %} scroll-trigger {{ scroll_animation }}{% endif %}"
              style="
                {%- if scroll_animation != 'none' -%}
                  --animation-order: {{  forloop.index }}
                {% endif %}
              "
              {{ block.shopify_attributes }}
            >
              <div
                class="instagram-item__media--ratio rounded hover-effect"
                style="--aspect-ratio: {{ ratio }};"
              >
                {% if local_video != blank %}
                  {%- liquid
                    assign source = local_video.sources
                    assign source_url = ''
                    for s in source
                      if s.format == 'mp4'
                        assign source_url = s.url
                        break
                      endif
                    endfor
                  -%}
                  <video
                    class="rounded"
                    loop="true"
                    muted="true"
                    playsinline="true"
                    autoplay="true"
                    data-src="{{ source_url }}"
                    data-poster="{{ local_video.preview_image | image_url: width: 1100 }}"
                    class="rounded lazy-video"
                  ></video>
                {% elsif image != blank %}
                  {% assign image_alt = image.alt | default: 'Instagram shop' %}
                  {% render 'responsive-image', type: 'banner', class: 'rounded', image: image, image_alt: image_alt %}
                {% else %}
                  {% render 'placeholder-render', class: 'rounded transition', image_alt: image_alt %}
                {% endif %}
                <span class="instagram-icon absolute inset-0 color-dark transition bg-white rounded shadow rounded-50 w-50 h-50 inline-flex content-center invisible pointer-none m-auto">
                  <svg
                    width="15"
                    height="15"
                    fill="none"
                  >
                    <use href="#icon-instagram"/>
                  </svg>
                </span>
              </div>
            </motion-element>
          {% endfor %}
        </grid-custom>
      {% else %}
        {% if section_st.items_per_row_mobile > 1 and section_st.items_per_row_mobile < 2 %}
          <div class="free-scroll">
        {% endif %}
        <slide-section
          class="swiper{% if reveal %} reveal_on_scroll{% endif %}"
          data-section-id="{{ section.id }}"
          data-autoplay="{{ autoplay }}"
          data-effect="slide"
          data-loop="{{ infinite }}"
          data-speed="500"
          data-autoplay-speed="{{ autorotate_speed }}"
          data-spacing="{{ column_gap }}"
          data-mobile="{{ items_per_row_mobile }}"
          data-desktop="{{ items_per_row }}"
          data-free-scroll="{{ data_free_scroll }}"
          data-pagination-progressbar="{% if carousel_pagination == 'show_progress_bar' %}true{% else %}false{% endif %}"
          style="{{ col_style | strip | strip_newlines }}"
          data-arrow-centerimage="1"
        >
          {% if show_arrow %}
            {%- render 'swiper-navigation' -%}
          {% endif %}
          <div class="swiper-wrapper">
            {% for block in section.blocks %}
              {% liquid
                assign block_st = block.settings
                assign image = block_st.image
                assign local_video = block_st.local_video
                assign link = block_st.link
                assign ratio = ''
                if image_ratio != 'adapt'
                  case image_ratio
                    when 'square'
                      assign ratio = '1/1'
                    when 'landscape'
                      assign ratio = '4/3'
                    when 'portrait'
                      assign ratio = '3/4'
                    else
                      if custom_ratio != empty
                        assign ratio = custom_ratio | replace: ':', '/'
                      else
                        assign ratio = '3/4'
                      endif
                  endcase
                else
                  if image != blank
                    assign ratio = image.aspect_ratio
                  else
                    assign ratio = '3/4'
                  endif
                endif
              %}
              <motion-element
                data-motion="fade-up-lg"
                {% if scroll_animation != 'slide_in' %}
                  hold
                {% endif %}
                data-motion-delay="{{ forloop.index0 | times: 50 }}"
                {% if link == blank %}
                  role="link" aria-disabled="true"
                {% else %}
                  href="{{ link | default: "#" }}"
                {% endif %}
                aria-label="{{ 'sections.instagram.label' | t }}"
                class="swiper-slide {% if scroll_animation != 'none' %} scroll-trigger {{ scroll_animation }}{% endif %}"
                {{ block.shopify_attributes }}
                style="
                  {%- if scroll_animation != 'none' -%}
                    --animation-order: {{  forloop.index }}
                  {% endif %}
                "
              >
                <div
                  class="instagram-item__media--ratio rounded hover-effect"
                  style="--aspect-ratio: {{ ratio }};"
                >
                  {% if local_video != blank %}
                    {%- liquid
                      assign source = local_video.sources
                      assign source_url = ''
                      for s in source
                        if s.format == 'mp4'
                          assign source_url = s.url
                          break
                        endif
                      endfor
                    -%}
                    <video
                      loop="true"
                      muted="true"
                      playsinline="true"
                      autoplay="true"
                      data-src="{{ source_url }}"
                      data-poster="{{ local_video.preview_image | image_url: width: 1100 }}"
                      class="rounded lazy-video"
                    ></video>
                  {% elsif image != blank %}
                    {% assign image_alt = image.alt | default: 'Instagram shop' %}
                    {% render 'responsive-image',
                      type: 'banner',
                      class: 'rounded',
                      image: image,
                      image_alt: image_alt
                    %}
                  {% else %}
                    {% render 'placeholder-render', class: 'rounded transition', image_alt: image_alt %}
                  {% endif %}
                  <span class="instagram-icon absolute inset-0 color-dark transition bg-white rounded shadow rounded-50 w-50 h-50 inline-flex content-center invisible pointer-none m-auto">
                    <svg
                      width="15"
                      height="15"
                      fill="none"
                    >
                      <use href="#icon-instagram"/>
                    </svg>
                  </span>
                </div>
              </motion-element>
            {% endfor %}
          </div>
          {%- if carousel_pagination == 'show_dots'
            or carousel_pagination == 'show_dots_on_mobile'
            or carousel_pagination == 'show_progress_bar'
          -%}
            <motion-element
              data-motion="fade-up-sm"
              {% if scroll_animation != 'slide_in' %}
                hold
              {% endif %}
              data-motion-delay="150"
              class="swiper-pagination  flex flex-wrap  {% if scroll_animation == 'slide_in' %} slide_in scroll-trigger {% endif %}   px-15 lh-1 bottom-30 {% if carousel_pagination == 'show_dots_on_mobile' %} hidden-md{% endif %} justify-content-center{% if section_st.content_below_image %} absolute-md-impo{% endif %}"
              style="--swiper-pagination-bottom: 3rem;--swiper-pagination-position: static;z-index: 2"
            >
            </motion-element>
          {% endif %}
        </slide-section>
        {% if section_st.items_per_row_mobile > 1 and section_st.items_per_row_mobile < 2 %}
          </div>
        {% endif %}
      {% endif %}
    {% endif %}
  </div>
</div>

{% schema %}
{
  "name": "t:sections.instagram.name",
  "tag": "section",
  "disabled_on": {
    "groups": ["header", "footer", "custom.overlay"]
  },
  "settings": [
    {
      "type": "select",
      "id": "section_width",
      "label": "t:sections.all.section_width.label",
      "default": "fluid_container",
      "options": [
        {
          "value": "container",
          "label": "t:sections.all.section_width.container.label"
        },
        {
          "value": "fluid_container",
          "label": "t:sections.all.section_width.fluid_container.label"
        },
        {
          "value": "stretch_width",
          "label": "t:sections.all.section_width.stretch_width.label"
        },
        {
          "value": "full_width",
          "label": "t:sections.all.section_width.full_width.label"
        }
      ]
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.color_scheme.label",
      "default": "scheme-1"
    },
    {
      "type": "header",
      "content": "t:sections.all.section_header.label"
    },
    {
      "type": "text",
      "id": "heading",
      "label": "t:sections.all.section_header.heading.label",
      "default": "Instagram"
    },
    {
      "type": "richtext",
      "id": "description",
      "label": "t:sections.all.section_header.description.label"
    },
    {
      "type": "select",
      "id": "header_size",
      "label": "t:sections.all.section_header.header_size.label",
      "default": "medium",
      "options": [
        {
          "value": "small",
          "label": "t:sections.all.section_header.header_size.small.label"
        },
        {
          "value": "medium",
          "label": "t:sections.all.section_header.header_size.medium.label"
        },
        {
          "value": "large",
          "label": "t:sections.all.section_header.header_size.large.label"
        }
      ]
    },
    {
      "type": "select",
      "id": "header_alignment",
      "label": "t:sections.all.section_header.alignment.label",
      "default": "center",
      "options": [
        {
          "value": "left",
          "label": "t:sections.all.section_header.alignment.left.label"
        },
        {
          "value": "center",
          "label": "t:sections.all.section_header.alignment.center.label"
        },
        {
          "value": "right",
          "label": "t:sections.all.section_header.alignment.right.label"
        }
      ]
    },
    {
      "type": "header",
      "content": "t:sections.instagram.settings.instagram_settings.header"
    },
    {
      "type": "select",
      "id": "image_ratio",
      "label": "t:sections.all.image.image_ratio.label",
      "options": [
        {
          "value": "adapt",
          "label": "t:sections.all.image.image_ratio.adapt.label"
        },
        {
          "value": "square",
          "label": "t:sections.all.image.image_ratio.square.label"
        },
        {
          "value": "portrait",
          "label": "t:sections.all.image.image_ratio.portrait.label"
        },
        {
          "value": "landscape",
          "label": "t:sections.all.image.image_ratio.landscape.label"
        },
        {
          "value": "custom",
          "label": "t:sections.all.image.image_ratio.custom.label"
        }
      ],
      "default": "adapt"
    },
    {
      "type": "text",
      "id": "custom_ratio",
      "label": "t:sections.all.image.image_ratio.custom_ratio.label"
    },
    {
      "type": "select",
      "id": "display_mode",
      "label": "t:sections.all.display_mode.label",
      "options": [
        {
          "value": "grid",
          "label": "t:sections.all.display_mode.grid"
        },
        {
          "value": "carousel",
          "label": "t:sections.all.display_mode.carousel"
        }
      ],
      "default": "grid"
    },
    {
      "type": "range",
      "id": "items_per_row",
      "label": "t:sections.all.items.items_per_row.label",
      "min": 2,
      "max": 8,
      "step": 1,
      "default": 4
    },
    {
      "type": "range",
      "id": "column_gap",
      "label": "t:sections.all.items.column_gap.label",
      "min": 0,
      "max": 50,
      "step": 5,
      "default": 30,
      "unit": "px"
    },
    {
      "type": "header",
      "content": "t:sections.all.carousel_settings.label",
      "visible_if": "{{ section.settings.display_mode == 'carousel' }}"
    },
    {
      "type": "checkbox",
      "id": "show_arrow",
      "label": "t:sections.all.carousel_settings.show-next-back.label",
      "default": false,
      "visible_if": "{{ section.settings.display_mode == 'carousel' }}"
    },
    {
      "type": "select",
      "id": "carousel_pagination",
      "label": "t:sections.all.carousel_settings.pagination.label",
      "options": [
        {
          "value": "disable",
          "label": "t:sections.all.carousel_settings.pagination.disable.label"
        },
        {
          "value": "show_dots",
          "label": "t:sections.all.carousel_settings.pagination.show_dots.label"
        },
        {
          "value": "show_dots_on_mobile",
          "label": "t:sections.all.carousel_settings.pagination.show_dots_on_mobile.label"
        },
        {
          "value": "show_progress_bar",
          "label": "t:sections.all.carousel_settings.pagination.show_progress_bar.label"
        }
      ],
      "visible_if": "{{ section.settings.display_mode == 'carousel' }}"
    },
    {
      "type": "checkbox",
      "id": "infinite",
      "label": "t:sections.all.carousel_settings.infinite.label",
      "default": false,
      "visible_if": "{{ section.settings.display_mode == 'carousel' }}"
    },
    {
      "type": "checkbox",
      "id": "autoplay",
      "label": "t:sections.all.carousel_settings.auto_change.label",
      "default": false,
      "visible_if": "{{ section.settings.display_mode == 'carousel' }}"
    },
    {
      "type": "range",
      "id": "autorotate_speed",
      "label": "t:sections.all.carousel_settings.change_slides_every.label",
      "max": 6,
      "min": 2,
      "step": 1,
      "unit": "s",
      "default": 5,
      "visible_if": "{{ section.settings.display_mode == 'carousel' }}"
    },
    {
      "type": "checkbox",
      "id": "reveal",
      "label": "t:sections.all.carousel_settings.reveal.label",
      "info": "t:sections.all.carousel_settings.reveal.info",
      "default": false,
      "visible_if": "{{ section.settings.display_mode == 'carousel' }}"
    },
    {
      "type": "header",
      "content": "t:sections.all.mobile_options.label"
    },
    {
      "type": "checkbox",
      "id": "carousel_on_mobile",
      "label": "t:sections.all.mobile_options.carousel_on_mobile.label",
      "default": false,
      "visible_if": "{{ section.settings.display_mode == 'grid' }}"
    },
    {
      "type": "range",
      "id": "items_per_row_mobile",
      "min": 1,
      "max": 2,
      "step": 0.5,
      "label": "t:sections.all.items.items_per_row.label",
      "default": 1
    },
    {
      "type": "header",
      "content": "t:sections.all.section_padding.label"
    },
    {
      "type": "range",
      "id": "padding_top",
      "label": "t:sections.all.section_padding.top.label",
      "default": 0,
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px"
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "label": "t:sections.all.section_padding.bottom.label",
      "default": 0,
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px"
    },
    {
      "type": "checkbox",
      "id": "reset_spacing",
      "label": "t:sections.all.section_padding.reset_spacing.label",
      "default": false
    }
  ],
  "blocks": [
    {
      "type": "instagram_manual_upload",
      "name": "t:sections.instagram.blocks.name",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "t:sections.instagram.blocks.image"
        },
        {
          "type": "video",
          "id": "local_video",
          "label": "t:sections.instagram.blocks.local_video"
        },
        {
          "type": "url",
          "id": "link",
          "label": "t:sections.instagram.blocks.link"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "t:sections.instagram.name",
      "blocks": [
        {
          "type": "instagram_manual_upload"
        },
        {
          "type": "instagram_manual_upload"
        },
        {
          "type": "instagram_manual_upload"
        },
        {
          "type": "instagram_manual_upload"
        }
      ]
    }
  ]
}
{% endschema %}
