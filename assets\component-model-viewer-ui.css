.shopify-model-viewer-ui .shopify-model-viewer-ui__controls-area {
  background: var(--color-white);
  border-color: var(--border-color-base);
}
.rounded-style
  .shopify-model-viewer-ui
  .shopify-model-viewer-ui__controls-area {
  border-radius: 5px;
}
.shopify-model-viewer-ui .shopify-model-viewer-ui__button,
button.shopify-model-viewer-ui__button.shopify-model-viewer-ui__button--poster {
  background: var(--color-white);
}

.shopify-model-viewer-ui .shopify-model-viewer-ui__button--control:hover {
  color: rgba(var(--color-heading), 0.55);
}

.shopify-model-viewer-ui .shopify-model-viewer-ui__button--control:active,
.shopify-model-viewer-ui
  .shopify-model-viewer-ui__button--control.focus-visible:focus {
  color: rgba(var(--color-heading), 0.55);
  background: rgba(var(--color-heading), 0.04);
}

.shopify-model-viewer-ui
  .shopify-model-viewer-ui__button--control:not(:last-child):after {
  border-color: var(--border-color-base);
}

.shopify-model-viewer-ui .shopify-model-viewer-ui__button--poster {
  border-radius: 50%;
  color: rgba(var(--color-heading), 0.6);
  background: rgb(var(--color-background));
  border-color: var(--color-border);
  transform: translate(-50%, -50%) scale(1);
  transition: transform var(--duration-short) ease,
    color var(--duration-short) ease;
}

.shopify-model-viewer-ui .shopify-model-viewer-ui__poster-control-icon {
  width: 4.8rem;
  height: 4.8rem;
  margin-top: 0.3rem;
}

.shopify-model-viewer-ui .shopify-model-viewer-ui__button--poster:hover,
.shopify-model-viewer-ui .shopify-model-viewer-ui__button--poster:focus {
  transform: translate(-50%, -50%) scale(1.1);
}
.shopify-model-viewer-ui .shopify-model-viewer-ui__control-icon {
  width: 3.6rem;
  margin: auto;
}
