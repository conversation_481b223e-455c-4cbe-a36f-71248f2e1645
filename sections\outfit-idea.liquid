{{ 'banner.css' | asset_url | stylesheet_tag }}
{%- liquid
  assign section_st = section.settings
  assign section_width = section_st.section_width
  assign color_scheme = section_st.color_scheme
  if section_st.reset_spacing
    assign reset_spacing = ' remove_spacing'
  endif
  assign column_gap = section_st.column_gap
  assign items_per_row = section_st.items_per_row
  assign heading = section_st.heading
  assign description = section_st.description
  assign image_ratio = section_st.image_ratio
  assign custom_ratio = section_st.custom_ratio
  assign header_size = ''
  if section_st.header_size == 'small'
    assign header_size = 'h3'
  elsif section_st.header_size == 'large'
    assign header_size = 'h1-size'
  endif
  assign scroll_animation = settings.scroll_animation
  assign carousel_on_mobile = section_st.carousel_on_mobile
-%}
{%- capture style -%}
    --section-pt: {{ section_st.padding_top }}; --section-pb: {{ section_st.padding_bottom }};
  {%- endcapture -%}
{%- capture col_style -%}
--col-gap: {{ column_gap }}px; --col-number: 1;{% if items_per_row > 2 %}--col-tablet: 2;--col-desktop-small: 2;{% endif %} --col-desktop: {{ items_per_row }};
  {%- endcapture -%}
{%- capture style_ratio -%}
  {%- liquid
    assign ratio = ''
    if image_ratio != 'adapt'
      case image_ratio
        when 'square'
          assign ratio = '1/1'
        when 'landscape'
          assign ratio = '4/3'
        when 'portrait'
          assign ratio = '3/4'
        else
          if custom_ratio != empty
            assign ratio = custom_ratio | replace: ':', '/'
          else
            assign ratio = '3/2'
          endif
      endcase
    else
      assign ratio = '3/2'
    endif
  -%}
  --aspect-ratio: {{ ratio }};
{%- endcapture -%}
<div
  class="section{% if section_st.padding_top < 30 %} pt-min{% endif %}{% if section_st.padding_bottom < 30 %} pb-min{% endif %} sec__products-grid color-{{ color_scheme }} gradient{{ reset_spacing }} outfit_idea"
  style="{{ style | strip | strip_newlines }}"
>
  <div class="{{ section_width }}">
    {%- if heading != blank or description != blank -%}
      <div class="section__header mb-33 mb-sm-20 text-{{ section_st.header_alignment }} {% if section_st.section_width == 'full_width' %} px-20{% endif %}">
        {%- if heading != blank or description != blank -%}
          <div class="secion__header-inner">
            {%- if section_st.heading != blank -%}
              <motion-element
                data-motion="fade-up-lg"
                data-motion-delay="50"
                {% if scroll_animation != 'slide_in' %}
                  hold
                {% endif %}
                class="block  {% if scroll_animation != 'none' %} scroll-trigger {{ scroll_animation }}{% endif %}"
                style="
                  {%- if scroll_animation != 'none' -%}
                    --animation-order: 0;
                  {% endif %}
                "
              >
                <h2
                  class="section__header-heading heading-letter-spacing {{ header_size  }} mt-0{% if section_st.description != blank %} mb-10{% else %} mb-0{% endif %}"
                >
                  {{ section_st.heading }}
                </h2>
              </motion-element>
            {% endif %}
            {%- if section_st.description != blank -%}
              <motion-element
                data-motion="fade-up-lg"
                data-motion-delay="150"
                {% if scroll_animation != 'slide_in' %}
                  hold
                {% endif %}
                class="section__header-des block {% if scroll_animation != 'none' %} scroll-trigger {{ scroll_animation }}{% endif %}  rich__text-m0"
                style="
                  {%- if scroll_animation != 'none' -%}
                    --animation-order: 1
                  {% endif %}
                "
              >
                {{ section_st.description }}
              </motion-element>
            {% endif %}
          </div>
        {% endif %}
      </div>
    {% endif %}
    <carousel-mobile
      class="products-grid__items {% if section.blocks.size > 2  %} products-idea-limit {% else %}  {% endif %} grid grid-cols gap"
      style="{{ col_style | strip | strip_newlines }}"
      data-enable="{{ carousel_on_mobile }}"
      data-mobile="1"
      data-spacing="{{ column_gap }}"
    >
      {% for block in section.blocks %}
        {%- liquid
          assign block_st = block.settings
        -%}
        {% case block.type %}
          {%- when 'image_with_text_overlay' -%}
            {% liquid
              assign image = block_st.image
              assign mobile_image = block_st.mobile_image | default: image
            %}
            <motion-element
              data-motion="fade-up-lg"
              {% if scroll_animation != 'slide_in' %}
                hold
              {% endif %}
              data-motion-delay="{{ forloop.index0 | times: 50 }}"
              class="section__block-inner sec__icon-switch-slide gradient hover-effect  {% if scroll_animation != 'none' %} scroll-trigger {{ scroll_animation }}{% endif %}  flex relative"
              style="
                {% if image_ratio == "adapt" and image != blank %} --aspect-ratio: {{ image.aspect_ratio  }};{%- if block_st.mobile_image -%} --aspect-ratio-mb: {{ block_st.mobile_image.aspect_ratio }};{% endif %}  {% else %} --aspect-ratio: {{ ratio  }}  {% endif %} ;     --overlay-opacity: {{ block_st.image_overlay_opacity }}%;  {%- if scroll_animation != 'none' -%}
                  --animation-order: {{  forloop.index }}
                {% endif %}
              "
            >
              <div
                class="banner__media w-full overlay-bg rounded absolute inset-0"
                style=" {% if image_ratio == "adapt" and image != blank %} --aspect-ratio: {{ image.aspect_ratio  }};{%- if block_st.mobile_image -%} --aspect-ratio-mb: {{ block_st.mobile_image.aspect_ratio }};{% endif %}  {% else %} --aspect-ratio: {{ ratio  }}  {% endif %}"
              >
                {%- if image != blank or mobile_image != blank -%}
                  {%- assign image_alt = image.alt | escape | default: 'Images' -%}
                  {% render 'responsive-image',
                    type: 'banner',
                    container: section_width,
                    colunm: items_per_row,
                    image: image,
                    image_mobile: mobile_image,
                    image_alt: image_alt
                  %}
                {%- else -%}
                  {%- render 'placeholder-render' -%}
                {% endif %}
                {% if block_st.image_link != blank %}
                  <a
                    class="absolute inset-0 z-2 block hidden-md"
                    aria-label="{{ block_st.heading }}"
                    href="{{ block_st.image_link }}"
                  ></a>
                {% endif %}
              </div>
              <div
                class="sec__content w-full flex {{ block_st.content_position }} text-{{ block_st.content_alignment }}"
                style="--padding-inline: {{ block_st.content_padding_inline }};--padding-block: {{ block_st.content_padding_block }};"
              >
                <div class="sec__content-inner py-custom px-custom relative{% if block_st.content_padding_inline < 35 %} x-min-value{% endif %}{% if block_st.content_padding_block < 35 %} y-min-value{% endif %}{% if block_st.content_below_image %} w-full{% endif %}">
                  {%- if block_st.custom_svg != blank -%}
                    <div
                      class="sec__content-custom-svg {% if block_st.subheading_spacing_bottom > 41 %} mb-big{% elsif block_st.subheading_spacing_bottom > 30 %} mb-medium {% else %} mb-custom{% endif %}"
                      style="--space-bottom: {{ block_st.custom_svg_spacing_bottom }}"
                    >
                      {{ block_st.custom_svg }}
                    </div>
                  {% endif %}
                  {%- if block_st.subheading != blank -%}
                    <div
                      class="sec__content-subheading heading-color heading fs-custom {{ block_st.subheading_font_weight }}{% if block_st.subheading_spacing_bottom > 41 %} mb-big{% elsif block_st.subheading_spacing_bottom > 30 %} mb-medium {% else %} mb-custom{% endif %}"
                      style="--font-size: {{ block_st.subheading_font_size }};--space-bottom: {{ block_st.subheading_spacing_bottom }}"
                    >
                      {{ block_st.subheading | escape }}
                    </div>
                  {% endif %}
                  {%- if block_st.heading != blank -%}
                    <h2
                      class="sec__content-heading heading-letter-spacing mt-0{% if block_st.heading_spacing_bottom > 41 %} mb-big{% elsif block_st.heading_spacing_bottom > 30 %} mb-medium {% else %} mb-custom{% endif %}{% if block_st.heading_uppercase %} uppercase{% endif %}{% if block_st.heading_font_size > 41 %} fs-big{% elsif block_st.heading_font_size > 24 %} fs-medium {% else %} fs-custom{% endif %} {{ block_st.heading_font_weight }}"
                      style="--font-size: {{ block_st.heading_font_size }};--space-bottom: {{ block_st.heading_spacing_bottom }}"
                      {{ block.shopify_attributes }}
                    >
                      {{ block_st.heading }}
                    </h2>
                  {% endif %}
                  {%- if block_st.description != blank -%}
                    <div
                      class="sec__content-des rich__text-m0 {% if block_st.des_spacing_bottom > 41 %} mb-big{% elsif block_st.des_spacing_bottom > 30 %} mb-medium {% else %} mb-custom{% endif %}{% if block_st.des_font_size > 24 %} fs-medium {% else %} fs-custom{% endif %} {{ block_st.des_font_weight }}"
                      style="--font-size: {{ block_st.des_font_size }};--space-bottom: {{ block_st.des_spacing_bottom }}"
                      {{ block.shopify_attributes }}
                    >
                      {{ block_st.description }}
                    </div>
                  {% endif %}

                  {% if block_st.timer != blank %}
                    {{ 'countdown.css' | asset_url | stylesheet_tag }}
                    {% liquid
                      assign fs_custom = ''
                      if block_st.font_size > 41
                        assign fs_custom = 'fs-big'
                      elsif block_st.font_size > 24
                        assign fs_custom = 'fs-medium'
                      else
                        assign fs_custom = 'fs-custom'
                      endif
                      assign mb_custom = ''
                      if block_st.spacing_bottom > 41
                        assign mb_custom = 'mb-big'
                      elsif block_st.spacing_bottom > 30
                        assign mb_custom = 'mb-medium'
                      else
                        assign mb_custom = 'mb-custom'
                      endif
                    %}
                    <div
                      class="timer outfit-timer {{ block_st.style }}  {{ mb_custom }} {{ fs_custom }}"
                      style="
                        --font-size: {{ block_st.font_size }};--space-bottom: {{ block_st.spacing_bottom }};{%- if scroll_animation != 'none' -%}
                          --animation-order: {{  forloop.index }}
                        {% endif %}
                      "
                    >
                      <countdown-timer
                        class="hidden inline-flex gap-10 justify-content-md-{{ section_st.content_alignment }}"
                        data-endtime="{{ block_st.timer }}"
                        data-timeout-message="{{ block_st.expired_message }}"
                        data-format="dd:hh:mm:ss"
                        data-days="{{ 'sections.times_bn.days' | t }}"
                        data-hours="{{ 'sections.times_bn.hour' | t }}"
                        data-mins="{{ 'sections.times_bn.mins' | t }}"
                        data-secs="{{ 'sections.times_bn.secs' | t }}"
                      >
                      </countdown-timer>
                    </div>
                  {% endif %}
                  {% if block_st.first_button_label != blank or block_st.second_button_label != blank %}
                    <div class="sec__content-btn align-center flex flex-wrap gap-15 justify-content-{{ block_st.content_alignment }}">
                      {% if block_st.first_button_label != blank %}
                        <a
                          {% if block_st.first_button_link == blank %}
                            role="link" aria-disabled="true"
                          {% else %}
                            href="{{ block_st.first_button_link | default: "#" }}"
                          {% endif %}
                          aria-label="{{ block_st.first_button_label }}"
                          class="relative z-3 inline-flex justify-center no-underline btn-{{ block_st.first_button_type }} p-lg-content-btn"
                        >
                          {{ block_st.first_button_label }}
                        </a>
                      {% endif %}
                      {% if block_st.second_button_label != blank %}
                        <a
                          {% if block_st.second_button_link == blank %}
                            role="link" aria-disabled="true"
                          {% else %}
                            href="{{ block_st.second_button_link | default: "#" }}"
                          {% endif %}
                          aria-label="{{ block_st.second_button_label }}"
                          class="relative z-3 inline-flex no-underline btn-{{ block_st.second_button_type }} px-md-20 py-md-10 p-lg-content-btn"
                        >
                          {{ block_st.second_button_label }}
                        </a>
                      {% endif %}
                    </div>
                  {% endif %}
                </div>
                {% if block_st.image_link != blank %}
                  <a
                    class="absolute inset-0 z-2 hidden block-md"
                    href="{{ block_st.image_link }}"
                    aria-label="{{ block_st.heading }}"
                  ></a>
                {% endif %}
              </div>
            </motion-element>
          {% when 'idea' %}
            {% liquid
              assign image = block.settings.image
            %}
            <motion-element
              data-motion="fade-up-lg"
              {% if scroll_animation != 'slide_in' %}
                hold
              {% endif %}
              data-motion-delay="{{ forloop.index0 | times: 50 }}"
              class="relative block sec__icon-switch-slide {% if scroll_animation != 'none' %} scroll-trigger {{ scroll_animation }}{% endif %} "
              style="
                {%- if scroll_animation != 'none' -%}
                  --animation-order: {{  forloop.index }}
                {% endif %}
              "
            >
              <div
                class=" mb-0 mb-md-0 rounded h-full"
                style="{% if image_ratio == "adapt" and image != blank %} --aspect-ratio: {{ image.aspect_ratio }}  {% else %} {{ style_ratio | strip | strip_newlines }}  {% endif %}"
              >
                {% if image != blank %}
                  {%- assign image_alt = image.alt | escape | default: 'Images' -%}
                  {% render 'responsive-image',
                    type: 'banner',
                    container: section_width,
                    colunm: items_per_row,
                    image: image,
                    image_alt: image_alt
                  %}
                {% else %}
                  {%- render 'placeholder-render' -%}
                {% endif %}
              </div>
              {% assign show_product = false %}
              {% for i in (1..5) %}
                {%- capture product -%}product_{{i}}{%- endcapture -%}
                {%- assign product = block.settings[product] -%}
                {%- if product != blank -%}
                  {% assign show_product = true %}
                {%- endif -%}
              {% endfor %}
              <idea-product
                class="idea-product"
                data-popup="{{ carousel_on_mobile }}"
                data-content-header="{{ 'general.outfit-idea.shop_the_look' | t }}"
              >
                <div class="flex absolute  bottom-0 left-0 mb-30 ms-30 pointer gap-10 justify-center align-center view_idea_product items-center btn btn-idea btn-white">
                  <svg xmlns="http://www.w3.org/2000/svg" width="15" height="16" viewBox="0 0 15 16" fill="none">
                    <use href="#icon-bag" />
                  </svg>
                  <span>{{ 'general.outfit-idea.view_products' | t }}</span>
                </div>
                <div class="idea-product-list absolute z-5  bottom-0 left-0 p-20 bg-white z-1 shadow">
                  <div class="idea-product-list_header flex justify-between items-center mb-20 border-bottom">
                    <h4 class="m-0 fs-18">{{ 'general.outfit-idea.shop_the_look' | t }}</h4>

                    <div class="close pointer">
                      <svg xmlns="http://www.w3.org/2000/svg" width="13" height="14" viewBox="0 0 13 14" fill="none">
                        <use  href="#icon-close" />
                      </svg>
                    </div>
                  </div>
                  <div class="custom-scrollbar">
                    {% for i in (1..5) %}
                      {%- capture product -%}product_{{i}}{%- endcapture -%}
                      {%- assign product = block.settings[product] -%}
                      {%- if product != blank -%}
                        {% render 'product-item',
                          card_product: product,
                          template_enable_rate: true,
                          template_enable_price: true,
                          custom_widths: '240, 120, 60',
                          type: 'list',
                          sizes: '60px'
                        %}
                      {%- endif -%}
                    {% endfor %}
                    {% if show_product == false %}
                      {% for i in (1..2) %}
                        {% render 'product-item',
                          template_enable_rate: true,
                          template_enable_price: true,
                          custom_widths: '240, 120, 60',
                          type: 'list',
                          sizes: '60px'
                        %}
                      {% endfor %}
                    {% endif %}
                  </div>
                </div>
                <template>
                  <div class="content-popup-mobile bls-image-js">
                    {% for i in (1..5) %}
                      {%- capture product -%}product_{{i}}{%- endcapture -%}
                      {%- assign product = block.settings[product] -%}
                      {%- if product != blank -%}
                        {% render 'product-item',
                          card_product: product,
                          template_enable_rate: true,
                          template_enable_price: true,
                          custom_widths: '240, 120, 60',
                          type: 'list',
                          sizes: '60px'
                        %}
                      {%- endif -%}
                    {% endfor %}
                    {% if show_product == false %}
                      {% for i in (1..2) %}
                        {% render 'product-item',
                          template_enable_rate: true,
                          template_enable_price: true,
                          custom_widths: '240, 120, 60',
                          type: 'list',
                          sizes: '60px'
                        %}
                      {% endfor %}
                    {% endif %}
                  </div>
                </template>
              </idea-product>
            </motion-element>
        {% endcase %}
      {% endfor %}
    </carousel-mobile>
  </div>
</div>
{% schema %}
{
  "name": "t:sections.outfit-idea.name",
  "tag": "section",
  "disabled_on": {
    "groups": ["header", "footer", "custom.overlay"]
  },
  "settings": [
    {
      "type": "select",
      "id": "section_width",
      "label": "t:sections.all.section_width.label",
      "default": "fluid_container",
      "options": [
        {
          "value": "container",
          "label": "t:sections.all.section_width.container.label"
        },
        {
          "value": "fluid_container",
          "label": "t:sections.all.section_width.fluid_container.label"
        },
        {
          "value": "stretch_width",
          "label": "t:sections.all.section_width.stretch_width.label"
        },
        {
          "value": "full_width",
          "label": "t:sections.all.section_width.full_width.label"
        }
      ]
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.color_scheme.label",
      "default": "scheme-1"
    },
    {
      "type": "header",
      "content": "t:sections.all.section_header.label"
    },
    {
      "type": "text",
      "id": "heading",
      "label": "t:sections.all.section_header.heading.label",
      "default": "Outfit idea"
    },
    {
      "type": "inline_richtext",
      "id": "description",
      "label": "t:sections.all.section_header.description.label",
      "default": "Best Selling Unmatched design—superior performance and customer satisfaction in one."
    },
    {
      "type": "select",
      "id": "header_size",
      "label": "t:sections.all.section_header.header_size.label",
      "default": "medium",
      "options": [
        {
          "value": "small",
          "label": "t:sections.all.section_header.header_size.small.label"
        },
        {
          "value": "medium",
          "label": "t:sections.all.section_header.header_size.medium.label"
        },
        {
          "value": "large",
          "label": "t:sections.all.section_header.header_size.large.label"
        }
      ]
    },
    {
      "type": "select",
      "id": "header_alignment",
      "label": "t:sections.all.section_header.alignment.label",
      "default": "left",
      "options": [
        {
          "value": "left",
          "label": "t:sections.all.section_header.alignment.left.label"
        },
        {
          "value": "center",
          "label": "t:sections.all.section_header.alignment.center.label"
        },
        {
          "value": "right",
          "label": "t:sections.all.section_header.alignment.right.label"
        }
      ]
    },
    {
      "type": "select",
      "id": "image_ratio",
      "label": "t:sections.all.image.image_ratio.label",
      "options": [
        {
          "value": "adapt",
          "label": "t:sections.all.image.image_ratio.adapt.label"
        },
        {
          "value": "square",
          "label": "t:sections.all.image.image_ratio.square.label"
        },
        {
          "value": "portrait",
          "label": "t:sections.all.image.image_ratio.portrait.label"
        },
        {
          "value": "landscape",
          "label": "t:sections.all.image.image_ratio.landscape.label"
        },
        {
          "value": "custom",
          "label": "t:sections.all.image.image_ratio.custom.label"
        }
      ],
      "default": "adapt"
    },
    {
      "type": "text",
      "id": "custom_ratio",
      "label": "t:sections.all.image.image_ratio.custom_ratio.label"
    },
    {
      "type": "header",
      "content": "t:sections.multi_content.block_settings.label"
    },
    {
      "type": "range",
      "id": "items_per_row",
      "label": "t:sections.all.items.items_per_row.label",
      "min": 2,
      "max": 4,
      "step": 1,
      "default": 2
    },
    {
      "type": "range",
      "id": "column_gap",
      "label": "t:sections.all.items.column_gap.label",
      "min": 0,
      "max": 50,
      "step": 5,
      "default": 30,
      "unit": "px"
    },
    {
      "type": "header",
      "content": "t:sections.all.mobile_options.label"
    },
    {
      "type": "checkbox",
      "id": "carousel_on_mobile",
      "label": "t:sections.all.mobile_options.carousel_on_mobile.label",
      "default": false
    },
    {
      "type": "header",
      "content": "t:sections.all.section_padding.label"
    },
    {
      "type": "range",
      "id": "padding_top",
      "label": "t:sections.all.section_padding.top.label",
      "default": 0,
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px"
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "label": "t:sections.all.section_padding.bottom.label",
      "default": 0,
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px"
    },
    {
      "type": "checkbox",
      "id": "reset_spacing",
      "label": "t:sections.all.section_padding.reset_spacing.label",
      "default": false
    }
  ],
  "blocks": [
    {
      "type": "idea",
      "name": "t:sections.outfit-idea.settings.block.add_idea",
      "limit": 4,
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "t:sections.outfit-idea.settings.block.image"
        },
        {
          "type": "product",
          "id": "product_1",
          "label": "t:sections.outfit-idea.settings.select_product"
        },
        {
          "type": "product",
          "id": "product_2",
          "label": "t:sections.outfit-idea.settings.select_product"
        },
        {
          "type": "product",
          "id": "product_3",
          "label": "t:sections.outfit-idea.settings.select_product"
        },
        {
          "type": "product",
          "id": "product_4",
          "label": "t:sections.outfit-idea.settings.select_product"
        },
        {
          "type": "product",
          "id": "product_5",
          "label": "t:sections.outfit-idea.settings.select_product"
        }
      ]
    },
    {
      "type": "image_with_text_overlay",
      "name": "t:sections.image_with_text_overlay.name",
      "limit": 4,
      "settings": [
        {
          "type": "header",
          "content": "t:sections.all.image.label"
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "t:sections.all.image.label"
        },
        {
          "type": "image_picker",
          "id": "mobile_image",
          "label": "t:sections.all.image.mobile_image.label"
        },
        {
          "type": "url",
          "id": "image_link",
          "label": "t:sections.all.image.link"
        },
        {
          "type": "range",
          "id": "image_overlay_opacity",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "%",
          "label": "t:sections.all.image.overlay_opacity.label",
          "default": 0
        },
        {
          "type": "header",
          "content": "t:sections.all.contents.label"
        },
        {
          "type": "textarea",
          "id": "custom_svg",
          "label": "t:sections.all.contents.custom_svg.label"
        },
        {
          "type": "text",
          "id": "subheading",
          "default": "Subheading",
          "label": "t:sections.all.contents.subheading.label"
        },
        {
          "type": "textarea",
          "id": "heading",
          "label": "t:sections.all.contents.heading.label",
          "default": "Text overlay"
        },
        {
          "type": "richtext",
          "id": "description",
          "label": "t:sections.all.contents.description.label",
          "default": "<p>Description</p>"
        },
        {
          "type": "text",
          "id": "timer",
          "label": "t:sections.image_with_text_overlay.blocks.timer.settings.end_time.label",
          "info": "t:sections.image_with_text_overlay.blocks.timer.settings.end_time.info"
        },
        {
          "type": "text",
          "id": "expired_message",
          "default": "Time expired",
          "label": "t:sections.image_with_text_overlay.blocks.timer.settings.expired_message.label",
          "info": "t:sections.image_with_text_overlay.blocks.timer.settings.expired_message.info"
        },
        {
          "type": "select",
          "id": "style",
          "label": "t:sections.image_with_text_overlay.blocks.timer.settings.style.label",
          "default": "default",
          "options": [
            {
              "value": "default",
              "label": "t:sections.image_with_text_overlay.blocks.timer.settings.style.normal"
            },
            {
              "value": "highlight",
              "label": "t:sections.image_with_text_overlay.blocks.timer.settings.style.highlight"
            }
          ]
        },
        {
          "type": "range",
          "id": "font_size",
          "label": "t:sections.all.content_settings.font_size.label",
          "default": 36,
          "min": 24,
          "max": 60,
          "step": 1,
          "unit": "px"
        },
        {
          "type": "range",
          "id": "spacing_bottom",
          "label": "t:sections.all.content_settings.spacing.spacing_bottom.label",
          "default": 30,
          "min": 0,
          "max": 50,
          "step": 1,
          "unit": "px"
        },
        {
          "type": "text",
          "id": "first_button_label",
          "default": "Button label",
          "label": "t:sections.all.contents.button.first_button_label.label",
          "info": "t:sections.all.contents.button.first_button_label.info"
        },
        {
          "type": "url",
          "id": "first_button_link",
          "label": "t:sections.all.contents.button.first_button_link.label"
        },
        {
          "type": "select",
          "id": "first_button_type",
          "label": "t:sections.all.contents.button.button_type.label",
          "default": "primary",
          "options": [
            {
              "value": "primary",
              "label": "t:sections.all.contents.button.button_type.primary.label"
            },
            {
              "value": "outline",
              "label": "t:sections.all.contents.button.button_type.outline.label"
            },
            {
              "value": "link",
              "label": "t:sections.all.contents.button.button_type.link.label"
            }
          ]
        },
        {
          "type": "text",
          "id": "second_button_label",
          "label": "t:sections.all.contents.button.secondary_button_label.label",
          "info": "t:sections.all.contents.button.secondary_button_label.info"
        },
        {
          "type": "url",
          "id": "second_button_link",
          "label": "t:sections.all.contents.button.secondary_button_link.label"
        },
        {
          "type": "select",
          "id": "second_button_type",
          "label": "t:sections.all.contents.button.button_type.label",
          "default": "primary",
          "options": [
            {
              "value": "primary",
              "label": "t:sections.all.contents.button.button_type.primary.label"
            },
            {
              "value": "outline",
              "label": "t:sections.all.contents.button.button_type.outline.label"
            },
            {
              "value": "link",
              "label": "t:sections.all.contents.button.button_type.link.label"
            }
          ]
        },
        {
          "type": "header",
          "content": "t:sections.all.content_setting.label"
        },
        {
          "type": "select",
          "id": "content_alignment",
          "label": "t:sections.all.content_settings.content_alignment.label",
          "default": "left",
          "options": [
            {
              "value": "left",
              "label": "t:sections.all.content_settings.content_alignment.left.label"
            },
            {
              "value": "center",
              "label": "t:sections.all.content_settings.content_alignment.center.label"
            },
            {
              "value": "right",
              "label": "t:sections.all.content_settings.content_alignment.right.label"
            }
          ]
        },
        {
          "type": "select",
          "id": "content_position",
          "default": "top-left",
          "label": "t:sections.all.content_settings.content_position.label",
          "options": [
            {
              "value": "top-left",
              "label": "t:sections.all.content_settings.content_position.top_left.label"
            },
            {
              "value": "top-center",
              "label": "t:sections.all.content_settings.content_position.top_center.label"
            },
            {
              "value": "top-right",
              "label": "t:sections.all.content_settings.content_position.top_right.label"
            },
            {
              "value": "middle-left",
              "label": "t:sections.all.content_settings.content_position.middle_left.label"
            },
            {
              "value": "middle-center",
              "label": "t:sections.all.content_settings.content_position.middle_center.label"
            },
            {
              "value": "middle-right",
              "label": "t:sections.all.content_settings.content_position.middle_right.label"
            },
            {
              "value": "bottom-left",
              "label": "t:sections.all.content_settings.content_position.bottom_left.label"
            },
            {
              "value": "bottom-center",
              "label": "t:sections.all.content_settings.content_position.bottom_center.label"
            },
            {
              "value": "bottom-right",
              "label": "t:sections.all.content_settings.content_position.bottom_right.label"
            }
          ]
        },
        {
          "type": "range",
          "id": "content_padding_block",
          "min": 10,
          "max": 60,
          "step": 1,
          "unit": "px",
          "default": 30,
          "label": "t:sections.all.content_settings.content_padding_block.label"
        },
        {
          "type": "range",
          "id": "content_padding_inline",
          "min": 10,
          "max": 60,
          "step": 1,
          "unit": "px",
          "default": 30,
          "label": "t:sections.all.content_settings.content_padding_inline.label"
        },
        {
          "type": "header",
          "content": "t:sections.all.mobile_options.label"
        },
        {
          "type": "header",
          "content": "t:sections.all.content_settings.typography.label",
          "info": "t:sections.all.content_setting.info"
        },
        {
          "type": "paragraph",
          "content": "t:sections.all.content_settings.custom_svg.label"
        },
        {
          "type": "range",
          "id": "custom_svg_spacing_bottom",
          "label": "t:sections.all.content_settings.spacing.spacing_bottom.label",
          "default": 10,
          "min": 0,
          "max": 30,
          "step": 1,
          "unit": "px"
        },
        {
          "type": "paragraph",
          "content": "t:sections.all.content_settings.subheading.label"
        },
        {
          "type": "range",
          "id": "subheading_font_size",
          "label": "t:sections.all.content_settings.font_size.label",
          "default": 12,
          "min": 10,
          "max": 30,
          "step": 1,
          "unit": "px"
        },
        {
          "type": "select",
          "id": "subheading_font_weight",
          "label": "t:sections.all.content_settings.font_weight.label",
          "default": "subheading_weight",
          "options": [
            {
              "value": "body_weight",
              "label": "t:sections.all.content_settings.font_weight.body_weight.label"
            },
            {
              "value": "subheading_weight",
              "label": "t:sections.all.content_settings.font_weight.subheading_weight.label"
            },
            {
              "value": "heading_weight",
              "label": "t:sections.all.content_settings.font_weight.heading_weight.label"
            }
          ]
        },
        {
          "type": "range",
          "id": "subheading_spacing_bottom",
          "label": "t:sections.all.content_settings.spacing.spacing_bottom.label",
          "default": 10,
          "min": 0,
          "max": 30,
          "step": 1,
          "unit": "px"
        },
        {
          "type": "paragraph",
          "content": "t:sections.all.content_settings.heading.label"
        },
        {
          "type": "range",
          "id": "heading_font_size",
          "label": "t:sections.all.content_settings.font_size.label",
          "default": 36,
          "min": 10,
          "max": 90,
          "step": 1,
          "unit": "px"
        },
        {
          "type": "select",
          "id": "heading_font_weight",
          "label": "t:sections.all.content_settings.font_weight.label",
          "default": "heading_weight",
          "options": [
            {
              "value": "body_weight",
              "label": "t:sections.all.content_settings.font_weight.body_weight.label"
            },
            {
              "value": "subheading_weight",
              "label": "t:sections.all.content_settings.font_weight.subheading_weight.label"
            },
            {
              "value": "heading_weight",
              "label": "t:sections.all.content_settings.font_weight.heading_weight.label"
            }
          ]
        },
        {
          "type": "checkbox",
          "id": "heading_uppercase",
          "label": "t:sections.all.text_transform.uppercase.label"
        },
        {
          "type": "range",
          "id": "heading_spacing_bottom",
          "label": "t:sections.all.content_settings.spacing.spacing_bottom.label",
          "default": 10,
          "min": 0,
          "max": 30,
          "step": 1,
          "unit": "px"
        },

        {
          "type": "paragraph",
          "content": "t:sections.all.content_settings.description.label"
        },
        {
          "type": "range",
          "id": "des_font_size",
          "label": "t:sections.all.content_settings.font_size.label",
          "default": 14,
          "min": 10,
          "max": 30,
          "step": 1,
          "unit": "px"
        },
        {
          "type": "select",
          "id": "des_font_weight",
          "label": "t:sections.all.content_settings.font_weight.label",
          "default": "body_weight",
          "options": [
            {
              "value": "body_weight",
              "label": "t:sections.all.content_settings.font_weight.body_weight.label"
            },
            {
              "value": "subheading_weight",
              "label": "t:sections.all.content_settings.font_weight.subheading_weight.label"
            },
            {
              "value": "heading_weight",
              "label": "t:sections.all.content_settings.font_weight.heading_weight.label"
            }
          ]
        },
        {
          "type": "range",
          "id": "des_spacing_bottom",
          "label": "t:sections.all.content_settings.spacing.spacing_bottom.label",
          "default": 10,
          "min": 0,
          "max": 40,
          "step": 1,
          "unit": "px"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "t:sections.outfit-idea.name",
      "blocks": [
        {
          "type": "idea"
        },
        {
          "type": "idea"
        }
      ]
    }
  ]
}
{% endschema %}
