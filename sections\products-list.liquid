{%- liquid
  assign section_st = section.settings
  assign section_width = section_st.section_width
  assign color_scheme = section_st.color_scheme
  if section_st.reset_spacing
    assign reset_spacing = ' remove_spacing'
  endif
  assign column_gap = section_st.column_gap
  assign items_per_row = section_st.items_per_row
  assign columns_per_row = section_st.columns_per_row
  assign carousel_on_mobile = section_st.carousel_on_mobile
  assign items_per_row_mobile = section_st.items_per_row_mobile
  assign heading = section_st.heading
  assign description = section_st.description
  assign header_size = ''
  if section_st.header_size == 'small'
    assign header_size = 'h3'
  elsif section_st.header_size == 'large'
    assign header_size = 'h1-size'
  endif
  if section_st.items_per_row_mobile > 1 and section_st.items_per_row_mobile < 2
    assign data_free_scroll = 'true'
  endif
  assign scroll_animation = settings.scroll_animation
  assign block_text = section.blocks | where: "type", "text"
-%}
{%- capture style -%}
  --section-pt: {{ section_st.padding_top }}; --section-pb: {{ section_st.padding_bottom }};
{%- endcapture -%}
{%- capture col_style -%}
--col-desktop: {{ columns_per_row }};--col-number:{{ items_per_row_mobile }};
{% if columns_per_row > 3 %}--col-tablet: 3;{% else %}--col-tablet: {{ columns_per_row }};{% endif %}
{% if column_gap < 15 %}--col-gap: {{ column_gap }}px;{% else %}--col-gap: 15px;--col-gap-desktop: {{  column_gap }}px;{% endif %}= --repeat: {{ columns_per_row }};  --col-mobile: {{ items_per_row_mobile }}
{%- endcapture -%}
{% style %}
  @media screen and (max-width: 767.98px) {
    {% if carousel_on_mobile == true %}
      [data-enable='true'].swiper.grid-cols {
      --col: 1;
    }
      {% endif %}

  }
{% endstyle %}
<div
  class="section{% if section_st.padding_top < 30 %} pt-min{% endif %}{% if section_st.padding_bottom < 30 %} pb-min{% endif %} sec__products-list color-{{ color_scheme }} gradient{{ reset_spacing }}"
  style="{{ style | strip | strip_newlines }}"
>
  <div class="{{ section_width }}">
    {%- if heading != blank or description != blank -%}
      <div class="section__header mb-33 mb-sm-20 text-{{ section_st.header_alignment }}{% if section_st.section_width == 'full_width' %} px-20{% endif %} {% if section_st.show_view_all_button %} flex gap-15 gap-md-30 flex-wrap {% if section_st.header_alignment == 'center'  %} justify-content-{{ section_st.header_alignment }} {% else %} justify-between {% endif %} align-center{% if section_st.header_alignment == 'right' %} flex-row-reverse{% endif %}{% endif %}">
        {%- if section_st.heading != blank -%}
          <motion-element
                data-motion="fade-up-lg"
                data-motion-delay="50"
                {% if scroll_animation != 'slide_in' %}
                  hold
                {% endif %}
            class="block  {% if scroll_animation != 'none' %} scroll-trigger {{ scroll_animation }}{% endif %}"
            style="
              {%- if scroll_animation != 'none' -%}
                --animation-order: 0;
              {% endif %}
            "
          >
          <h2
            class="section__header-heading  heading-letter-spacing {{ header_size  }} mt-0{% if section_st.description != blank %} mb-10{% else %} mb-0{% endif %}"
          >
            {{ section_st.heading }}
          </h2>
        </motion-element>
        {% endif %}
        {%- if section_st.description != blank -%}
          <motion-element
            data-motion="fade-up-lg"
            data-motion-delay="150"
            {% if scroll_animation != 'slide_in' %}
              hold
            {% endif %}
            class="section__header-des block {% if scroll_animation != 'none' %} scroll-trigger {{ scroll_animation }}{% endif %} rich__text-m0"
            style="
              {%- if scroll_animation != 'none' -%}
                --animation-order: 1
              {% endif %}
            "
          >
            {{ section_st.description }}
          </motion-element>
        {% endif %}
        {% if section_st.show_view_all_button and section_st.header_alignment != 'center' %}
          <a
            class="btn_view-all no-underline inline-flex btn-link"
            href="{% if section_st.view_all_button_link != blank %}{{ section_st.view_all_button_link }}{% else %}#{% endif %}"
            role="link"
            aria-label="{{- 'general.view_all' | t -}}"
          >
            {{- 'general.view_all' | t -}}
          </a>
        {% endif %}
      </div>
    {% endif %}
    {% if section.blocks.size > 0 %}
      <grid-custom
        class="{% if block_text.size > 0 %}custom-block{% endif %} products-grid__items grid grid-cols gap swiper{% if section_st.items_per_row_mobile > 1 and section_st.items_per_row_mobile < 2 %} grid_scroll{% endif %}"
        style="{{ col_style | strip | strip_newlines }}"
        data-enable="{{ carousel_on_mobile }}"
        data-section-id="{{ section.id }}"
        data-mobile="{{ items_per_row_mobile }}"
        data-free-scroll="{{ data_free_scroll }}"
        data-spacing="{{ column_gap }}"
      >
        {% for block in section.blocks %}
          {% case block.type %}
            {% when 'collection' %}
              {% assign collection_title = block.settings.collection_title %}
              <motion-element
                    data-motion="fade-up-lg"
                    {% if scroll_animation != 'slide_in' %}
                      hold
                    {% endif %}
                data-motion-delay="{{ forloop.index0 | times: 50 }}"
                class="products-list__column {% if scroll_animation != 'none' %} scroll-trigger {{ scroll_animation }}{% endif %} grid-custom-item"
                {{ block.shopify_attributes }}
                style="
                  {%- if scroll_animation != 'none' -%}
                    --animation-order: {{  forloop.index }}
                  {% endif %}
                "
              >
                {% if section_st.framed != true %}
                  {% if collection_title != blank %}
                    <h3 class="h5 mb-30 mt-0">{{ collection_title }}</h3>
                  {% else %}
                    <h3 class="h5 mb-30 mt-0">{{ block.settings.collection.title }}</h3>
                  {% endif %}
                {% endif %}
                <div class="products-list__column-items flex flex-column gap-30">
                  {% if block.settings.collection.products.size > 0 %}
                      {% for product in block.settings.collection.products limit: items_per_row %}
                        <div class="product-list__column-block-item {% if section_st.framed %} border p-15 rounded{% endif %}">
                          {% render 'product-item',
                            card_product: product,
                            section_id: section.id,
                            template_enable_action: false,
                            template_enable_product_vendor: false,
                            template_enable_rate: true,
                            template_enable_product_short_description: false,
                            template_enable_color_swatches: false,
                            template_enable_price: true,
                            type: 'list'
                          %}
                        </div>
                    {% endfor %}
                  {% else %}
                    {% for i in (1..items_per_row) %}
                      {% render 'product-item',
                        section_id: section.id,
                        template_enable_action: false,
                        template_enable_product_vendor: false,
                        template_enable_rate: true,
                        template_enable_product_short_description: false,
                        template_enable_color_swatches: false,
                        template_enable_price: true,
                        type: 'list'
                      %}
                    {% endfor %}
                  {% endif %}
                </div>
              </motion-element>
            {% when 'image_with_text_overlay' %}
            {{ 'banner.css' | asset_url | stylesheet_tag }}
            {% assign block_st = block.settings %}
              <motion-element
                data-motion="fade-up-lg"
                {% if scroll_animation != 'slide_in' %}
                  hold
                {% endif %}
                data-motion-delay="{{ forloop.index0 | times: 50 }}"
                class="{{ settings.hover_effect }} h-full product-list__banner section__block-inner gradient color-{{ block_st.color_scheme }} flex-important relative {% if scroll_animation != 'none' %} scroll-trigger {{ scroll_animation }}{% endif %} grid-custom-item"
                style="{%- if block_st.image != blank -%}--aspect-ratio: {{ block_st.image.aspect_ratio }};{%- else -%}--aspect-ratio: 5/2;{% endif %}{%- if block_st.mobile_image -%}--aspect-ratio-mb: {{ block_st.mobile_image.aspect_ratio }};{% endif %}"
              >
                <div
                  class="hover-effect banner__media w-full overlay-bg rounded absolute inset-0"
                  style="{%- if block_st.image != blank -%}--aspect-ratio: {{ block_st.image.aspect_ratio }};{%- else -%}--aspect-ratio: 5/2; {% endif %}--overlay-opacity: {{ block_st.image_overlay_opacity }}%; {%- if block_st.mobile_image -%} --aspect-ratio-mb: {{ block_st.mobile_image.aspect_ratio }};{% endif %}"
                >
                  {% liquid
                    assign image = block_st.image
                    assign mobile_image = block_st.mobile_image | default: image
                  %}
                  {%- if image != blank or mobile_image != blank -%}
                    {%- assign image_alt = image.alt | escape | default: 'Images' -%}
                    {% render 'responsive-image',
                      type: 'banner',
                      container: section_width,
                      image: image,
                      image_mobile: mobile_image,
                      image_alt: image_alt
                    %}
                  {%- else -%}
                    {%- render 'placeholder-render' -%}
                  {% endif %}
                  {% if block_st.image_link != blank %}
                    <a
                      class="absolute inset-0 z-2 block hidden-md"
                      aria-label="{{ block_st.heading }}"
                      href="{{ block_st.image_link }}"
                    ></a>
                  {% endif %}
                </div>
                <div
                  class="sec__content w-full flex {{ block_st.content_position }} text-{{ block_st.content_alignment }}{% if section_st.equal_height_adjustment %} align-self-{{ block_st.vertical_align }}{% endif %}"
                  style="--padding-inline: {{ block_st.content_padding_inline }};--padding-block: {{ block_st.content_padding_block }};"
                >
                  <div class="sec__content-inner py-custom px-custom relative{% if block_st.content_padding_inline < 35 %} x-min-value{% endif %}{% if block_st.content_padding_block < 35 %} y-min-value{% endif %}">
                    {%- if block_st.custom_svg != blank -%}
                      <div
                        class="sec__content-custom-svg {% if block_st.subheading_spacing_bottom > 41 %} mb-big{% elsif block_st.subheading_spacing_bottom > 30 %} mb-medium {% else %} mb-custom{% endif %}"
                        style="--space-bottom: {{ block_st.custom_svg_spacing_bottom }}"
                      >
                        {{ block_st.custom_svg }}
                      </div>
                    {% endif %}
                    {%- if block_st.subheading != blank -%}
                      <div
                        class="sec__content-subheading heading-color heading fs-custom {{ block_st.subheading_font_weight }}{% if block_st.subheading_spacing_bottom > 41 %} mb-big{% elsif block_st.subheading_spacing_bottom > 30 %} mb-medium {% else %} mb-custom{% endif %}"
                        style="--font-size: {{ block_st.subheading_font_size }};--space-bottom: {{ block_st.subheading_spacing_bottom }}"
                      >
                        {{ block_st.subheading | escape }}
                      </div>
                    {% endif %}
                    {%- if block_st.heading != blank -%}
                      <h2
                        class="sec__content-heading heading-letter-spacing mt-0{% if block_st.heading_spacing_bottom > 41 %} mb-big{% elsif block_st.heading_spacing_bottom > 30 %} mb-medium {% else %} mb-custom{% endif %}{% if block_st.heading_uppercase %} uppercase{% endif %}{% if block_st.heading_font_size > 41 %} fs-big{% elsif block_st.heading_font_size > 24 %} fs-medium {% else %} fs-custom{% endif %} {{ block_st.heading_font_weight }}"
                        style="--font-size: {{ block_st.heading_font_size }};--space-bottom: {{ block_st.heading_spacing_bottom }}"
                        {{ block.shopify_attributes }}
                      >
                        {{ block_st.heading }}
                      </h2>
                    {% endif %}
                    {%- if block_st.description != blank -%}
                      <div
                        class="sec__content-des rich__text-m0 {% if block_st.des_spacing_bottom > 41 %} mb-big{% elsif block_st.des_spacing_bottom > 30 %} mb-medium {% else %} mb-custom{% endif %}{% if block_st.des_font_size > 24 %} fs-medium {% else %} fs-custom{% endif %} {{ block_st.des_font_weight }}"
                        style="--font-size: {{ block_st.des_font_size }};--space-bottom: {{ block_st.des_spacing_bottom }}"
                        {{ block.shopify_attributes }}
                      >
                        {{ block_st.description }}
                      </div>
                    {% endif %}
                    {% if block_st.first_button_label != blank or block_st.second_button_label != blank %}
                      <div class="sec__content-btn align-center flex flex-wrap gap-15 justify-content-{{ block_st.content_alignment }}">
                        {% if block_st.first_button_label != blank %}
                          <a
                            {% if block_st.first_button_link == blank %}
                              role="link" aria-disabled="true"
                            {% else %}
                              href="{{ block_st.first_button_link | default: "#" }}"
                            {% endif %}
                            aria-label="{{ block_st.first_button_label }}"
                            class="relative z-3 inline-flex no-underline btn-{{ block_st.first_button_type }} px-md-20 py-md-10 p-lg-content-btn"
                          >
                            {{ block_st.first_button_label }}
                          </a>
                        {% endif %}
                        {% if block_st.second_button_label != blank %}
                          <a
                            {% if block_st.second_button_link == blank %}
                              role="link" aria-disabled="true"
                            {% else %}
                              href="{{ block_st.second_button_link | default: "#" }}"
                            {% endif %}
                            aria-label="{{ block_st.second_button_label }}"
                            class="relative z-3 inline-flex no-underline btn-{{ block_st.second_button_type }} px-md-20 py-md-10 p-lg-content-btn"
                          >
                            {{ block_st.second_button_label }}
                          </a>
                        {% endif %}
                      </div>
                    {% endif %}
                  </div>
                  {% if block_st.image_link != blank %}
                    <a
                      class="absolute inset-0 z-2 hidden block-md"
                      href="{{ block_st.image_link }}"
                      aria-label="{{ block_st.heading }}"
                    ></a>
                  {% endif %}
                </div>
              </motion-element>
          {% endcase %}
        {% endfor %}
      </grid-custom>
    {% endif %}
  </div>
</div>

{% schema %}
{
  "name": "t:sections.products-list.name",
  "tag": "section",
  "disabled_on": {
    "groups": ["header", "footer", "custom.overlay"]
  },
  "settings": [
    {
      "type": "select",
      "id": "section_width",
      "label": "t:sections.all.section_width.label",
      "default": "fluid_container",
      "options": [
        {
          "value": "container",
          "label": "t:sections.all.section_width.container.label"
        },
        {
          "value": "fluid_container",
          "label": "t:sections.all.section_width.fluid_container.label"
        },
        {
          "value": "stretch_width",
          "label": "t:sections.all.section_width.stretch_width.label"
        },
        {
          "value": "full_width",
          "label": "t:sections.all.section_width.full_width.label"
        }
      ]
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.color_scheme.label",
      "default": "scheme-1"
    },
    {
      "type": "header",
      "content": "t:sections.all.section_header.label"
    },
    {
      "type": "text",
      "id": "heading",
      "label": "t:sections.all.section_header.heading.label"
    },
    {
      "type": "richtext",
      "id": "description",
      "label": "t:sections.all.section_header.description.label"
    },
    {
      "type": "checkbox",
      "id": "show_view_all_button",
      "label": "t:sections.all.section_header.show_view_all_button.label"
    },
    {
      "type": "url",
      "id": "view_all_button_link",
      "label": "t:sections.all.section_header.show_view_all_button.label_link"
    },
    {
      "type": "select",
      "id": "header_size",
      "label": "t:sections.all.section_header.header_size.label",
      "default": "medium",
      "options": [
        {
          "value": "small",
          "label": "t:sections.all.section_header.header_size.small.label"
        },
        {
          "value": "medium",
          "label": "t:sections.all.section_header.header_size.medium.label"
        },
        {
          "value": "large",
          "label": "t:sections.all.section_header.header_size.large.label"
        }
      ]
    },
    {
      "type": "select",
      "id": "header_alignment",
      "label": "t:sections.all.section_header.alignment.label",
      "default": "left",
      "options": [
        {
          "value": "left",
          "label": "t:sections.all.section_header.alignment.left.label"
        },
        {
          "value": "center",
          "label": "t:sections.all.section_header.alignment.center.label"
        },
        {
          "value": "right",
          "label": "t:sections.all.section_header.alignment.right.label"
        }
      ]
    },
    {
      "type": "header",
      "content": "t:sections.products-list.settings.products.header"
    },
    {
      "type": "range",
      "id": "columns_per_row",
      "label": "t:sections.products-list.settings.products.columns_per_row",
      "min": 1,
      "max": 4,
      "step": 1,
      "default": 3
    },
    {
      "type": "range",
      "id": "items_per_row",
      "label": "t:sections.all.items.items_per_row.label",
      "min": 1,
      "max": 4,
      "step": 1,
      "default": 3
    },
    {
      "type": "range",
      "id": "column_gap",
      "label": "t:sections.all.items.column_gap.label",
      "min": 0,
      "max": 50,
      "step": 5,
      "default": 30,
      "unit": "px"
    },
    {
      "type": "checkbox",
      "id": "framed",
      "label": "t:sections.testimonials.settings.testimonials_setting.framed",
      "default": false
    },
    {
      "type": "header",
      "content": "t:sections.all.mobile_options.label"
    },
    {
      "type": "checkbox",
      "id": "carousel_on_mobile",
      "label": "t:sections.all.mobile_options.carousel_on_mobile.label",
      "default": false
    },
    {
      "type": "range",
      "id": "items_per_row_mobile",
      "label": "t:sections.all.items.items_per_row.label",
      "min": 1,
      "max": 2,
      "step": 0.5,
      "default": 1
    },
    {
      "type": "header",
      "content": "t:sections.all.section_padding.label"
    },
    {
      "type": "range",
      "id": "padding_top",
      "label": "t:sections.all.section_padding.top.label",
      "default": 0,
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px"
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "label": "t:sections.all.section_padding.bottom.label",
      "default": 0,
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px"
    },
    {
      "type": "checkbox",
      "id": "reset_spacing",
      "label": "t:sections.all.section_padding.reset_spacing.label",
      "default": false
    }
  ],
  "blocks": [
    {
      "type": "collection",
      "name": "t:sections.products-list.blocks.name",
      "settings": [
        {
          "type": "collection",
          "id": "collection",
          "label": "t:sections.products-list.blocks.collection"
        },
        {
          "type": "text",
          "id": "collection_title",
          "label": "t:sections.products-list.blocks.collection_title.label",
          "info": "t:sections.products-list.blocks.collection_title.info"
        }
      ]
    },
    {
      "type": "image_with_text_overlay",
      "name": "t:sections.image_with_text_overlay.name",
      "limit": 4,
      "settings": [
        {
          "type": "color_scheme",
          "id": "color_scheme",
          "label": "t:sections.all.color_scheme.label",
          "default": "scheme-1"
        },
        {
          "type": "header",
          "content": "t:sections.all.image.label"
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "t:sections.all.image.label"
        },
        {
          "type": "image_picker",
          "id": "mobile_image",
          "label": "t:sections.all.image.mobile_image.label"
        },
        {
          "type": "url",
          "id": "image_link",
          "label": "t:sections.all.image.link"
        },
        {
          "type": "range",
          "id": "image_overlay_opacity",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "%",
          "label": "t:sections.all.image.overlay_opacity.label",
          "default": 0
        },
        {
          "type": "header",
          "content": "t:sections.all.contents.label"
        },
        {
          "type": "textarea",
          "id": "custom_svg",
          "label": "t:sections.all.contents.custom_svg.label"
        },
        {
          "type": "text",
          "id": "subheading",
          "default": "Subheading",
          "label": "t:sections.all.contents.subheading.label"
        },
        {
          "type": "textarea",
          "id": "heading",
          "label": "t:sections.all.contents.heading.label",
          "default": "Text overlay"
        },
        {
          "type": "html",
          "id": "description",
          "label": "t:sections.all.contents.description.label"
        },
        {
          "type": "text",
          "id": "first_button_label",
          "default": "Button label",
          "label": "t:sections.all.contents.button.first_button_label.label",
          "info": "t:sections.all.contents.button.first_button_label.info"
        },
        {
          "type": "url",
          "id": "first_button_link",
          "label": "t:sections.all.contents.button.first_button_link.label"
        },
        {
          "type": "select",
          "id": "first_button_type",
          "label": "t:sections.all.contents.button.button_type.label",
          "default": "primary",
          "options": [
            {
              "value": "primary",
              "label": "t:sections.all.contents.button.button_type.primary.label"
            },
            {
              "value": "outline",
              "label": "t:sections.all.contents.button.button_type.outline.label"
            },
            {
              "value": "link",
              "label": "t:sections.all.contents.button.button_type.link.label"
            }
          ]
        },
        {
          "type": "text",
          "id": "second_button_label",
          "label": "t:sections.all.contents.button.secondary_button_label.label",
          "info": "t:sections.all.contents.button.secondary_button_label.info"
        },
        {
          "type": "url",
          "id": "second_button_link",
          "label": "t:sections.all.contents.button.secondary_button_link.label"
        },
        {
          "type": "select",
          "id": "second_button_type",
          "label": "t:sections.all.contents.button.button_type.label",
          "default": "primary",
          "options": [
            {
              "value": "primary",
              "label": "t:sections.all.contents.button.button_type.primary.label"
            },
            {
              "value": "outline",
              "label": "t:sections.all.contents.button.button_type.outline.label"
            },
            {
              "value": "link",
              "label": "t:sections.all.contents.button.button_type.link.label"
            }
          ]
        },
        {
          "type": "header",
          "content": "t:sections.all.content_setting.label"
        },
        {
          "type": "select",
          "id": "content_alignment",
          "label": "t:sections.all.content_settings.content_alignment.label",
          "default": "left",
          "options": [
            {
              "value": "left",
              "label": "t:sections.all.content_settings.content_alignment.left.label"
            },
            {
              "value": "center",
              "label": "t:sections.all.content_settings.content_alignment.center.label"
            },
            {
              "value": "right",
              "label": "t:sections.all.content_settings.content_alignment.right.label"
            }
          ]
        },
        {
          "type": "select",
          "id": "content_position",
          "default": "top-left",
          "label": "t:sections.all.content_settings.content_position.label",
          "options": [
            {
              "value": "top-left",
              "label": "t:sections.all.content_settings.content_position.top_left.label"
            },
            {
              "value": "top-center",
              "label": "t:sections.all.content_settings.content_position.top_center.label"
            },
            {
              "value": "top-right",
              "label": "t:sections.all.content_settings.content_position.top_right.label"
            },
            {
              "value": "middle-left",
              "label": "t:sections.all.content_settings.content_position.middle_left.label"
            },
            {
              "value": "middle-center",
              "label": "t:sections.all.content_settings.content_position.middle_center.label"
            },
            {
              "value": "middle-right",
              "label": "t:sections.all.content_settings.content_position.middle_right.label"
            },
            {
              "value": "bottom-left",
              "label": "t:sections.all.content_settings.content_position.bottom_left.label"
            },
            {
              "value": "bottom-center",
              "label": "t:sections.all.content_settings.content_position.bottom_center.label"
            },
            {
              "value": "bottom-right",
              "label": "t:sections.all.content_settings.content_position.bottom_right.label"
            }
          ]
        },
        {
          "type": "range",
          "id": "content_padding_block",
          "min": 10,
          "max": 60,
          "step": 1,
          "unit": "px",
          "default": 30,
          "label": "t:sections.all.content_settings.content_padding_block.label"
        },
        {
          "type": "range",
          "id": "content_padding_inline",
          "min": 10,
          "max": 60,
          "step": 1,
          "unit": "px",
          "default": 30,
          "label": "t:sections.all.content_settings.content_padding_inline.label"
        },
        {
          "type": "header",
          "content": "t:sections.all.content_settings.typography.label",
          "info": "t:sections.all.content_setting.info"
        },
        {
          "type": "paragraph",
          "content": "t:sections.all.content_settings.custom_svg.label"
        },
        {
          "type": "range",
          "id": "custom_svg_spacing_bottom",
          "label": "t:sections.all.content_settings.spacing.spacing_bottom.label",
          "default": 10,
          "min": 0,
          "max": 30,
          "step": 1,
          "unit": "px"
        },
        {
          "type": "paragraph",
          "content": "t:sections.all.content_settings.subheading.label"
        },
        {
          "type": "range",
          "id": "subheading_font_size",
          "label": "t:sections.all.content_settings.font_size.label",
          "default": 12,
          "min": 10,
          "max": 30,
          "step": 1,
          "unit": "px"
        },
        {
          "type": "select",
          "id": "subheading_font_weight",
          "label": "t:sections.all.content_settings.font_weight.label",
          "default": "subheading_weight",
          "options": [
            {
              "value": "body_weight",
              "label": "t:sections.all.content_settings.font_weight.body_weight.label"
            },
            {
              "value": "subheading_weight",
              "label": "t:sections.all.content_settings.font_weight.subheading_weight.label"
            },
            {
              "value": "heading_weight",
              "label": "t:sections.all.content_settings.font_weight.heading_weight.label"
            }
          ]
        },
        {
          "type": "range",
          "id": "subheading_spacing_bottom",
          "label": "t:sections.all.content_settings.spacing.spacing_bottom.label",
          "default": 10,
          "min": 0,
          "max": 30,
          "step": 1,
          "unit": "px"
        },
        {
          "type": "paragraph",
          "content": "t:sections.all.content_settings.heading.label"
        },
        {
          "type": "range",
          "id": "heading_font_size",
          "label": "t:sections.all.content_settings.font_size.label",
          "default": 36,
          "min": 10,
          "max": 90,
          "step": 1,
          "unit": "px"
        },
        {
          "type": "select",
          "id": "heading_font_weight",
          "label": "t:sections.all.content_settings.font_weight.label",
          "default": "heading_weight",
          "options": [
            {
              "value": "body_weight",
              "label": "t:sections.all.content_settings.font_weight.body_weight.label"
            },
            {
              "value": "subheading_weight",
              "label": "t:sections.all.content_settings.font_weight.subheading_weight.label"
            },
            {
              "value": "heading_weight",
              "label": "t:sections.all.content_settings.font_weight.heading_weight.label"
            }
          ]
        },
        {
          "type": "checkbox",
          "id": "heading_uppercase",
          "label": "t:sections.all.text_transform.uppercase.label"
        },
        {
          "type": "range",
          "id": "heading_spacing_bottom",
          "label": "t:sections.all.content_settings.spacing.spacing_bottom.label",
          "default": 10,
          "min": 0,
          "max": 30,
          "step": 1,
          "unit": "px"
        },
        {
          "type": "paragraph",
          "content": "t:sections.all.content_settings.description.label"
        },
        {
          "type": "range",
          "id": "des_font_size",
          "label": "t:sections.all.content_settings.font_size.label",
          "default": 14,
          "min": 10,
          "max": 30,
          "step": 1,
          "unit": "px"
        },
        {
          "type": "select",
          "id": "des_font_weight",
          "label": "t:sections.all.content_settings.font_weight.label",
          "default": "body_weight",
          "options": [
            {
              "value": "body_weight",
              "label": "t:sections.all.content_settings.font_weight.body_weight.label"
            },
            {
              "value": "subheading_weight",
              "label": "t:sections.all.content_settings.font_weight.subheading_weight.label"
            },
            {
              "value": "heading_weight",
              "label": "t:sections.all.content_settings.font_weight.heading_weight.label"
            }
          ]
        },
        {
          "type": "range",
          "id": "des_spacing_bottom",
          "label": "t:sections.all.content_settings.spacing.spacing_bottom.label",
          "default": 10,
          "min": 0,
          "max": 40,
          "step": 1,
          "unit": "px"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "t:sections.products-list.name",
      "blocks": [
        {
          "type": "collection"
        },
        {
          "type": "collection"
        },
        {
          "type": "collection"
        }
      ]
    }
  ]
}
{% endschema %}