.collection-item__name.btn-white {
  --btn-padding-x: 2rem;
  width: 18rem;
  max-width: 100%;
}
.collection-item__media.morden:after {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  border-radius: 10px;
  background: linear-gradient(
    180deg,
    rgba(0, 0, 0, 0) 25%,
    rgba(0, 0, 0, 0.4) 100%
  );
  opacity: 0.75;
  pointer-events: none;
  z-index: 2;
  border-radius: var(--rounded-radius, 0);
}
.collection-item__information.absolute .collection-item__name-inner {
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
}
.collection-item__count.absolute {
  right: -16px;
}
@media (max-width: 575.98px) {
  [data-mobile="3"]
    .collection-item__information.absolute
    .collection-item__name {
    --btn-padding-x: 1.5rem;
  }
  [data-mobile="3"]
    .collection-item__information.absolute
    .collection-item__name-inner {
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
  }
}
