{%- liquid
  assign section_st = section.settings
  assign section_width = section_st.section_width
  assign color_scheme = section_st.color_scheme
  if section_st.reset_spacing
    assign reset_spacing = ' remove_spacing'
  endif
  assign column_gap = section_st.column_gap
  assign items_to_show = section_st.items_to_show
  assign heading = section_st.heading
  assign description = section_st.description
  assign header_size = ''
  if section_st.header_size == 'small'
    assign header_size = 'h3'
  elsif section_st.header_size == 'large'
    assign header_size = 'h1-size'
  endif
  if section_st.items_per_row_mobile > 1 and section_st.items_per_row_mobile < 2
    assign data_free_scroll = 'true'
  endif
  assign reveal = section_st.reveal
  assign items_per_row = section_st.items_per_row
  assign items_per_row_mobile = section_st.items_per_row_mobile
  assign scroll_animation = settings.scroll_animation
-%}
{%- capture style -%}
    --section-pt: {{ section_st.padding_top }}; --section-pb: {{ section_st.padding_bottom }};
  {%- endcapture -%}
{%- capture col_style -%}
--col-desktop: {{ items_per_row }};--col-number: {{ items_per_row_mobile }};{% if items_per_row > 3 %}--col-tablet: 3;{% else %}--col-tablet: {{ items_per_row }};{% endif %}
{% if column_gap < 15 %}--col-gap: {{ column_gap }}px;{% else %}--col-gap: 15px;--col-gap-desktop: {{  column_gap }}px;{% endif %}--repeat: {{ items_to_show }}; --col-mobile: {{ items_per_row_mobile }}
  {%- endcapture -%}
<div
  class="section{% if section_st.padding_top < 30 %} pt-min{% endif %}{% if section_st.padding_bottom < 30 %} pb-min{% endif %} sec__related-product color-{{ color_scheme }} gradient{{ reset_spacing }}{% if reveal %} overflow-hidden{% endif %}"
  style="{{ style | strip | strip_newlines }}"
>
  <div class="{{ section_width }}">
    {%- if heading != blank or description != blank -%}
      <div class="section__header mb-33 mb-sm-20 text-{{ section_st.header_alignment }}{% if section_st.section_width == 'full_width' %} px-20{% endif %}">
        {%- if heading != blank or description != blank -%}
          <div class="secion__header-inner">
            {%- if section_st.heading != blank -%}
              <motion-element
                data-motion="fade-up-lg"
                {% if scroll_animation != 'slide_in' %}
                  hold
                {% endif %}
                data-motion-delay="50"
                class="block {% if scroll_animation != 'none' %} scroll-trigger {{ scroll_animation }}{% endif %}"
                style="
                  {%- if scroll_animation != 'none' -%}
                    --animation-order: 0;
                  {% endif %}
                "
              >
                <h2 class="section__header-heading heading-letter-spacing {{ header_size  }} mt-0{% if section_st.description != blank %} mb-10{% else %} mb-0{% endif %}">
                  {{ section_st.heading }}
                </h2>
              </motion-element>
            {% endif %}
            {%- if section_st.description != blank -%}
              <motion-element
                data-motion="fade-up-lg"
                data-motion-delay="150"
                {% if scroll_animation != 'slide_in' %}
                  hold
                {% endif -%}
                class="section__header-des block rich__text-m0"
                style="
                  {%- if scroll_animation != 'none' -%}
                    --animation-order: 1
                  {% endif %}
                "
              >
                {{ section_st.description }}
              </motion-element>
            {% endif %}
          </div>
        {% endif %}
      </div>
    {% endif %}
    <product-recommendations
      data-section-id="{{ section.id }}"
      {% if data_free_scroll != blank %}
        data-free-scroll="{{ data_free_scroll }}"
      {% endif %}
      style="{{ col_style | strip | strip_newlines }}"
      class="products-grid__items flex flex-cols{% if column_gap < 31 %} row-gap-20 row-gap-md-30{% endif %} flex-wrap {% if section_st.items_per_row_mobile > 1 and section_st.items_per_row_mobile < 2 %} grid_scroll{% endif %}"
      data-url="{{ routes.product_recommendations_url }}?section_id={{ section.id }}&product_id={{ product.id }}&limit={{ items_to_show }}"
      data-arrow-centerimage="1"
    >
      {% if recommendations.performed and recommendations.products_count > 0 %}
        {% for recommendation in recommendations.products %}
          {% render 'product-item',
            card_product: recommendation,
            section_id: section.id,
            is_compare: false,
            template_enable_action: true,
            template_enable_product_vendor: true,
            template_enable_rate: true,
            template_enable_product_short_description: false,
            template_enable_color_swatches: true,
            template_enable_price: true,
            template_enable_add_cart: true,
            template_enable_product_badges: true,
            indexFor: forloop.index,
            scroll_animation: scroll_animation
          %}
        {% endfor %}
      {% else %}
        <p>{{ 'sections.related_products.no_product' | t }}</p>
      {% endif %}
    </product-recommendations>
  </div>
</div>

{% schema %}
{
  "name": "t:sections.related-products.name",
  "tag": "section",
  "settings": [
    {
      "type": "select",
      "id": "section_width",
      "label": "t:sections.all.section_width.label",
      "default": "fluid_container",
      "options": [
        {
          "value": "container",
          "label": "t:sections.all.section_width.container.label"
        },
        {
          "value": "fluid_container",
          "label": "t:sections.all.section_width.fluid_container.label"
        },
        {
          "value": "stretch_width",
          "label": "t:sections.all.section_width.stretch_width.label"
        },
        {
          "value": "full_width",
          "label": "t:sections.all.section_width.full_width.label"
        }
      ]
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.color_scheme.label",
      "default": "scheme-1"
    },
    {
      "type": "header",
      "content": "t:sections.all.section_header.label"
    },
    {
      "type": "text",
      "id": "heading",
      "label": "t:sections.all.section_header.heading.label",
      "default": "Related products"
    },
    {
      "type": "inline_richtext",
      "id": "description",
      "label": "t:sections.all.section_header.description.label"
    },
    {
      "type": "select",
      "id": "header_size",
      "label": "t:sections.all.section_header.header_size.label",
      "default": "medium",
      "options": [
        {
          "value": "small",
          "label": "t:sections.all.section_header.header_size.small.label"
        },
        {
          "value": "medium",
          "label": "t:sections.all.section_header.header_size.medium.label"
        },
        {
          "value": "large",
          "label": "t:sections.all.section_header.header_size.large.label"
        }
      ]
    },
    {
      "type": "select",
      "id": "header_alignment",
      "label": "t:sections.all.section_header.alignment.label",
      "default": "left",
      "options": [
        {
          "value": "left",
          "label": "t:sections.all.section_header.alignment.left.label"
        },
        {
          "value": "center",
          "label": "t:sections.all.section_header.alignment.center.label"
        },
        {
          "value": "right",
          "label": "t:sections.all.section_header.alignment.right.label"
        }
      ]
    },
    {
      "type": "header",
      "content": "t:sections.related-products.settings.products.header"
    },
    {
      "type": "range",
      "id": "items_to_show",
      "label": "t:sections.all.items.items_to_show.label",
      "min": 2,
      "max": 25,
      "step": 1,
      "default": 8
    },
    {
      "type": "range",
      "id": "items_per_row",
      "label": "t:sections.all.items.items_per_row.label",
      "min": 1,
      "max": 5,
      "step": 1,
      "default": 4
    },
    {
      "type": "range",
      "id": "column_gap",
      "label": "t:sections.all.items.column_gap.label",
      "min": 0,
      "max": 50,
      "step": 5,
      "default": 30,
      "unit": "px"
    },
    {
      "type": "header",
      "content": "t:sections.all.mobile_options.label"
    },
    {
      "type": "range",
      "id": "items_per_row_mobile",
      "label": "t:sections.all.items.items_per_row.label",
      "min": 1,
      "max": 2,
      "step": 0.5,
      "default": 1
    },
    {
      "type": "header",
      "content": "t:sections.all.section_padding.label"
    },
    {
      "type": "range",
      "id": "padding_top",
      "label": "t:sections.all.section_padding.top.label",
      "default": 0,
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px"
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "label": "t:sections.all.section_padding.bottom.label",
      "default": 0,
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px"
    },
    {
      "type": "checkbox",
      "id": "reset_spacing",
      "label": "t:sections.all.section_padding.reset_spacing.label",
      "default": false
    }
  ]
}
{% endschema %}
