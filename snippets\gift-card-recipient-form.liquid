 {% assign enable_skeleton_loading = section.settings.enable_skeleton_loading  %} 
<div class="customer mb-10  {% if enable_skeleton_loading %} skeleton-loading{% endif %}">
  {%- assign gift_card_recipient_control_flag = 'properties[__shopify_send_gift_card_to_recipient]' -%}
  <script src="{{ 'recipient-form.js' | asset_url }}" defer="defer"></script>
  <recipient-form
    class="recipient-form mb-20"
    data-section-id="{{ section.id }}"
    data-product-variant-id="{{ product.selected_or_first_available_variant.id }}"
  >
    <div class="checkbox-group mb-20 lh-1 relative " style="width: fit-content;">
      <input
        id="Recipient-checkbox-{{ section.id }}"
        type="checkbox"
        name="{{ gift_card_recipient_control_flag }}"
        disabled
        class="input z-10  m-0 opacity-0 absolute inset-0 pointer inset-0"
      >
      <span class="checkmark"></span>
      <label
        for="Recipient-checkbox-{{ section.id }}"
        class="pointer"
      >
        {{- 'recipient.form.checkbox' | t -}}
      </label>
    </div>
    <div
      class="product-form__recipient-error-message-wrapper mt-10"
      role="alert"
      {% unless form.errors %}
        hidden
      {% endunless %}
    >
      {{ 'templates.contact.form.error_heading' | t }}
      <ul>
        {%- if form.errors -%}
          {%- for field in form.errors -%}
            <li>
              {%- if field == 'form' -%}
                {{ form.errors.messages[field] }}
              {%- else -%}
                <a href="#Recipient-{{ field }}-{{ section.id }}">
                  {{ form.errors.messages[field] }}
                </a>
              {%- endif -%}
            </li>
          {%- endfor -%}
        {%- endif -%}
      </ul>
    </div>
    <p
      id="Recipient-fields-live-region-{{ section.id }}"
      class="visually-hidden"
      role="status"
    ></p>
    <div class="recipient-fields mt-10" style="display: none;">
      <div class="recipient-fields__field mb-10">
        <div class="field">
          <input
            class="form-input form-input-placeholder w-full"
            id="Recipient-email-{{ section.id }}"
            type="email"
            placeholder="{{ 'recipient.form.email_label' | t }}"
            name="properties[Recipient email]"
            value="{{ form.email }}"
            {% if form.errors contains 'email' %}
              aria-invalid="true"
              aria-describedby="RecipientForm-email-error-{{ section.id }}"
            {% endif %}
          >
          <label class="field__label" hidden for="Recipient-email-{{ section.id }}">
            <span class="recipient-email-label optional">
              {{- 'recipient.form.email_label_optional_for_no_js_behavior' | t -}}
            </span>
          </label>
        </div>
        <div
          id="RecipientForm-email-error-{{ section.id }}"
          class="form__message{% unless form.errors contains 'email' %} hidden{% endunless %}"
        >
          {%- if form.errors contains 'email' -%}
            {{ form.errors.messages.email }}.
          {%- endif -%}
        </div>
      </div>
      <div class="recipient-fields__field mb-10">
        <div class="field">
          <input
            class="form-input form-input-placeholder w-full"
            autocomplete="name"
            type="text"
            id="Recipient-name-{{ section.id }}"
            name="properties[Recipient name]"
            placeholder="{{ 'recipient.form.name_label' | t }}"
            value="{{ form.name }}"
            {% if form.errors contains 'name' %}
              aria-invalid="true"
              aria-describedby="RecipientForm-name-error-{{ section.id }}"
            {% endif %}
          >
          <label class="field__label" hidden for="Recipient-name-{{ section.id }}">
            {{- 'recipient.form.name_label' | t -}}
          </label>
        </div>

        <div
          id="RecipientForm-name-error-{{ section.id }}"
          class="form__message{% unless form.errors contains 'name' %} hidden{% endunless %}"
        >
          {%- if form.errors contains 'name' -%}
            {{ form.errors.messages.name }}.
          {%- endif -%}
        </div>
      </div>

      <div class="recipient-fields__field text-area mb-10">
        {%- assign max_chars_message = 200 -%}
        {%- assign max_chars_message_rendered = 'recipient.form.max_characters' | t: max_chars: max_chars_message -%}
        {%- assign message_label_rendered = 'recipient.form.message_label' | t -%}
        <div class="field-input">
          <textarea
            rows="10"
            id="Recipient-message-{{ section.id }}"
            class="form-input form-input-placeholder w-full text-area"
            name="properties[Message]"
            maxlength="{{ max_chars_message }}"
            placeholder="{{ 'recipient.form.message_label' | t }}"
            aria-label="{{ message_label_rendered }} {{ max_chars_message_rendered }}"
            {% if form.errors contains 'message' %}
              aria-invalid="true"
              aria-describedby="RecipientForm-message-error-{{ section.id }}"
            {% endif %}
          >{{ form.message }}</textarea>
          <label class="form__label field__label" hidden for="Recipient-message-{{ section.id }}">
            {{ message_label_rendered }}
          </label>
        </div>
        <span class="recipient-form-field-label">{{ max_chars_message_rendered }}</span>
        <div
          id="RecipientForm-message-error-{{ section.id }}"
          class="form__message{% unless form.errors contains 'message' %} hidden{% endunless %}"
        >
          {%- if form.errors contains 'message' -%}
            {{ form.errors.messages.message }}.
          {%- endif -%}
        </div>
      </div>

      <div class="recipient-fields__field">
        <div class="field">
          <input
            class="form-input form-input-placeholder w-full send-on"
            autocomplete="send_on"
            type="date"
            id="Recipient-send-on-{{ section.id }}"
            name="properties[Send on]"
            placeholder="{{ 'recipient.form.send_on' | t }}"
            pattern="\d{4}-\d{2}-\d{2}"
            value="{{ form.send_on }}"
            {% if form.errors contains 'send_on' %}
              aria-invalid="true"
              aria-describedby="RecipientForm-send_on-error-{{ section.id }}"
            {% endif %}
          >
          <label class="form__label field__label recipient-form-field-label" for="Recipient-send-on-{{ section.id }}">
            {{ 'recipient.form.send_on_label' | t }}
          </label>
        </div>

        <div
          id="RecipientForm-send_on-error-{{ section.id }}"
          class="form__message{% unless form.errors contains 'send_on' %} hidden{% endunless %}"
        >
          {%- if form.errors contains 'send_on' -%}
            {{ form.errors.messages.send_on }}.
          {%- endif -%}
        </div>
      </div>
    </div>
    <input
      type="hidden"
      name="{{ gift_card_recipient_control_flag }}"
      value="if_present"
      id="Recipient-control-{{ section.id }}"
    >
    <input
      type="hidden"
      name="properties[__shopify_offset]"
      value=""
      id="Recipient-timezone-offset-{{ section.id }}"
      disabled
    >
  </recipient-form>
</div>
