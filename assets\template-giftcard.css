

main,header,.gift-card__buttons.no-print{
    display: flex;
    align-items: center;
    flex-direction: column;
    gap:20px;
}
main,.gift-card__buttons.no-print{
     gap: 0px;
}
header  h2{ 
    font-family: var(--font-heading-family);
    font-size: 20px;
    margin:0;
}
header .text{
    font-family: var(--font-heading-family);
    font-size: clamp(2.8rem,4.5vw,4rem);
     margin: 0;
     font-weight: 600;
}
.gift-card__price h1{ 
   font-size: 20px;
   margin:0;
   font-family: var(--font-heading-family);
}
.gift-card__price{
     margin-bottom: 10px;
}
.gift-card__copy-button{    
     font-size: 20px;
     font-weight: 600;
     margin: 0;
     cursor: pointer;
     margin: 20px 0px;
     position: relative;
    font-family: var(--font-heading-family);
    padding-bottom: 2px;
  margin: 0;
}


#gift-card-code{
    margin: 0;
    margin: 10px 0px;
}
.button{
    font-family: var(--font-heading-family);
    padding: 15px 15px;
    border: 1px solid #e5e8ec;
    background: #ffffff;
    color: #555555;
    border-radius: 30px;
    font-weight: 500;
    font-size: 12px;
    max-width: 100%;
    width: 570px;
    text-align: center;
      font-weight: 600;
      text-transform: uppercase;
      cursor: pointer;
      margin-top: 10px;
}
.gift-card__qr-code{
     margin: 20px 0px;
}
main{
    margin-bottom: 100px;
}
.gift-card__copy-success{
    font-family: var(--font-heading-family);
    margin-bottom: 5px;
}
.success::before{
    background: url("data:image/svg+xml,%3Csvg width='18' height='18' viewBox='0 0 18 18' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M6.0332 8.99172L8.00502 10.9717L11.9568 7.01172' stroke='%23008A00' stroke-width='1.3' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M7.97336 1.17845C8.5379 0.695724 9.46245 0.695724 10.0352 1.17845L11.3279 2.29118C11.5734 2.50391 12.0315 2.67572 12.3588 2.67572H13.7497C14.617 2.67572 15.3288 3.38754 15.3288 4.25482V5.64572C15.3288 5.96482 15.5006 6.43118 15.7134 6.67663L16.8261 7.96936C17.3088 8.53391 17.3088 9.45845 16.8261 10.0312L15.7134 11.3239C15.5006 11.5694 15.3288 12.0275 15.3288 12.3548V13.7457C15.3288 14.613 14.617 15.3248 13.7497 15.3248H12.3588C12.0397 15.3248 11.5734 15.4966 11.3279 15.7094L10.0352 16.8221C9.47063 17.3048 8.54609 17.3048 7.97336 16.8221L6.68063 15.7094C6.43518 15.4966 5.977 15.3248 5.64972 15.3248H4.23427C3.367 15.3248 2.65518 14.613 2.65518 13.7457V12.3466C2.65518 12.0275 2.48336 11.5694 2.27881 11.3239L1.17427 10.023C0.699723 9.45845 0.699723 8.54209 1.17427 7.97754L2.27881 6.67663C2.48336 6.43118 2.65518 5.973 2.65518 5.65391V4.24663C2.65518 3.37936 3.367 2.66754 4.23427 2.66754H5.64972C5.96881 2.66754 6.43518 2.49572 6.68063 2.283L7.97336 1.17845Z' stroke='%23008A00' stroke-width='1.3' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat center center;
    width: 17px;
    height: 17px;
    display: inline-block;
    content: "";
    transform: translateY(-50%);
    -webkit-transform: translateY(-50%);
    position: absolute;
    left: 15px;
    top: 50%;
  }
  .success, .warning {
    padding: 12px 20px 12px 45px;
    position: relative;
  }
  .success {
      --color-success: #008a00;
      --border-color-success: #b2e5ad;
      --background-success: #e3fadf;
      color: var(--color-success);
      background-color: var(--background-success);
      border: 1px solid var(--border-color-success);
      border-radius: 5px;
      gap: 0.5rem;
  }
.button--secondary{
   background-color: black;
  color: white;
}
.gift-card__image-wrapper img{
    max-width: 100%;
}
@media(max-width: 768px){
    .button{
       width: 300px;
    }
}
.countinue_shop {
     text-decoration: none;
}