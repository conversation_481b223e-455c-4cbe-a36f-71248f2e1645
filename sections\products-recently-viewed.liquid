{%- liquid
  assign section_st = section.settings
  assign section_width = section_st.section_width
  assign color_scheme = section_st.color_scheme
  if section_st.reset_spacing
    assign reset_spacing = ' remove_spacing'
  endif
  assign column_gap = section_st.column_gap
  assign items_per_row = section_st.items_per_row
  assign items_to_show = section_st.items_to_show
  assign heading = section_st.heading
  assign description = section_st.description
  assign header_size = ''
  if section_st.header_size == 'small'
    assign header_size = 'h3'
  elsif section_st.header_size == 'large'
    assign header_size = 'h1-size'
  endif
  if section_st.items_per_row_mobile > 1 and section_st.items_per_row_mobile < 2
    assign data_free_scroll = 'true'
  endif
  assign show_arrow = section_st.show_arrow
  assign carousel_pagination = section_st.carousel_pagination
  assign infinite = section_st.infinite
  assign autoplay = section_st.autoplay
  assign autorotate_speed = section_st.autorotate_speed
  assign reveal = section_st.reveal
  assign scroll_animation = settings.scroll_animation
-%}
{%- capture style -%}
    --section-pt: {{ section_st.padding_top }}; --section-pb: {{ section_st.padding_bottom }};
  {%- endcapture -%}
{%- capture col_style -%}
  {% if column_gap < 15 %}--col-gap: {{ column_gap }}px;{% else %}--col-gap: 15px;--col-gap-desktop: {{  column_gap }}px;{% endif %}
  {%- endcapture -%}
<div
  class="section{% if section_st.padding_top < 30 %} pt-min{% endif %}{% if section_st.padding_bottom < 30 %} pb-min{% endif %} sec__related-product color-{{ color_scheme }} gradient{{ reset_spacing }}{% if reveal %} overflow-hidden{% endif %}"
  style="{{ style | strip | strip_newlines }}"
>
  <div class="{{ section_width }}">
    {%- if heading != blank or description != blank -%}
      <div class="section__header mb-33 mb-sm-20 text-{{ section_st.header_alignment }}{% if section_st.section_width == 'full_width' %} px-20{% endif %}">
        {%- if heading != blank or description != blank -%}
          <div class="secion__header-inner">
            {%- if section_st.heading != blank -%}
              <motion-element
                data-motion="fade-up-lg"
                {% if scroll_animation != 'slide_in' %}
                  hold
                {% endif %}
                data-motion-delay="50"
                class="block {% if scroll_animation != 'none' %} scroll-trigger {{ scroll_animation }}{% endif %}"
                style="
                  {%- if scroll_animation != 'none' -%}
                    --animation-order: 0;
                  {% endif %}
                "
              >
              <h2
                class="section__header-heading heading-letter-spacing {{ header_size  }} mt-0{% if section_st.description != blank %} mb-10{% else %} mb-0{% endif %}"
              >
                {{ section_st.heading }}
              </h2>
            </motion-element>
            {% endif %}
            {%- if section_st.description != blank -%}
              <motion-element
                data-motion="fade-up-lg"
                data-motion-delay="150"
                {% if scroll_animation != 'slide_in' %}
                  hold
                {% endif %}
                class="section__header-des block rich__text-m0"
                style="
                  {%- if scroll_animation != 'none' -%}
                    --animation-order: 1
                  {% endif %}
                "
              >
                {{ section_st.description }}
              </motion-element>
            {% endif %}
          </div>
        {% endif %}
      </div>
    {% endif %}
    {% if section_st.items_per_row_mobile > 1 and section_st.items_per_row_mobile < 2 %}
      <div class="free-scroll">
    {% endif %}
    <recently-viewed-products
      data-section-id="{{ section.id }}"
      data-autoplay="{{ autoplay }}"
      data-effect="slide"
      data-loop="{{ infinite }}"
      data-speed="500"
      data-autoplay-speed="{{ autorotate_speed }}"
      data-spacing="{{ column_gap }}"
      data-mobile="{{ section_st.items_per_row_mobile }}"
      data-desktop="{{ items_per_row }}"
      data-free-scroll="{{ data_free_scroll }}"
      data-pagination-progressbar="{% if carousel_pagination == 'show_progress_bar' %}true{% else %}false{% endif %}"
      style="{{ col_style | strip | strip_newlines }}"
      class="swiper products-recently-viewed page-width section-{{ section.id }}-padding isolate"
      data-url="{{ routes.search_url }}?section_id={{ section.id }}"
      data-limit="{{ items_to_show }}"
      data-arrow-centerimage="1"
    >
      {% if search.results != blank %}
        {% if show_arrow %}
          {%- render 'swiper-navigation' -%}
        {% endif %}
        <div class="swiper-wrapper">
          {% for item in search.results %}
            {% render 'product-item',
              card_product: item,
              section_id: section.id,
              class: ' swiper-slide',
              template_enable_action: true,
              template_enable_product_vendor: true,
              template_enable_rate: true,
              template_enable_product_short_description: false,
              template_enable_color_swatches: true,
              template_enable_price: true,
              template_enable_product_badges: true,
              template_enable_add_cart: true,
              scroll_animation: scroll_animation,
              indexFor: forloop.index
            %}
          {% endfor %}
        </div>
        {%- if carousel_pagination == 'show_dots'
          or carousel_pagination == 'show_dots_on_mobile'
          or carousel_pagination == 'show_progress_bar'
        -%}
          <div
            class="swiper-pagination flex flex-wrap px-15 lh-1 bottom-30 {% if carousel_pagination == 'show_dots_on_mobile' %} hidden-md{% endif %} justify-content-center{% if section_st.content_below_image %} absolute-md-impo{% endif %}"
            style="--swiper-pagination-bottom: 3rem;--swiper-pagination-position: static;"
          ></div>
        {% endif %}
      {% else %}
        <p class="text-center">{{ 'sections.recently-viewed-products.no_product' | t }}</p>
      {% endif %}
    </recently-viewed-products>
    {% if section_st.items_per_row_mobile > 1 and section_st.items_per_row_mobile < 2 %}</div>{% endif %}
  </div>
</div>

{% schema %}
{
  "name": "t:sections.products-recently-viewed.name",
  "tag": "section",
  "disabled_on": {
    "groups": ["header", "footer", "custom.overlay"]
  },
  "settings": [
    {
      "type": "select",
      "id": "section_width",
      "label": "t:sections.all.section_width.label",
      "default": "fluid_container",
      "options": [
        {
          "value": "container",
          "label": "t:sections.all.section_width.container.label"
        },
        {
          "value": "fluid_container",
          "label": "t:sections.all.section_width.fluid_container.label"
        },
        {
          "value": "stretch_width",
          "label": "t:sections.all.section_width.stretch_width.label"
        },
        {
          "value": "full_width",
          "label": "t:sections.all.section_width.full_width.label"
        }
      ]
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.color_scheme.label",
      "default": "scheme-1"
    },
    {
      "type": "header",
      "content": "t:sections.all.section_header.label"
    },
    {
      "type": "text",
      "id": "heading",
      "label": "t:sections.all.section_header.heading.label",
      "default": "Products recently viewed"
    },
    {
      "type": "inline_richtext",
      "id": "description",
      "label": "t:sections.all.section_header.description.label"
    },
    {
      "type": "select",
      "id": "header_size",
      "label": "t:sections.all.section_header.header_size.label",
      "default": "medium",
      "options": [
        {
          "value": "small",
          "label": "t:sections.all.section_header.header_size.small.label"
        },
        {
          "value": "medium",
          "label": "t:sections.all.section_header.header_size.medium.label"
        },
        {
          "value": "large",
          "label": "t:sections.all.section_header.header_size.large.label"
        }
      ]
    },
    {
      "type": "select",
      "id": "header_alignment",
      "label": "t:sections.all.section_header.alignment.label",
      "default": "left",
      "options": [
        {
          "value": "left",
          "label": "t:sections.all.section_header.alignment.left.label"
        },
        {
          "value": "center",
          "label": "t:sections.all.section_header.alignment.center.label"
        },
        {
          "value": "right",
          "label": "t:sections.all.section_header.alignment.right.label"
        }
      ]
    },
    {
      "type": "header",
      "content": "t:sections.products-recently-viewed.settings.products.header"
    },
    {
      "type": "range",
      "id": "items_to_show",
      "label": "t:sections.all.items.items_to_show.label",
      "min": 2,
      "max": 25,
      "step": 1,
      "default": 8
    },
    {
      "type": "range",
      "id": "items_per_row",
      "label": "t:sections.all.items.items_per_row.label",
      "min": 1,
      "max": 5,
      "step": 1,
      "default": 4
    },
    {
      "type": "range",
      "id": "column_gap",
      "label": "t:sections.all.items.column_gap.label",
      "min": 0,
      "max": 50,
      "step": 5,
      "default": 30,
      "unit": "px"
    },
    {
      "type": "header",
      "content": "t:sections.all.carousel_settings.label"
    },
    {
      "type": "checkbox",
      "id": "show_arrow",
      "label": "t:sections.all.carousel_settings.show-next-back.label",
      "default": false
    },
    {
      "type": "select",
      "id": "carousel_pagination",
      "label": "t:sections.all.carousel_settings.pagination.label",
      "options": [
        {
          "value": "disable",
          "label": "t:sections.all.carousel_settings.pagination.disable.label"
        },
        {
          "value": "show_dots",
          "label": "t:sections.all.carousel_settings.pagination.show_dots.label"
        },
        {
          "value": "show_dots_on_mobile",
          "label": "t:sections.all.carousel_settings.pagination.show_dots_on_mobile.label"
        },
        {
          "value": "show_progress_bar",
          "label": "t:sections.all.carousel_settings.pagination.show_progress_bar.label"
        }
      ]
    },
    {
      "type": "checkbox",
      "id": "infinite",
      "label": "t:sections.all.carousel_settings.infinite.label",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "autoplay",
      "label": "t:sections.all.carousel_settings.auto_change.label",
      "default": false
    },
    {
      "type": "range",
      "id": "autorotate_speed",
      "label": "t:sections.all.carousel_settings.change_slides_every.label",
      "max": 6,
      "min": 2,
      "step": 1,
      "unit": "s",
      "default": 5
    },
    {
      "type": "checkbox",
      "id": "reveal",
      "label": "t:sections.all.carousel_settings.reveal.label",
      "info": "t:sections.all.carousel_settings.reveal.info",
      "default": false
    },
    {
      "type": "header",
      "content": "t:sections.all.mobile_options.label"
    },
    {
      "type": "range",
      "id": "items_per_row_mobile",
      "label": "t:sections.all.items.items_per_row.label",
      "min": 1,
      "max": 2,
      "step": 0.5,
      "default": 1
    },
    {
      "type": "header",
      "content": "t:sections.all.section_padding.label"
    },
    {
      "type": "range",
      "id": "padding_top",
      "label": "t:sections.all.section_padding.top.label",
      "default": 0,
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px"
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "label": "t:sections.all.section_padding.bottom.label",
      "default": 0,
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px"
    },
    {
      "type": "checkbox",
      "id": "reset_spacing",
      "label": "t:sections.all.section_padding.reset_spacing.label",
      "default": false
    }
  ],
  "presets": [
    {
      "name": "t:sections.products-recently-viewed.name"
    }
  ]
}
{% endschema %}
