{{ 'my-account.css' | asset_url | stylesheet_tag }}
<div class="container ">
  <div class="customer-form reset-passwordw-full max-w-custom m-auto col-sm-w-custom"  style="--col-width: 80%; --max-width: 63rem">
    {% form 'reset_customer_password' %}
      {%- if form.errors -%}
        <ul class="error">
          {%- for field in form.errors -%}
            <li class="flex gap-5">
              {%- render 'icon-error' -%}
              {%- if field == 'form' -%}
                {{ form.errors.messages[field] }}
              {%- else -%}
                <a href="#RegisterForm-{{ field }}" class="link link-underline no-underline">
                  <span class="text">
                    {{ form.errors.translated_fields[field] | capitalize }}
                    {{ form.errors.messages[field] }}
                  </span>
                </a>
              {% endif %}
            </li>
          {%- endfor -%}
        </ul>
      {% endif %}
      <div class="password form-group mb-20 flex flex-column ">
        <label class="form-label pb-5 vis" for="password"></label>
        <input type="password" required placeholder=" {{- 'customer.reset_password.password' | t -}}" name="customer[password]" id="password">
      </div>

      <div class="password_confirm form-group mb-20 flex flex-column ">
        <label class="form-label pb-5" for="password_confirmation"></label>
        <input required type="password" placeholder="{{- 'customer.reset_password.password_confirm' | t -}}" name="customer[password_confirmation]" id="password_confirmation">
      </div>

      <div class="submit mt-30 text-center">
        <input  class="btn-primary w-full text-center" type="submit" value="{{ 'customer.reset_password.submit' | t }}">
      </div>
    {% endform %}
  </div>
</div>
