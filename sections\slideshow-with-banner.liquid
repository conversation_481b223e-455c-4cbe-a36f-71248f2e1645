<link rel="stylesheet" href="{{ 'banner.css' | asset_url }}" media="print" onload="this.media='all'">
<link rel="stylesheet" href="{{ 'slideshow.css' | asset_url }}" media="print" onload="this.media='all'">
{% liquid
  assign section_st = section.settings
  assign section_width = section_st.section_width
  if section_st.reset_spacing
    assign reset_spacing = ' remove_spacing'
  endif

  assign has_slideshow_parent = false
  assign has_banner_parent = false

  assign slideshow_parent = section.blocks | where: 'type', 'slideshow_parent' | first
  assign bl_slide_parent_st = slideshow_parent.settings
  assign slide_height = 'adapt'
  assign color_scheme = bl_slide_parent_st.color_scheme
  assign show_arrow = bl_slide_parent_st.show_arrow
  assign carousel_pagination = bl_slide_parent_st.carousel_pagination
  assign slide_effect = bl_slide_parent_st.slide_effect
  assign pagination_position = bl_slide_parent_st.pagination_position
  assign autoplay = bl_slide_parent_st.autoplay
  assign autorotate_speed = bl_slide_parent_st.autorotate_speed

  assign slideshow_child_blocks = ''
  assign banner_child_blocks = ''

  for block in section.blocks
    if block.type == 'slideshow_parent'
      assign has_slideshow_parent = true
      continue
    elsif block.type == 'banner_parent'
      assign has_banner_parent = true
      continue
    elsif has_slideshow_parent and block.type == 'slideshow_child'
      assign slideshow_child_blocks = slideshow_child_blocks | append: ',' | append: block.id
    elsif has_banner_parent and block.type == 'banner_child'
      assign banner_child_blocks = banner_child_blocks | append: ',' | append: block.id
    endif
  endfor

  if slideshow_child_blocks != ''
    assign slideshow_child_blocks = slideshow_child_blocks | remove_first: ',' | split: ','
  endif

  if banner_child_blocks != ''
    assign banner_child_blocks = banner_child_blocks | remove_first: ',' | split: ','
  endif
%}
{%- capture video_attribute -%}
    muted autoplay
{%- endcapture -%}
{%- capture style -%}
--section-pt: {{ section_st.padding_top }};--section-pb: {{ section_st.padding_bottom }}; {%- if slide_height == 'custom' -%}
  --height: {{ section_st.mobile_height }}px;--height-desktop: {{ section_st.desktop_height }}px;
  {% endif %}
{%- endcapture -%}
<div
  id="{{ section.id }}"
  class="section{% if section_st.padding_top < 30 %} pt-min{% endif %}{% if section_st.padding_bottom < 30 %} pb-min{% endif %} gradient slideshow {{ slide_effect }} color-{{ color_scheme }}{{ reset_spacing }}"
  style="{{ style | strip | strip_newlines }}"
  data-id="{{ section.id }}"
>
  <div class="{{ section_width }}">
    <div class="section-slide-with-banner" style="--slide-width: {{ section_st.slide_width }}%;--coloumn-gap: {{  section_st.column_gap }}px;">
      {% if has_slideshow_parent and slideshow_child_blocks.size > 0 %}
        <div class="slide__wrapper">
          <slide-section
            id="swiper-{{ section.id }}"
            class="swiper slideshow   lazy-loading-swiper-before"
            data-section-id="{{ section.id }}"
            data-autoplay="{{ autoplay }}"
            data-autoplay-speed="{{ autorotate_speed }}"
            data-effect="{{ slide_effect }}"
            data-loop="true"
            data-speed="600"
            data-slideshow="1"
            data-mobile="1"
            data-tablet="1"
            data-desktop="1"
            data-auto-height="true"
          >
            <div class="swiper-wrapper">
              {% for block_id in slideshow_child_blocks %}
                {% liquid
                  assign block = section.blocks | where: 'id', block_id | first
                  assign block_st = block.settings
                  assign image = block_st.image
                  assign image_mobile = block_st.image_mobile | default: image
                  assign slider_effect = block_st.slider_effect
                  assign local_video = block_st.local_video
                  assign lazy_load = false
                  assign ratio_dk = ''
                  assign ratio_mb = ''
                  assign first_slide = slideshow_child_blocks | first
                  if first_slide != blank
                    assign first_slide_block = section.blocks | where: 'id', first_slide | first
                    assign first_slide_settings = first_slide_block.settings
                  endif
                  if slide_height == 'adapt'
                    if first_slide_settings.image != blank and first_slide_settings.image_mobile != blank
                      assign ratio_dk = first_slide_settings.image.aspect_ratio
                      assign ratio_mb = first_slide_settings.image_mobile.aspect_ratio | default: ratio_dk
                    elsif first_slide_settings.image == blank and first_slide_settings.image_mobile != blank
                      assign ratio_dk = first_slide_settings.image_mobile.aspect_ratio
                      assign ratio_mb = first_slide_settings.image_mobile.aspect_ratio
                    elsif first_slide_settings.image != blank and first_slide_settings.image_mobile == blank
                      assign ratio_dk = first_slide_settings.image.aspect_ratio
                      assign ratio_mb = first_slide_settings.image.aspect_ratio
                    elsif first_slide_settings.local_video != blank
                      assign ratio_dk = first_slide_settings.local_video.aspect_ratio
                      assign ratio_mb = first_slide_settings.local_video.aspect_ratio
                    else
                      assign ratio_dk = '3/2'
                      assign ratio_mb = '2/3'
                    endif
                  else
                    assign ratio_dk = '3/2'
                    assign ratio_mb = '2/3'
                  endif
                  if forloop.first
                    assign lazy_load = true
                  endif
                %}
                <div {{ block.shopify_attributes }} class="swiper-slide">
                  <div
                    class="sec__inner flex relative {% if slide_height == 'adapt' and image_mobile != blank %} ratio-mobile{% endif %} h-{{ slide_height }}"
                    style="--overlay-opacity: {{ block_st.overlay_opacity }}%;{% if slide_height == 'adapt' %}--aspect-ratio: {{ ratio_dk }};--aspect-ratio-mb: {{ ratio_mb }};{% endif %}"
                  >
                    <div
                      class="banner__media w-full overlay-bg{% if section_st.section_width != "full_width" %} rounded{% endif %} absolute inset-0{% if slide_height == 'adapt' and image_mobile != blank %} ratio-mobile{% endif %}"
                      style="--aspect-ratio: {{ ratio_dk }};--aspect-ratio-mb: {{ ratio_mb }};--point:{{ image.presentation.focal_point }};"
                    >
                      {%- if image != blank or image_mobile != blank or local_video != blank -%}
                        {% if local_video != blank %}
                          {%- liquid
                            assign source = local_video.sources
                            assign source_url = ''
                            for s in source
                              if s.format == 'mp4'
                                assign source_url = s.url
                                break
                              endif
                            endfor
                            assign mobile_url = ''
                            assign desktop_url = ''
                            if image_mobile
                              assign mobile_url = image_mobile
                            endif
                            if image
                              assign desktop_url = image
                            endif
                            assign poster = local_video.preview_image | image_url: width: 1100
                            assign poster_desktop = image | default: mobile_url
                            assign poster_mobile = image_mobile | default: desktop_url
                          -%}
                          <video
                            class="slideshow"
                            loop="true"
                            {% if poster_mobile != blank %}
                              data-posterMobile="{{ poster_mobile | image_url: width: 1100 }}"
                            {% endif %}
                            {% if poster_desktop != blank %}
                              data-posterDesktop="{{ poster_desktop | image_url: width: 1100 }}"
                            {% endif %}
                            data-src="{{ source_url }}"
                            poster="{{ poster }}"
                            playsinline="true"
                            {{ video_attribute }}
                          ></video>
                        {% else %}
                          {%- liquid
                            if block.settings.image.alt == blank
                              assign image_alt = block.settings.heading
                            else
                              assign image_alt = block.settings.image.alt
                            endif
                          -%}
                          {% render 'responsive-image',
                            type: 'banner',
                            container: section_width,
                            image: image,
                            image_mobile: image_mobile,
                            image_alt: image_alt,
                            lazy_load: lazy_load,
                            first: forloop.first
                          %}
                        {% endif %}
                      {%- else -%}
                        {%- render 'placeholder-render', class: 'absolute inset-0 w-full h-full object-fit-cover' -%}
                      {% endif %}
                    </div>
                    {% if block_st.heading != blank
                      or block_st.subheading != blank
                      or block_st.description != blank
                      or block_st.first_button_label != blank
                      or block_st.second_button_label != blank
                    %}
                      <div
                        class="sec__content relative z-1 flex{% if block_st.content_width == 'full_width' %} w-full{% else %} w-1024-full{% endif %} {{ block_st.content_position }} {{ block_st.content_width }} text-{{block_st.content_alignment_mobile}} text-md-{{ block_st.content_alignment }}{% if block_st.show_content_background %} py-custom px-custom{% if block_st.content_padding_inline < 15 %} min-value{% endif %}{% endif %}"
                        style="--padding-inline: {{ block_st.content_padding_inline }};--padding-block: {{ block_st.content_padding_block }};"
                      >
                        <div
                          class="sec__content-inner w-full w-md-unset invisible max-w-custom{% if block_st.show_content_background == blank %} py-custom px-custom{% if block_st.content_padding_inline < 15 %} min-value{% endif %}{% endif %}{% if block_st.show_content_background %} content-box relative p-30 p-1025-50{% endif %} {{ slider_effect }}"
                          style="--max-width: {{ block_st.content_max_width }}%;{% if block_st.show_content_background %}--content-bg: {{ block_st.content_background}}; --opacity:{{ block_st.content_box_opacity }}%;{% endif %}"
                        >
                          {% if block_st.subheading != blank %}
                            <motion-element
                              data-motion="fade-up-lg"
                              hold
                              data-motion-delay="100"
                              class="sec__content-subheading  {% if block_st.slider_effect == 'effect_fadeinup' %} opacity-0  {{ slider_effect }}  {% endif %}  block heading{% if bl_slide_parent_st.subheading_spacing_bottom > 41 %} mb-big{% elsif bl_slide_parent_st.subheading_spacing_bottom > 30 %} mb-medium {% else %} mb-custom{% endif %} fs-custom {{ bl_slide_parent_st.subheading_font_weight }}"
                              style="--font-size: {{ bl_slide_parent_st.subheading_font_size }};--space-bottom: {{ bl_slide_parent_st.subheading_spacing_bottom }}"
                            >
                              {{ block_st.subheading | escape }}
                            </motion-element>
                          {% endif %}
                          {% if block_st.heading != blank %}
                            <motion-element
                              data-motion="fade-up-lg"
                              hold
                              data-motion-delay="200"
                              class="block  {% if block_st.slider_effect == 'effect_fadeinup' %} opacity-0 {{ slider_effect }} {% endif %}"
                            >
                              <h2
                                class="sec__content-heading   heading-letter-spacing mt-0{% if bl_slide_parent_st.heading_spacing_bottom > 41 %} mb-big{% elsif bl_slide_parent_st.heading_spacing_bottom > 30 %} mb-medium {% else %} mb-custom{% endif %}{% if bl_slide_parent_st.heading_uppercase %} uppercase{% endif %}{% if bl_slide_parent_st.heading_font_size > 41 %} fs-big{% elsif bl_slide_parent_st.heading_font_size > 24 %} fs-medium{% else %} fs-custom{% endif %} {{ bl_slide_parent_st.heading_font_weight }}"
                                style="--font-size: {{ bl_slide_parent_st.heading_font_size }};--space-bottom: {{ bl_slide_parent_st.heading_spacing_bottom }}"
                                {{ block.shopify_attributes }}
                              >
                                {{ block_st.heading }}
                              </h2>
                            </motion-element>
                          {% endif %}
                          {% if block_st.description != blank %}
                            <motion-element
                              data-motion="fade-up-lg"
                              hold
                              data-motion-delay="300"
                              class="sec__content-des {% if block_st.slider_effect == 'effect_fadeinup' %} opacity-0  {{ slider_effect }}  {% endif %} block rich__text-m0 {% if bl_slide_parent_st.des_spacing_bottom > 41 %} mb-big{% elsif bl_slide_parent_st.des_spacing_bottom > 30 %} mb-medium {% else %} mb-custom{% endif %}{% if bl_slide_parent_st.des_font_size > 24 %} fs-medium{% else %} fs-custom{% endif %} {{ bl_slide_parent_st.des_font_weight }}"
                              style="--font-size: {{ bl_slide_parent_st.des_font_size }};--space-bottom: {{ bl_slide_parent_st.des_spacing_bottom }}"
                              {{ block.shopify_attributes }}
                            >
                              {{ block_st.description }}
                            </motion-element>
                          {% endif %}
                          {% if block_st.first_button_label != blank or block_st.second_button_label != blank %}
                            <motion-element
                              data-motion="fade-up-lg"
                              hold
                              data-motion-delay="400"
                              class="sec__content-btn {% if block_st.slider_effect == 'effect_fadeinup' %} opacity-0  {{ slider_effect }}  {% endif %} align-center flex flex-wrap gap-15 justify-content-{{ block_st.content_alignment_mobile }} justify-content-md-{{ block_st.content_alignment }}"
                            >
                              {% if block_st.first_button_label != blank %}
                                <a
                                  {% if block_st.first_button_link == blank %}
                                    role="link" aria-disabled="true"
                                  {% else %}
                                    href="{{ block_st.first_button_link | default: "#" }}"
                                  {% endif %}
                                  aria-label="{{ block_st.first_button_label }}"
                                  class="inline-flex no-underline{% if block_st.first_button_style %} btn-outline{% else %} btn-primary{% endif %}"
                                >
                                  {{ block_st.first_button_label }}
                                </a>
                              {% endif %}
                              {% if block_st.second_button_label != blank %}
                                <a
                                  {% if block_st.second_button_link == blank %}
                                    role="link" aria-disabled="true"
                                  {% else %}
                                    href="{{ block_st.second_button_link | default: "#" }}"
                                  {% endif %}
                                  aria-label="{{ block_st.second_button_label }}"
                                  class="inline-flex no-underline{% if block_st.second_button_style %} btn-outline{% else %} btn-primary{% endif %}"
                                >
                                  {{ block_st.second_button_label }}
                                </a>
                              {% endif %}
                            </motion-element>
                          {% endif %}
                        </div>
                      </div>
                    {% endif %}
                  </div>
                </div>
              {% endfor %}
            </div>
            {% if show_arrow %}
              {%- render 'swiper-navigation', class: 'original-style' -%}
            {% endif %}
            {%- if carousel_pagination == 'show_dots' or carousel_pagination == 'show_dots_on_mobile' -%}
              <div
                class="swiper-pagination flex flex-wrap px-15 lh-1 bottom-30 {% if carousel_pagination == 'show_dots_on_mobile' %} hidden-md{% endif %} justify-content-{{ pagination_position }}"
                style="--swiper-pagination-bottom: clamp(1.2rem, 2vw, 3rem);--swiper-pagination-position: absolute;"
              ></div>
            {% endif %}
          </slide-section>
        </div>
      {% endif %}
      {% if has_banner_parent and banner_child_blocks.size > 0 %}
        <div class="banner__wrapper">
          <div class="banner__inner {{ settings.hover_effect }} flex flex-wrap justify-content-center grid-{{ banner_child_blocks.size }}">
            {% for block_id in banner_child_blocks %}
              {% liquid
                assign block = section.blocks | where: 'id', block_id | first
                assign block_st = block.settings
                assign color_scheme = block_st.color_scheme
              %}
              <div
                class="hover-effect section__block-inner gradient color-{{ color_scheme }} flex relative"
                style="{%- if block_st.image != blank -%}--aspect-ratio: {{ block_st.image.aspect_ratio }};{%- else -%}--aspect-ratio: 5/2;{% endif %}{%- if block_st.mobile_image -%}--aspect-ratio-mb: {{ block_st.mobile_image.aspect_ratio }};{% endif %}"
              >
                <div
                  class="banner__media w-full overlay-bg rounded absolute inset-0"
                  style="{%- if block_st.image != blank -%}--aspect-ratio: {{ block_st.image.aspect_ratio }};{%- else -%}--aspect-ratio: 5/2; {% endif %}--overlay-opacity: {{ block_st.image_overlay_opacity }}%; {%- if block_st.mobile_image -%} --aspect-ratio-mb: {{ block_st.mobile_image.aspect_ratio }};{% endif %}"
                >
                  {% liquid
                    assign image = block_st.image
                    assign mobile_image = block_st.mobile_image | default: image
                  %}
                  {%- if image != blank or mobile_image != blank -%}
                    {%- assign image_alt = image.alt | escape | default: 'Images' -%}
                    {% render 'responsive-image',
                      type: 'banner',
                      container: section_width,
                      image: image,
                      image_mobile: mobile_image,
                      image_alt: image_alt
                    %}
                  {%- else -%}
                    {%- render 'placeholder-render' -%}
                  {% endif %}
                  {% if block_st.image_link != blank %}
                    <a
                      class="absolute inset-0 z-2 block hidden-md"
                      aria-label="{{ block_st.heading }}"
                      href="{{ block_st.image_link }}"
                    ></a>
                  {% endif %}
                </div>
                <div
                  class="sec__content w-full flex {{ block_st.content_position }} text-{{ block_st.content_alignment }}{% if section_st.equal_height_adjustment %} align-self-{{ block_st.vertical_align }}{% endif %}"
                  style="--padding-inline: {{ block_st.content_padding_inline }};--padding-block: {{ block_st.content_padding_block }};"
                >
                  <div class="sec__content-inner py-custom px-custom relative{% if block_st.content_padding_inline < 35 %} x-min-value{% endif %}{% if block_st.content_padding_block < 35 %} y-min-value{% endif %}">
                    {%- if block_st.custom_svg != blank -%}
                      <div
                        class="sec__content-custom-svg {% if block_st.subheading_spacing_bottom > 41 %} mb-big{% elsif block_st.subheading_spacing_bottom > 30 %} mb-medium {% else %} mb-custom{% endif %}"
                        style="--space-bottom: {{ block_st.custom_svg_spacing_bottom }}"
                      >
                        {{ block_st.custom_svg }}
                      </div>
                    {% endif %}
                    {%- if block_st.subheading != blank -%}
                      <div
                        class="sec__content-subheading heading-color heading fs-custom {{ block_st.subheading_font_weight }}{% if block_st.subheading_spacing_bottom > 41 %} mb-big{% elsif block_st.subheading_spacing_bottom > 30 %} mb-medium {% else %} mb-custom{% endif %}"
                        style="--font-size: {{ block_st.subheading_font_size }};--space-bottom: {{ block_st.subheading_spacing_bottom }}"
                      >
                        {{ block_st.subheading | escape }}
                      </div>
                    {% endif %}
                    {%- if block_st.heading != blank -%}
                      <h2
                        class="sec__content-heading heading-letter-spacing mt-0{% if block_st.heading_spacing_bottom > 41 %} mb-big{% elsif block_st.heading_spacing_bottom > 30 %} mb-medium {% else %} mb-custom{% endif %}{% if block_st.heading_uppercase %} uppercase{% endif %}{% if block_st.heading_font_size > 41 %} fs-big{% elsif block_st.heading_font_size > 24 %} fs-medium {% else %} fs-custom{% endif %} {{ block_st.heading_font_weight }}"
                        style="--font-size: {{ block_st.heading_font_size }};--space-bottom: {{ block_st.heading_spacing_bottom }}"
                        {{ block.shopify_attributes }}
                      >
                        {{ block_st.heading }}
                      </h2>
                    {% endif %}
                    {%- if block_st.description != blank -%}
                      <div
                        class="sec__content-des rich__text-m0 {% if block_st.des_spacing_bottom > 41 %} mb-big{% elsif block_st.des_spacing_bottom > 30 %} mb-medium {% else %} mb-custom{% endif %}{% if block_st.des_font_size > 24 %} fs-medium {% else %} fs-custom{% endif %} {{ block_st.des_font_weight }}"
                        style="--font-size: {{ block_st.des_font_size }};--space-bottom: {{ block_st.des_spacing_bottom }}"
                        {{ block.shopify_attributes }}
                      >
                        {{ block_st.description }}
                      </div>
                    {% endif %}
                    {% if block_st.first_button_label != blank or block_st.second_button_label != blank %}
                      <div class="sec__content-btn align-center flex flex-wrap gap-15 justify-content-{{ block_st.content_alignment }}">
                        {% if block_st.first_button_label != blank %}
                          <a
                            {% if block_st.first_button_link == blank %}
                              role="link" aria-disabled="true"
                            {% else %}
                              href="{{ block_st.first_button_link | default: "#" }}"
                            {% endif %}
                            aria-label="{{ block_st.first_button_label }}"
                            class="relative z-3 inline-flex no-underline btn-{{ block_st.first_button_type }} px-md-20 py-md-10"
                          >
                            {{ block_st.first_button_label }}
                          </a>
                        {% endif %}
                        {% if block_st.second_button_label != blank %}
                          <a
                            {% if block_st.second_button_link == blank %}
                              role="link" aria-disabled="true"
                            {% else %}
                              href="{{ block_st.second_button_link | default: "#" }}"
                            {% endif %}
                            aria-label="{{ block_st.second_button_label }}"
                            class="relative z-3 inline-flex no-underline btn-{{ block_st.second_button_type }} px-md-20 py-md-10"
                          >
                            {{ block_st.second_button_label }}
                          </a>
                        {% endif %}
                      </div>
                    {% endif %}
                  </div>
                  {% if block_st.image_link != blank %}
                    <a
                      class="absolute inset-0 z-2 hidden block-md"
                      href="{{ block_st.image_link }}"
                      aria-label="{{ block_st.heading }}"
                    ></a>
                  {% endif %}
                </div>
              </div>
            {% endfor %}
          </div>
        </div>
      {% endif %}
    </div>
  </div>
</div>

{% schema %}
{
  "name": "t:sections.slideshow-with-banner.name",
  "tag": "section",
  "disabled_on": {
    "groups": ["header", "footer", "custom.overlay"]
  },
  "settings": [
    {
      "type": "select",
      "id": "section_width",
      "options": [
        {
          "value": "container",
          "label": "t:sections.all.section_width.container.label"
        },
        {
          "value": "fluid_container",
          "label": "t:sections.all.section_width.fluid_container.label"
        },
        {
          "value": "stretch_width",
          "label": "t:sections.all.section_width.stretch_width.label"
        },
        {
          "value": "full_width",
          "label": "t:sections.all.section_width.full_width.label"
        }
      ],
      "default": "full_width",
      "label": "t:sections.all.section_width.label"
    },
    {
      "type": "range",
      "id": "column_gap",
      "label": "t:sections.all.items.column_gap.label",
      "min": 0,
      "max": 50,
      "step": 5,
      "default": 30,
      "unit": "px"
    },
    {
      "type": "range",
      "id": "slide_width",
      "label": "t:sections.slideshow-with-banner.blocks.slide_width.label",
      "min": 0,
      "max": 70,
      "step": 1,
      "default": 50,
      "unit": "%"
    },
    {
      "type": "header",
      "content": "t:sections.all.section_padding.label"
    },
    {
      "type": "range",
      "id": "padding_top",
      "label": "t:sections.all.section_padding.top.label",
      "default": 0,
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px"
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "label": "t:sections.all.section_padding.bottom.label",
      "default": 0,
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px"
    },
    {
      "type": "checkbox",
      "id": "reset_spacing",
      "label": "t:sections.all.section_padding.reset_spacing.label",
      "default": false
    }
  ],
  "blocks": [
    {
      "type": "slideshow_parent",
      "name": "t:sections.slideshow-with-banner.blocks.slideshow_parent.name",
      "limit": 1,
      "settings": [
        {
          "type": "color_scheme",
          "id": "color_scheme",
          "label": "t:sections.all.color_scheme.label"
        },
        {
          "type": "header",
          "content": "t:sections.all.content_setting.label",
          "info": "t:sections.all.content_setting.info"
        },
        {
          "type": "paragraph",
          "content": "t:sections.all.content_settings.subheading.label"
        },
        {
          "type": "range",
          "id": "subheading_font_size",
          "label": "t:sections.all.content_settings.font_size.label",
          "default": 14,
          "min": 12,
          "max": 24,
          "step": 1,
          "unit": "px"
        },
        {
          "type": "select",
          "id": "subheading_font_weight",
          "label": "t:sections.all.content_settings.font_weight.label",
          "default": "body_weight",
          "options": [
            {
              "value": "body_weight",
              "label": "t:sections.all.content_settings.font_weight.body_weight.label"
            },
            {
              "value": "subheading_weight",
              "label": "t:sections.all.content_settings.font_weight.subheading_weight.label"
            },
            {
              "value": "heading_weight",
              "label": "t:sections.all.content_settings.font_weight.heading_weight.label"
            }
          ]
        },
        {
          "type": "range",
          "id": "subheading_spacing_bottom",
          "label": "t:sections.all.content_settings.spacing.spacing_bottom.label",
          "default": 10,
          "min": 0,
          "max": 60,
          "step": 1,
          "unit": "px"
        },
        {
          "type": "paragraph",
          "content": "t:sections.all.content_settings.heading.label"
        },
        {
          "type": "range",
          "id": "heading_font_size",
          "label": "t:sections.all.content_settings.font_size.label",
          "default": 70,
          "min": 20,
          "max": 120,
          "step": 1,
          "unit": "px"
        },
        {
          "type": "select",
          "id": "heading_font_weight",
          "label": "t:sections.all.content_settings.font_weight.label",
          "default": "heading_weight",
          "options": [
            {
              "value": "body_weight",
              "label": "t:sections.all.content_settings.font_weight.body_weight.label"
            },
            {
              "value": "subheading_weight",
              "label": "t:sections.all.content_settings.font_weight.subheading_weight.label"
            },
            {
              "value": "heading_weight",
              "label": "t:sections.all.content_settings.font_weight.heading_weight.label"
            }
          ]
        },
        {
          "type": "checkbox",
          "id": "heading_uppercase",
          "label": "t:sections.all.text_transform.uppercase.label"
        },
        {
          "type": "range",
          "id": "heading_spacing_bottom",
          "label": "t:sections.all.content_settings.spacing.spacing_bottom.label",
          "default": 10,
          "min": 0,
          "max": 60,
          "step": 1,
          "unit": "px"
        },
        {
          "type": "paragraph",
          "content": "t:sections.all.content_settings.description.label"
        },
        {
          "type": "range",
          "id": "des_font_size",
          "label": "t:sections.all.content_settings.font_size.label",
          "default": 14,
          "min": 12,
          "max": 20,
          "step": 1,
          "unit": "px"
        },
        {
          "type": "select",
          "id": "des_font_weight",
          "label": "t:sections.all.content_settings.font_weight.label",
          "default": "body_weight",
          "options": [
            {
              "value": "body_weight",
              "label": "t:sections.all.content_settings.font_weight.body_weight.label"
            },
            {
              "value": "subheading_weight",
              "label": "t:sections.all.content_settings.font_weight.subheading_weight.label"
            },
            {
              "value": "heading_weight",
              "label": "t:sections.all.content_settings.font_weight.heading_weight.label"
            }
          ]
        },
        {
          "type": "range",
          "id": "des_spacing_bottom",
          "label": "t:sections.all.content_settings.spacing.spacing_bottom.label",
          "default": 40,
          "min": 0,
          "max": 60,
          "step": 1,
          "unit": "px"
        },
        {
          "type": "header",
          "content": "t:sections.all.carousel_settings.label"
        },
        {
          "type": "select",
          "id": "slide_effect",
          "label": "t:sections.all.carousel_settings.slide_effect.label",
          "options": [
            {
              "value": "fade",
              "label": "t:sections.all.carousel_settings.slide_effect.fadein"
            },
            {
              "value": "slide",
              "label": "t:sections.all.carousel_settings.slide_effect.slide"
            }
          ]
        },
        {
          "type": "checkbox",
          "id": "show_arrow",
          "label": "t:sections.all.carousel_settings.show-next-back.label",
          "default": false
        },
        {
          "type": "select",
          "id": "carousel_pagination",
          "label": "t:sections.all.carousel_settings.pagination.label",
          "options": [
            {
              "value": "disable",
              "label": "t:sections.all.carousel_settings.pagination.disable.label"
            },
            {
              "value": "show_dots",
              "label": "t:sections.all.carousel_settings.pagination.show_dots.label"
            },
            {
              "value": "show_dots_on_mobile",
              "label": "t:sections.all.carousel_settings.pagination.show_dots_on_mobile.label"
            }
          ]
        },
        {
          "type": "select",
          "id": "pagination_position",
          "label": "t:sections.all.carousel_settings.pagination_position.label",
          "default": "center",
          "options": [
            {
              "value": "left",
              "label": "t:sections.all.carousel_settings.pagination_position.left.label"
            },
            {
              "value": "center",
              "label": "t:sections.all.carousel_settings.pagination_position.center.label"
            },
            {
              "value": "right",
              "label": "t:sections.all.carousel_settings.pagination_position.right.label"
            }
          ]
        },
        {
          "type": "checkbox",
          "id": "autoplay",
          "label": "t:sections.all.carousel_settings.auto_change.label",
          "default": false
        },
        {
          "type": "range",
          "id": "autorotate_speed",
          "label": "t:sections.all.carousel_settings.change_slides_every.label",
          "max": 6,
          "min": 2,
          "step": 1,
          "unit": "s",
          "default": 5
        }
      ]
    },
    {
      "type": "slideshow_child",
      "name": "t:sections.slideshow-with-banner.blocks.slideshow_child.name",
      "limit": 5,
      "settings": [
        {
          "type": "header",
          "content": "t:sections.slideshow.blocks.slider_item.settings.header.content_image"
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "t:sections.all.image.label",
          "info": "t:sections.slideshow.blocks.slider_item.settings.image.info"
        },
        {
          "type": "image_picker",
          "id": "image_mobile",
          "label": "t:sections.all.image.mobile_image.label",
          "info": "t:sections.slideshow.blocks.slider_item.settings.image_mobile.info"
        },
        {
          "type": "video",
          "id": "local_video",
          "label": "t:sections.shopable-video.blocks.video_local.label"
        },
        {
          "type": "range",
          "id": "overlay_opacity",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "%",
          "label": "t:sections.all.image.image_overlay_opacity.label",
          "default": 0
        },
        {
          "type": "header",
          "content": "t:sections.slideshow.blocks.slider_item.settings.header.content_text"
        },
        {
          "type": "select",
          "id": "slider_effect",
          "label": "t:sections.slideshow.blocks.slider_item.settings.slider_effect.label",
          "default": "effect_fadeindown",
          "options": [
            {
              "value": "effect_fadein",
              "label": "t:sections.slideshow.blocks.slider_item.settings.slider_effect.options__1.label"
            },
            {
              "value": "effect_fadeinup",
              "label": "t:sections.slideshow.blocks.slider_item.settings.slider_effect.options__2.label"
            },
            {
              "value": "effect_fadeindown",
              "label": "t:sections.slideshow.blocks.slider_item.settings.slider_effect.options__3.label"
            },
            {
              "value": "effect_zoomin",
              "label": "t:sections.slideshow.blocks.slider_item.settings.slider_effect.options__4.label"
            },
            {
              "value": "effect_zoomout",
              "label": "t:sections.slideshow.blocks.slider_item.settings.slider_effect.options__5.label"
            }
          ]
        },
        {
          "type": "textarea",
          "id": "subheading",
          "label": "t:sections.all.contents.subheading.label",
          "default": "Subheading"
        },
        {
          "type": "textarea",
          "id": "heading",
          "label": "t:sections.all.contents.heading.label",
          "default": "Image slide"
        },
        {
          "type": "richtext",
          "id": "description",
          "label": "t:sections.all.contents.description.label",
          "default": "<p>Tell your brand's story through images</p>"
        },
        {
          "type": "text",
          "id": "first_button_label",
          "default": "Button label",
          "label": "t:sections.all.contents.button.first_button_label.label",
          "info": "t:sections.all.contents.button.first_button_label.info"
        },
        {
          "type": "url",
          "id": "first_button_link",
          "label": "t:sections.all.contents.button.first_button_link.label"
        },
        {
          "type": "checkbox",
          "id": "first_button_style",
          "label": "t:sections.all.contents.button.use_outline_button_style"
        },
        {
          "type": "text",
          "id": "second_button_label",
          "label": "t:sections.all.contents.button.secondary_button_label.label",
          "info": "t:sections.all.contents.button.first_button_label.info"
        },
        {
          "type": "url",
          "id": "second_button_link",
          "label": "t:sections.all.contents.button.secondary_button_link.label"
        },
        {
          "type": "checkbox",
          "id": "second_button_style",
          "label": "t:sections.all.contents.button.use_outline_button_style"
        },
        {
          "type": "header",
          "content": "t:sections.all.content_settings.label"
        },
        {
          "type": "select",
          "id": "content_alignment",
          "label": "t:sections.all.content_settings.content_alignment.label",
          "default": "left",
          "options": [
            {
              "value": "left",
              "label": "t:sections.all.content_settings.content_alignment.left.label"
            },
            {
              "value": "center",
              "label": "t:sections.all.content_settings.content_alignment.center.label"
            },
            {
              "value": "right",
              "label": "t:sections.all.content_settings.content_alignment.right.label"
            }
          ]
        },
        {
          "type": "select",
          "id": "content_position",
          "default": "bottom-left",
          "label": "t:sections.all.desktop_content_position.label",
          "info": "t:sections.all.desktop_content_position.info",
          "options": [
            {
              "value": "top-left",
              "label": "t:sections.all.desktop_content_position.options__1.label"
            },
            {
              "value": "top-center",
              "label": "t:sections.all.desktop_content_position.options__2.label"
            },
            {
              "value": "top-right",
              "label": "t:sections.all.desktop_content_position.options__3.label"
            },
            {
              "value": "middle-left",
              "label": "t:sections.all.desktop_content_position.options__4.label"
            },
            {
              "value": "middle-center",
              "label": "t:sections.all.desktop_content_position.options__5.label"
            },
            {
              "value": "middle-right",
              "label": "t:sections.all.desktop_content_position.options__6.label"
            },
            {
              "value": "bottom-left",
              "label": "t:sections.all.desktop_content_position.options__7.label"
            },
            {
              "value": "bottom-center",
              "label": "t:sections.all.desktop_content_position.options__8.label"
            },
            {
              "value": "bottom-right",
              "label": "t:sections.all.desktop_content_position.options__9.label"
            }
          ]
        },
        {
          "type": "select",
          "id": "content_width",
          "label": "t:sections.all.content_settings.content_width.label",
          "default": "container",
          "options": [
            {
              "value": "container",
              "label": "t:sections.all.section_width.container.label"
            },
            {
              "value": "fluid_container",
              "label": "t:sections.all.section_width.fluid_container.label"
            },
            {
              "value": "full_width",
              "label": "t:sections.all.section_width.full_width.label"
            }
          ]
        },
        {
          "type": "range",
          "id": "content_max_width",
          "min": 40,
          "max": 100,
          "step": 1,
          "unit": "%",
          "default": 100,
          "label": "t:sections.all.content_settings.max_width.label",
          "info": "t:sections.all.content_settings.max_width.info"
        },
        {
          "type": "range",
          "id": "content_padding_block",
          "min": 40,
          "max": 120,
          "step": 5,
          "unit": "px",
          "default": 60,
          "label": "t:sections.all.content_settings.content_padding_block.label"
        },
        {
          "type": "range",
          "id": "content_padding_inline",
          "min": 0,
          "max": 120,
          "step": 5,
          "unit": "px",
          "default": 30,
          "label": "t:sections.all.content_settings.content_padding_inline.label"
        },
        {
          "type": "header",
          "content": "t:sections.all.content_box.label"
        },
        {
          "type": "checkbox",
          "id": "show_content_background",
          "label": "t:sections.all.content_box.show_content_background.label"
        },
        {
          "type": "color",
          "id": "content_background",
          "label": "t:sections.all.content_box.content_background.label",
          "default": "#fff"
        },
        {
          "type": "range",
          "id": "content_box_opacity",
          "label": "t:sections.all.content_box.content_box_opacity.label",
          "default": 100,
          "min": 10,
          "max": 100,
          "step": 1,
          "unit": "%"
        },
        {
          "type": "header",
          "content": "t:sections.all.mobile_options.label"
        },
        {
          "type": "select",
          "id": "content_alignment_mobile",
          "label": "t:sections.all.content_settings.content_alignment.label",
          "default": "left",
          "options": [
            {
              "value": "left",
              "label": "t:sections.all.content_settings.content_alignment.left.label"
            },
            {
              "value": "center",
              "label": "t:sections.all.content_settings.content_alignment.center.label"
            },
            {
              "value": "right",
              "label": "t:sections.all.content_settings.content_alignment.right.label"
            }
          ]
        }
      ]
    },
    {
      "type": "banner_parent",
      "name": "t:sections.slideshow-with-banner.blocks.banner_parent.name",
      "limit": 1,
      "settings": []
    },
    {
      "type": "banner_child",
      "name": "t:sections.slideshow-with-banner.blocks.banner_child.name",
      "limit": 3,
      "settings": [
        {
          "type": "color_scheme",
          "id": "color_scheme",
          "label": "t:sections.all.color_scheme.label",
          "default": "scheme-1"
        },
        {
          "type": "select",
          "id": "vertical_align",
          "label": "t:sections.multi_content.block_settings.vertical_align.label",
          "default": "start",
          "options": [
            {
              "value": "start",
              "label": "t:sections.multi_content.block_settings.vertical_align.top.label"
            },
            {
              "value": "center",
              "label": "t:sections.multi_content.block_settings.vertical_align.middle.label"
            },
            {
              "value": "end",
              "label": "t:sections.multi_content.block_settings.vertical_align.bottom.label"
            }
          ]
        },
        {
          "type": "header",
          "content": "t:sections.all.image.label"
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "t:sections.all.image.label"
        },
        {
          "type": "image_picker",
          "id": "mobile_image",
          "label": "t:sections.all.image.mobile_image.label"
        },
        {
          "type": "url",
          "id": "image_link",
          "label": "t:sections.all.image.link"
        },
        {
          "type": "range",
          "id": "image_overlay_opacity",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "%",
          "label": "t:sections.all.image.overlay_opacity.label",
          "default": 0
        },
        {
          "type": "header",
          "content": "t:sections.all.contents.label"
        },
        {
          "type": "textarea",
          "id": "custom_svg",
          "label": "t:sections.all.contents.custom_svg.label"
        },
        {
          "type": "text",
          "id": "subheading",
          "default": "Subheading",
          "label": "t:sections.all.contents.subheading.label"
        },
        {
          "type": "textarea",
          "id": "heading",
          "label": "t:sections.all.contents.heading.label",
          "default": "Text overlay"
        },
        {
          "type": "richtext",
          "id": "description",
          "label": "t:sections.all.contents.description.label",
          "default": "<p>Description</p>"
        },
        {
          "type": "text",
          "id": "first_button_label",
          "default": "Button label",
          "label": "t:sections.all.contents.button.first_button_label.label",
          "info": "t:sections.all.contents.button.first_button_label.info"
        },
        {
          "type": "url",
          "id": "first_button_link",
          "label": "t:sections.all.contents.button.first_button_link.label"
        },
        {
          "type": "select",
          "id": "first_button_type",
          "label": "t:sections.all.contents.button.button_type.label",
          "default": "primary",
          "options": [
            {
              "value": "primary",
              "label": "t:sections.all.contents.button.button_type.primary.label"
            },
            {
              "value": "outline",
              "label": "t:sections.all.contents.button.button_type.outline.label"
            },
            {
              "value": "link",
              "label": "t:sections.all.contents.button.button_type.link.label"
            }
          ]
        },
        {
          "type": "text",
          "id": "second_button_label",
          "label": "t:sections.all.contents.button.secondary_button_label.label",
          "info": "t:sections.all.contents.button.secondary_button_label.info"
        },
        {
          "type": "url",
          "id": "second_button_link",
          "label": "t:sections.all.contents.button.secondary_button_link.label"
        },
        {
          "type": "select",
          "id": "second_button_type",
          "label": "t:sections.all.contents.button.button_type.label",
          "default": "primary",
          "options": [
            {
              "value": "primary",
              "label": "t:sections.all.contents.button.button_type.primary.label"
            },
            {
              "value": "outline",
              "label": "t:sections.all.contents.button.button_type.outline.label"
            },
            {
              "value": "link",
              "label": "t:sections.all.contents.button.button_type.link.label"
            }
          ]
        },
        {
          "type": "header",
          "content": "t:sections.all.content_setting.label"
        },
        {
          "type": "select",
          "id": "content_alignment",
          "label": "t:sections.all.content_settings.content_alignment.label",
          "default": "left",
          "options": [
            {
              "value": "left",
              "label": "t:sections.all.content_settings.content_alignment.left.label"
            },
            {
              "value": "center",
              "label": "t:sections.all.content_settings.content_alignment.center.label"
            },
            {
              "value": "right",
              "label": "t:sections.all.content_settings.content_alignment.right.label"
            }
          ]
        },
        {
          "type": "select",
          "id": "content_position",
          "default": "top-left",
          "label": "t:sections.all.content_settings.content_position.label",
          "options": [
            {
              "value": "top-left",
              "label": "t:sections.all.content_settings.content_position.top_left.label"
            },
            {
              "value": "top-center",
              "label": "t:sections.all.content_settings.content_position.top_center.label"
            },
            {
              "value": "top-right",
              "label": "t:sections.all.content_settings.content_position.top_right.label"
            },
            {
              "value": "middle-left",
              "label": "t:sections.all.content_settings.content_position.middle_left.label"
            },
            {
              "value": "middle-center",
              "label": "t:sections.all.content_settings.content_position.middle_center.label"
            },
            {
              "value": "middle-right",
              "label": "t:sections.all.content_settings.content_position.middle_right.label"
            },
            {
              "value": "bottom-left",
              "label": "t:sections.all.content_settings.content_position.bottom_left.label"
            },
            {
              "value": "bottom-center",
              "label": "t:sections.all.content_settings.content_position.bottom_center.label"
            },
            {
              "value": "bottom-right",
              "label": "t:sections.all.content_settings.content_position.bottom_right.label"
            }
          ]
        },
        {
          "type": "range",
          "id": "content_padding_block",
          "min": 10,
          "max": 60,
          "step": 1,
          "unit": "px",
          "default": 30,
          "label": "t:sections.all.content_settings.content_padding_block.label"
        },
        {
          "type": "range",
          "id": "content_padding_inline",
          "min": 10,
          "max": 60,
          "step": 1,
          "unit": "px",
          "default": 30,
          "label": "t:sections.all.content_settings.content_padding_inline.label"
        },
        {
          "type": "header",
          "content": "t:sections.all.mobile_options.label"
        },
        {
          "type": "header",
          "content": "t:sections.all.content_settings.typography.label",
          "info": "t:sections.all.content_setting.info"
        },
        {
          "type": "paragraph",
          "content": "t:sections.all.content_settings.custom_svg.label"
        },
        {
          "type": "range",
          "id": "custom_svg_spacing_bottom",
          "label": "t:sections.all.content_settings.spacing.spacing_bottom.label",
          "default": 10,
          "min": 0,
          "max": 30,
          "step": 1,
          "unit": "px"
        },
        {
          "type": "paragraph",
          "content": "t:sections.all.content_settings.subheading.label"
        },
        {
          "type": "range",
          "id": "subheading_font_size",
          "label": "t:sections.all.content_settings.font_size.label",
          "default": 12,
          "min": 10,
          "max": 30,
          "step": 1,
          "unit": "px"
        },
        {
          "type": "select",
          "id": "subheading_font_weight",
          "label": "t:sections.all.content_settings.font_weight.label",
          "default": "subheading_weight",
          "options": [
            {
              "value": "body_weight",
              "label": "t:sections.all.content_settings.font_weight.body_weight.label"
            },
            {
              "value": "subheading_weight",
              "label": "t:sections.all.content_settings.font_weight.subheading_weight.label"
            },
            {
              "value": "heading_weight",
              "label": "t:sections.all.content_settings.font_weight.heading_weight.label"
            }
          ]
        },
        {
          "type": "range",
          "id": "subheading_spacing_bottom",
          "label": "t:sections.all.content_settings.spacing.spacing_bottom.label",
          "default": 10,
          "min": 0,
          "max": 30,
          "step": 1,
          "unit": "px"
        },
        {
          "type": "paragraph",
          "content": "t:sections.all.content_settings.heading.label"
        },
        {
          "type": "range",
          "id": "heading_font_size",
          "label": "t:sections.all.content_settings.font_size.label",
          "default": 36,
          "min": 10,
          "max": 90,
          "step": 1,
          "unit": "px"
        },
        {
          "type": "select",
          "id": "heading_font_weight",
          "label": "t:sections.all.content_settings.font_weight.label",
          "default": "heading_weight",
          "options": [
            {
              "value": "body_weight",
              "label": "t:sections.all.content_settings.font_weight.body_weight.label"
            },
            {
              "value": "subheading_weight",
              "label": "t:sections.all.content_settings.font_weight.subheading_weight.label"
            },
            {
              "value": "heading_weight",
              "label": "t:sections.all.content_settings.font_weight.heading_weight.label"
            }
          ]
        },
        {
          "type": "checkbox",
          "id": "heading_uppercase",
          "label": "t:sections.all.text_transform.uppercase.label"
        },
        {
          "type": "range",
          "id": "heading_spacing_bottom",
          "label": "t:sections.all.content_settings.spacing.spacing_bottom.label",
          "default": 10,
          "min": 0,
          "max": 30,
          "step": 1,
          "unit": "px"
        },

        {
          "type": "paragraph",
          "content": "t:sections.all.content_settings.description.label"
        },
        {
          "type": "range",
          "id": "des_font_size",
          "label": "t:sections.all.content_settings.font_size.label",
          "default": 14,
          "min": 10,
          "max": 30,
          "step": 1,
          "unit": "px"
        },
        {
          "type": "select",
          "id": "des_font_weight",
          "label": "t:sections.all.content_settings.font_weight.label",
          "default": "body_weight",
          "options": [
            {
              "value": "body_weight",
              "label": "t:sections.all.content_settings.font_weight.body_weight.label"
            },
            {
              "value": "subheading_weight",
              "label": "t:sections.all.content_settings.font_weight.subheading_weight.label"
            },
            {
              "value": "heading_weight",
              "label": "t:sections.all.content_settings.font_weight.heading_weight.label"
            }
          ]
        },
        {
          "type": "range",
          "id": "des_spacing_bottom",
          "label": "t:sections.all.content_settings.spacing.spacing_bottom.label",
          "default": 10,
          "min": 0,
          "max": 40,
          "step": 1,
          "unit": "px"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "t:sections.slideshow-with-banner.name"
    }
  ]
}
{% endschema %}
