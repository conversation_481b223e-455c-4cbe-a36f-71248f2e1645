{{ 'password.css' | asset_url | stylesheet_tag }}
{{ 'page-password.css' | asset_url | stylesheet_tag }}
<style type="text/css">
  .password-logo {
    max-width: {{ settings.desktop_logo_width }}px;
  }
</style>
{% liquid
  assign st = section.settings
  assign banner_img = st.banner_img
  assign banner_position = st.banner_position
  assign text_horizontal_position = st.text_horizontal_position
  assign content_width = st.content_width

  if banner_img != blank
    assign show_banner = ' show-banner'
  endif
  assign banner_position_class = ''
  if banner_position == 'position-right'
    assign banner_position_class = ' flex-row-reverse'
    if text_horizontal_position == 'left'
      assign text_horizontal_position = 'right'
    elsif text_horizontal_position == 'right'
      assign text_horizontal_position = 'left'
    endif
  endif
%}

<div class="bls-main-password {{ banner_position_class }}   {% if   banner_img != blank  %} justify-content-{{ text_horizontal_position }} {%  else %} justify-content-center {% endif %} ">
  {% if banner_img != blank %}
    <div class="image_banner">
      <div class="bls__password-banner relative d-none block-xl">
        {%- assign image_alt = banner_img.alt | default: 'banner_image' -%}
        <img
          loading="lazy"
          src="{{ banner_img | image_url }}"
          alt="{{ image_alt }}"
          width="{{ banner_img.width }}"
          height="{{ banner_img.height }}"
        >
      </div>
    </div>
  {% endif %}
  <div class="main-content section-full">
    <div class="wrapper flex justify-between flex-column">
      <div class="color-{{ section.settings.color_scheme }} gradient">
        <div class="password-header">
          <div class="password-logo-layout">
            {%- if settings.logo != blank -%}
              {%- assign logo_alt = settings.logo.alt | default: shop.name | escape -%}
              {%- assign logo_height = settings.desktop_logo_width | divided_by: settings.logo.aspect_ratio -%}
              {{
                settings.logo
                | image_url: width: 500
                | image_tag:
                  class: 'password-logo',
                  widths: '50, 100, 150, 200, 250, 300, 400, 500',
                  width: settings.logo_width,
                  height: logo_height,
                  alt: logo_alt
              }}
            {%- else -%}
              <h1 class="h2">{{ shop.name }}</h1>
            {% endif %}
            <svg xmlns="http://www.w3.org/2000/svg" width="1" height="20" viewBox="0 0 1 20" fill="none">
              <rect width="1" height="20" fill="#111111"/>
            </svg>
            <p>Comming soon</p>
          </div>

      

          <password-modal>
            <div class="password-modal modal">
              <div class="modal__toggle" aria-haspopup="dialog">
                <div class="modal__toggle-open password-link link underlined-link">
                  {% render 'icon-padlock' %}
                  <h2 class="password-modal__content-heading" id="DialogHeading">
                    {{ 'general.password_page.login_password_button' | t }}
                  </h2>
                </div>
              </div>
              <div
                class="modal__content"
                role="dialog"
                aria-labelledby="DialogHeading"
                aria-modal="true"
              >
                <div
                  class="password-modal__content"
                  tabindex="-1"
                  data-title="{{ 'general.password_page.login_form_heading' | t }}"
                >
                  <h2 class="password-modal-title">{{ 'general.password_page.login_form_heading' | t }}</h2>
                  {%- form 'storefront_password', class: 'password-form' -%}
                    <div class="password-field field{% if form.errors %} password-field--error{% endif %}">
                      <input
                        type="password"
                        name="password"
                        id="Password"
                        class="field__input field__input-pass"
                        autocomplete="current-password"
                        {% if form.errors %}
                          aria-invalid="true"
                          aria-describedby="PasswordLoginForm-password-error"
                        {% endif %}
                        placeholder="{{ 'general.password_page.login_form_password_placeholder' | t }}"
                      >

                      {%- if form.errors -%}
                        <div id="PasswordLoginForm-password-error" role="status">
                          <span class="visually-hidden">{{ 'accessibility.error' | t }}</span>
                          <span class="form__message">
                            {%- render 'icon-error' %}
                            {{ 'general.password_page.login_form_error' | t -}}
                          </span>
                        </div>
                      {% endif %}
                    </div>
                    <button name="commit" class="password-button btn btn-primary">
                      {{ 'general.password_page.login_form_submit' | t }}
                    </button>
                  {%- endform -%}
                  <small class="password__footer-text">{{ 'general.password_page.admin_link_html' | t }}</small>
                </div>
              </div>
            </div>
          </password-modal>
        </div>
        {%- if shop.password_message != blank -%}
          <div class="password-content mb-10">
            {{ shop.password_message }}
          </div>
        {% endif %}
      </div>

      <div
        class="row flex-nowrap {{ banner_position_class }} bls__banner-text--grid"
      >
        <div class="bls__banner-text--content {{ show_banner }} text-{{ st.text_align }}">
          <div
            class="bls__password-with-banner bls__banner-text--content-inner content-horizontal-{{ text_horizontal_position }}"
            style="--content-width:{{ content_width }}%"
          >
            {%- for block in section.blocks -%}
              {%- liquid
                assign bs = block.settings
                assign heading_fs = bs.heading_fs
                assign heading_color = bs.heading_color
                assign heading_fw = bs.heading_fw
                assign heading_tf = bs.heading_tf
                assign heading_mt = bs.heading_mt
                assign heading_mb = bs.heading_mb
                assign subheading_fs = bs.subheading_fs
                assign subheading_color = bs.subheading_color
                assign subheading_fw = bs.subheading_fw
                assign subheading_tf = bs.subheading_tf
                assign subheading_mt = bs.subheading_mt
                assign subheading_mb = bs.subheading_mb
                assign txt_color = bs.txt_color
                assign txt_fs = bs.txt_fs
                assign txt_fw = bs.txt_fw
                assign txt_tf = bs.txt_tf
                assign txt_mt = bs.txt_mt
                assign txt_mb = bs.txt_mb
                assign countdown_design = bs.countdown_design
                assign bn_end_time = bs.bn_end_time
                assign bn_txt_color = bs.bn_txt_color
                assign bn_txt_fs = bs.bn_txt_fs
                assign number_txt_fs = bs.number_txt_fs
                assign bn_txt_fw = bs.bn_txt_fw
                assign bn_txt_tf = bs.bn_txt_tf
                assign bn_txt_mt = bs.bn_txt_mt
                assign bn_txt_mb = bs.bn_txt_mb
                assign form_txt_mb = bs.form_txt_mb
                assign form_txt_mt = bs.form_txt_mt
              -%}
              {% case block.type %}
                {%- when 'subheading' -%}
                  <h4
                    {{ block.shopify_attributes }}
                    class="bls__banner-text--subheading "
                    style="--text-subheading-font-size: {{ subheading_fs }}px; --text-subheading-font-weight: {{ subheading_fw }}; --color-heading: {{ subheading_color }}; --text-subheading-transform: {{ subheading_tf }}; --text-subheading-mt: {{ subheading_mt }}px; --text-subheading-mb: {{ subheading_mb }}px;"
                  >
                    {{ block.settings.sub_title | escape }}
                  </h4>
                {%- when 'heading' -%}
                  <h2
                    {{ block.shopify_attributes }}
                    class="bls__banner-text--heading "
                    style="--text-heading-font-size: {{ heading_fs }}px; --text-heading-font-weight: {{ heading_fw }}; --color-heading: {{ heading_color }}; --text-heading-transform: {{ heading_tf }}; --text-heading-mt: {{ heading_mt }}px; --text-heading-mb: {{ heading_mb }}px;"
                  >
                    {{ block.settings.title | escape }}
                  </h2>
                {%- when 'text' -%}
                  <div
                    {{ block.shopify_attributes }}
                    class="bls__banner-text--des "
                    style="--text-font-size: {{ txt_fs }}px;{%- if txt_color != blank -%} --text-color: {{ txt_color }};{% endif %} --text-font-weight: {{ txt_fw }}; --text-transform: {{ txt_tf }}; --text-mt: {{ txt_mt }}px; --text-mb: {{ txt_mb }}px;"
                  >
                    {{ block.settings.txt_content }}
                  </div>
                {%- when 'countdown_bn' -%}
                  {% if bn_end_time != blank %}
                    <div
                      {{ block.shopify_attributes }}
                      class="{{ countdown_design }}  bls__timer"
                      data-timer="{{ bn_end_time }}"
                    >
                      <div class="d-inline-flex">
                        <div
                          class="timer-display timer-banner flex"
                          style="--bn-number-font-size: {{ number_txt_fs }}px;--bn-text-font-size: {{ bn_txt_fs }}px;{%- if bn_txt_color != blank -%} --bn-text-color: {{ bn_txt_color }};{% endif %} --bn-text-font-weight: {{ bn_txt_fw }}; --bn-text-transform: {{ bn_txt_tf }}; --bn-text-mt: {{ bn_txt_mt }}px; --bn-text-mb: {{ bn_txt_mb }}px;"
                        >
                          <div class="timer-block">
                            <span class="timer-block__num js-timer-days">00</span>
                            <span class="timer-block__text">{{ 'sections.times_bn.days' | t }}</span>
                          </div>
                          <div class="timer-block">
                            <span class="timer-block__num js-timer-hours">00</span>
                            <span class="timer-block__text">{{ 'sections.times_bn.hour' | t }}</span>
                          </div>
                          <div class="timer-block">
                            <span class="timer-block__num js-timer-minutes">00</span>
                            <span class="timer-block__text">{{ 'sections.times_bn.mins' | t }}</span>
                          </div>
                          <div class="timer-block">
                            <span class="timer-block__num js-timer-seconds">00</span>
                            <span class="timer-block__text">{{ 'sections.times_bn.secs' | t }}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  {% endif %}
                {%- when 'form_subscribe' -%}
                  <div
                    {{ block.shopify_attributes }}
                    class="bls__subcribe-submit"
                    style="--mb: {{ form_txt_mb }}px; --mt:{{  form_txt_mt }}px"
                  >
                    {%- form 'customer', class: 'subscribe-form' -%}
                      <input
                        type="hidden"
                        name="contact[tags]"
                        value="newsletter"
                      >
                      <div class=" flex flex-wrap gap-10">
                        <input
                          type="email"
                          required
                          name="contact[email]"
                          value="{% if customer %}{{ customer.email }}{% endif %}"
                          placeholder="{{ block.settings.email_placeholder }}"
                          class="bls__subcribe-input btn-radius mr-10 flex-fill"
                        >
                        <button
                          type="submit"
                          name="commit"
                          class="bls__subcribe-button btn btn-primary "
                        >
                          {{ block.settings.btn_label }}
                        </button>
                      </div>
                    {%- endform -%}
                  </div>
              {%- endcase -%}
            {%- endfor -%}
          </div>
        </div>
      </div>
      <div></div>
    </div>
  </div>
</div>

{% schema %}
{
  "name": "t:sections.main-password-content.name",
  "tag": "section",
  "settings": [
    {
      "type": "header",
      "content": "t:sections.main-password-content.settings.header__1.content",
      "info": "t:sections.main-password-content.settings.header__1.info"
    },
    {
      "type": "image_picker",
      "id": "banner_img",
      "label": "t:sections.main-password-content.settings.banner_img.label"
    },
    {
      "type": "select",
      "id": "banner_position",
      "label": "t:sections.main-password-content.settings.banner_position.label",
      "default": "position-right",
      "options": [
        {
          "value": "position-left",
          "label": "t:sections.main-password-content.settings.banner_position.options__1.label"
        },
        {
          "value": "position-right",
          "label": "t:sections.main-password-content.settings.banner_position.options__2.label"
        }
      ]
    },
    {
      "type": "header",
      "content": "t:sections.main-password-content.settings.header__2.content"
    },
    {
      "type": "select",
      "id": "text_horizontal_position",
      "label": "t:sections.main-password-content.settings.text_horizontal_position.label",
      "default": "left",
      "options": [
        {
          "value": "left",
          "label": "t:sections.main-password-content.settings.text_horizontal_position.options__1.label"
        },
        {
          "value": "center",
          "label": "t:sections.main-password-content.settings.text_horizontal_position.options__2.label"
        },
        {
          "value": "right",
          "label": "t:sections.main-password-content.settings.text_horizontal_position.options__3.label"
        }
      ]
    },
    {
      "type": "select",
      "id": "text_align",
      "label": "t:sections.all.section_header.alignment.label",
      "default": "center",
      "options": [
        {
          "value": "left",
          "label": "t:sections.all.content_settings.content_alignment.left.label"
        },
        {
          "value": "center",
          "label": "t:sections.all.content_settings.content_alignment.center.label"
        },
        {
          "value": "right",
          "label": "t:sections.all.content_settings.content_alignment.right.label"
        }
      ]
    },
    {
      "type": "range",
      "id": "content_width",
      "label": "t:sections.main-password-content.settings.content_width.label",
      "min": 30,
      "max": 100,
      "step": 1,
      "unit": "%",
      "default": 100
    }
  ],
  "blocks": [
    {
      "type": "heading",
      "name": "t:sections.main-password-content.blocks.heading.name",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "t:sections.all.page_heading.heading.label",
          "default": "Heading"
        },
        {
          "type": "color",
          "id": "heading_color",
          "label": "t:sections.main-password-content.blocks.heading.settings.heading_color.label",
          "default": "#111"
        },
        {
          "type": "range",
          "id": "heading_fs",
          "label": "t:sections.main-password-content.blocks.heading.settings.heading_fs.label",
          "min": 20,
          "max": 120,
          "step": 1,
          "unit": "px",
          "default": 40
        },
        {
          "type": "range",
          "id": "heading_fw",
          "label": "t:sections.main-password-content.blocks.heading.settings.heading_fw.label",
          "min": 100,
          "max": 900,
          "step": 100,
          "default": 500
        },
        {
          "type": "select",
          "id": "heading_tf",
          "label": "t:sections.main-password-content.blocks.heading.settings.heading_tf.label",
          "options": [
            {
              "value": "unset",
              "label": "t:sections.main-password-content.blocks.heading.settings.heading_tf.options__1.label"
            },
            {
              "value": "capitalize",
              "label": "t:sections.main-password-content.blocks.heading.settings.heading_tf.options__2.label"
            },
            {
              "value": "uppercase",
              "label": "t:sections.main-password-content.blocks.heading.settings.heading_tf.options__3.label"
            }
          ]
        },
        {
          "type": "header",
          "content": "t:sections.main-password-content.blocks.heading.settings.header__1.content"
        },
        {
          "type": "range",
          "id": "heading_mt",
          "min": 0,
          "max": 40,
          "step": 1,
          "unit": "px",
          "default": 0,
          "label": "t:sections.main-password-content.blocks.heading.settings.heading_mt.label"
        },
        {
          "type": "range",
          "id": "heading_mb",
          "min": 0,
          "max": 40,
          "step": 1,
          "unit": "px",
          "default": 10,
          "label": "t:sections.main-password-content.blocks.heading.settings.heading_mb.label"
        }
      ]
    },
    {
      "type": "subheading",
      "name": "t:sections.main-password-content.blocks.subheading.name",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "sub_title",
          "label": "t:sections.main-password-content.blocks.subheading.settings.sub_title.label",
          "default": "Subheading"
        },
        {
          "type": "color",
          "id": "subheading_color",
          "label": "t:sections.main-password-content.blocks.subheading.settings.subheading_color.label",
          "default": "#111"
        },
        {
          "type": "range",
          "id": "subheading_fs",
          "label": "t:sections.main-password-content.blocks.subheading.settings.subheading_fs.label",
          "min": 10,
          "max": 20,
          "step": 1,
          "unit": "px",
          "default": 14
        },
        {
          "type": "range",
          "id": "subheading_fw",
          "label": "t:sections.main-password-content.blocks.subheading.settings.subheading_fw.label",
          "min": 100,
          "max": 900,
          "step": 100,
          "default": 500
        },
        {
          "type": "select",
          "id": "subheading_tf",
          "label": "t:sections.main-password-content.blocks.subheading.settings.subheading_tf.label",
          "options": [
            {
              "value": "unset",
              "label": "t:sections.main-password-content.blocks.subheading.settings.subheading_tf.options__1.label"
            },
            {
              "value": "capitalize",
              "label": "t:sections.main-password-content.blocks.subheading.settings.subheading_tf.options__2.label"
            },
            {
              "value": "uppercase",
              "label": "t:sections.main-password-content.blocks.subheading.settings.subheading_tf.options__3.label"
            }
          ]
        },
        {
          "type": "header",
          "content": "t:sections.main-password-content.blocks.subheading.settings.header__1.content"
        },
        {
          "type": "range",
          "id": "subheading_mt",
          "min": 0,
          "max": 40,
          "step": 1,
          "unit": "px",
          "default": 0,
          "label": "t:sections.main-password-content.blocks.subheading.settings.subheading_mt.label"
        },
        {
          "type": "range",
          "id": "subheading_mb",
          "min": 0,
          "max": 40,
          "step": 1,
          "unit": "px",
          "default": 10,
          "label": "t:sections.main-password-content.blocks.subheading.settings.subheading_mb.label"
        }
      ]
    },
    {
      "type": "countdown_bn",
      "name": "t:sections.main-password-content.blocks.countdown_bn.name",
      "limit": 1,
      "settings": [
        {
          "type": "select",
          "id": "countdown_design",
          "default": "countdown_design_1",
          "label": "t:sections.main-password-content.blocks.countdown_bn.settings.countdown_design.label",
          "options": [
            {
              "value": "countdown_design_1",
              "label": "t:sections.main-password-content.blocks.countdown_bn.settings.countdown_design.options__1.label"
            },
            {
              "value": "countdown_design_2",
              "label": "t:sections.main-password-content.blocks.countdown_bn.settings.countdown_design.options__2.label"
            },
            {
              "value": "countdown_design_3",
              "label": "t:sections.main-password-content.blocks.countdown_bn.settings.countdown_design.options__3.label"
            }
          ]
        },
        {
          "type": "text",
          "id": "bn_end_time",
          "label": "t:sections.main-password-content.blocks.countdown_bn.settings.bn_end_time.label",
          "default": "12-24-2025",
          "info": "e.g. mm-dd-yyyy hh:mm:ss"
        },
        {
          "type": "color",
          "id": "bn_txt_color",
          "label": "t:sections.main-password-content.blocks.countdown_bn.settings.bn_txt_color.label"
        },
        {
          "type": "range",
          "id": "bn_txt_fs",
          "label": "t:sections.main-password-content.blocks.countdown_bn.settings.bn_txt_fs.label",
          "min": 8,
          "max": 30,
          "step": 1,
          "unit": "px",
          "default": 18
        },
        {
          "type": "range",
          "id": "number_txt_fs",
          "label": "t:sections.main-password-content.blocks.countdown_bn.settings.number_txt_fs.label",
          "min": 24,
          "max": 80,
          "step": 1,
          "unit": "px",
          "default": 36
        },
        {
          "type": "range",
          "id": "bn_txt_fw",
          "label": "t:sections.main-password-content.blocks.countdown_bn.settings.bn_txt_fw.label",
          "min": 100,
          "max": 900,
          "step": 100,
          "default": 400
        },
        {
          "type": "select",
          "id": "bn_txt_tf",
          "label": "t:sections.main-password-content.blocks.countdown_bn.settings.bn_txt_tf.label",
          "options": [
            {
              "value": "unset",
              "label": "t:sections.main-password-content.blocks.countdown_bn.settings.bn_txt_tf.options__1.label"
            },
            {
              "value": "capitalize",
              "label": "t:sections.main-password-content.blocks.countdown_bn.settings.bn_txt_tf.options__2.label"
            },
            {
              "value": "uppercase",
              "label": "t:sections.main-password-content.blocks.countdown_bn.settings.bn_txt_tf.options__3.label"
            },
            {
              "value": "lowercase",
              "label": "t:sections.main-password-content.blocks.countdown_bn.settings.bn_txt_tf.options__4.label"
            }
          ]
        },
        {
          "type": "header",
          "content": "t:sections.main-password-content.blocks.countdown_bn.settings.header__1.content"
        },
        {
          "type": "range",
          "id": "bn_txt_mt",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "px",
          "default": 0,
          "label": "t:sections.main-password-content.blocks.countdown_bn.settings.bn_txt_mt.label"
        },
        {
          "type": "range",
          "id": "bn_txt_mb",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "px",
          "default": 25,
          "label": "t:sections.main-password-content.blocks.countdown_bn.settings.bn_txt_mt.label"
        }
      ]
    },
    {
      "type": "form_subscribe",
      "name": "t:sections.main-password-content.blocks.form_subscribe.name",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "email_placeholder",
          "label": "t:sections.main-password-content.blocks.form_subscribe.settings.email_placeholder.label",
          "default": "Enter your email..."
        },
        {
          "type": "text",
          "id": "btn_label",
          "label": "t:sections.main-password-content.blocks.form_subscribe.settings.btn_label.label",
          "default": "Subscribe"
        },
        {
          "type": "header",
          "content": "t:sections.main-password-content.blocks.form_subscribe.settings.header__1.content"
        },
        {
          "type": "range",
          "id": "form_txt_mt",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "px",
          "default": 0,
          "label": "t:sections.main-password-content.blocks.form_subscribe.settings.form_txt_mt.label"
        },
        {
          "type": "range",
          "id": "form_txt_mb",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "px",
          "default": 25,
          "label": "t:sections.main-password-content.blocks.form_subscribe.settings.form_txt_mb.label"
        }
      ]
    },
    {
      "type": "text",
      "name": "t:sections.main-password-content.blocks.text.name",
      "limit": 1,
      "settings": [
        {
          "type": "richtext",
          "id": "txt_content",
          "label": "t:sections.main-password-content.blocks.text.settings.txt_content.label",
          "default": "<p>This shop will be powered by ALukas - Multipurpose Premium Shopify theme (password: 1). Are you the store owner? Log in here</p>"
        },
        {
          "type": "range",
          "id": "txt_fs",
          "label": "t:sections.main-password-content.blocks.text.settings.txt_fs.label",
          "min": 8,
          "max": 30,
          "step": 1,
          "unit": "px",
          "default": 16
        },
        {
          "type": "range",
          "id": "txt_fw",
          "label": "t:sections.main-password-content.blocks.text.settings.txt_fw.label",
          "min": 100,
          "max": 900,
          "step": 100,
          "default": 400
        },
        {
          "type": "select",
          "id": "txt_tf",
          "label": "t:sections.main-password-content.blocks.text.settings.txt_tf.label",
          "options": [
            {
              "value": "unset",
              "label": "t:sections.main-password-content.blocks.text.settings.txt_tf.options__1.label"
            },
            {
              "value": "capitalize",
              "label": "t:sections.main-password-content.blocks.text.settings.txt_tf.options__2.label"
            },
            {
              "value": "uppercase",
              "label": "t:sections.main-password-content.blocks.text.settings.txt_tf.options__3.label"
            },
            {
              "value": "lowercase",
              "label": "t:sections.main-password-content.blocks.text.settings.txt_tf.options__4.label"
            }
          ]
        },
        {
          "type": "header",
          "content": "t:sections.main-password-content.blocks.text.settings.header__1.content"
        },
        {
          "type": "range",
          "id": "txt_mt",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "px",
          "default": 0,
          "label": "t:sections.main-password-content.blocks.text.settings.txt_mt.label"
        },
        {
          "type": "range",
          "id": "txt_mb",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "px",
          "default": 25,
          "label": "t:sections.main-password-content.blocks.text.settings.txt_mb.label"
        }
      ]
    }
  ]
}
{% endschema %}
