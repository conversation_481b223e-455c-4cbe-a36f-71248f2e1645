.sec__inner .banner__media :is(picture, source, img) {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.sec__inner .banner__media .image-picture {
  display: block;
}
@media screen and (max-width: 767.98px) {
  .text-below-mb[style*="--aspect-ratio"]:before {
    display: none;
  }
  .text-below-mb .py-custom {
    padding-top: 0;
    padding-bottom: 0;
    --color-heading: #111;
    --btn-primary-bg-graident-color: #111;
    --btn-color: #fff;
    --btn-primary-color: #fff;
  }
  .text-below-mb.h-custom,
  .text-below-mb.h-full_screen,
  .text-below-mb .h-full_screen {
    min-height: auto;
  }
  .text-below-mb.h-custom .banner__media {
    height: var(--height);
    position: relative;
  }
  multi-content.swiper.row-gap {
    row-gap: 0;
  }
  multi-content[data-enable="true"] > .section__block:not(:first-child) {
    display: none;
  }
}
.sec__content:not(.show-content-box):is(
    .container:not(.px-custom),
    .fluid_container:not(.px-custom)
  ) {
  padding-left: 0;
  padding-right: 0;
}
:where(.px-custom:not(.x-min-value)) {
  padding-left: clamp(
    15px,
    calc(15px + (var(--padding-inline) - 15) * var(--responsive-rate)),
    calc(var(--padding-inline) * 1px)
  );
  padding-right: clamp(
    15px,
    calc(15px + (var(--padding-inline) - 15) * var(--responsive-rate)),
    calc(var(--padding-inline) * 1px)
  );
}
:where(.x-min-value) {
  padding-left: calc(var(--padding-inline) * 1px);
  padding-right: calc(var(--padding-inline) * 1px);
}
:where(.px-custom.min-value:not(.container, .fluid_container)) {
  padding-left: clamp(
    calc(var(--padding-inline) * 1px),
    calc(15px + (var(--padding-inline) - 15) * var(--responsive-rate)),
    calc(var(--padding-inline) * 1px)
  );
  padding-right: clamp(
    calc(var(--padding-inline) * 1px),
    calc(15px + (var(--padding-inline) - 15) * var(--responsive-rate)),
    calc(var(--padding-inline) * 1px)
  );
}
:where(.y-min-value) {
  padding-top: calc(var(--padding-block) * 1px);
  padding-bottom: calc(var(--padding-block) * 1px);
}
:where(.py-custom:not(.y-min-value)) {
  padding-top: clamp(
    (var(--padding-block) * 0.64px),
    calc(
      (var(--padding-block) * 0.64px) +
        (var(--padding-block) - (var(--padding-block) * 0.64)) *
        var(--responsive-rate)
    ),
    calc(var(--padding-block) * 1px)
  );
  padding-bottom: clamp(
    (var(--padding-block) * 0.64px),
    calc(
      (var(--padding-block) * 0.64px) +
        (var(--padding-block) - (var(--padding-block) * 0.64)) *
        var(--responsive-rate)
    ),
    calc(var(--padding-block) * 1px)
  );
}
.h-custom {
  min-height: var(--height);
}
@media (min-width: 992px) {
  .h-custom {
    min-height: var(--height-desktop);
  }
  .w-as-max {
    width: var(--max-width);
  }
}
.content-box::before {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  background-color: var(--content-bg);
  opacity: var(--opacity);
  pointer-events: none;
  border-radius: var(--rounded-radius, 0);
}
.content-box > * {
  position: relative;
}
.sec__video video,
.video-foreground iframe,
.video-foreground {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  pointer-events: none;
  object-fit: cover;
}
@media screen and (max-width: 576.98px) {
  multi-content.swiper.row-gap-30 {
    row-gap: 0;
  }
}
multi-content.rounded .remove-rounded {
  border-radius: 0;
}
video-section.h-full video {
  height: 100%;
  object-fit: cover;
  object-position: center;
}
.sec__multi-content video-section.h-full video{
  width: 100%;
}
.text-below-video[style*="--aspect-ratio"]:before {
  display: none;
}
.product-banner__image {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.5s ease, visibility 0.5s ease;
}
.product-banner__image.active {
  opacity: 1;
  visibility: visible;
}
/* Banner packery */
.section-slide-with-banner .btn-link,
.banner-packery .btn-link{
  padding: 0 0 3px;
}
.banner-packery{
  display: grid;
}

.banner-packery .section__block-inner{
  margin: calc(var(--col-gap) / 2);
  background: none;
}
@media screen and (max-width: 576.98px) {
  .banner-packery .section__block-inner{
    margin: calc(var(--col-gap) / 2) 0;
  }
}
@media (min-width: 1025px) {
  .banner-packery{
    grid-template-columns: repeat(4, 1fr);
    margin-right: calc(-1* var(--col-gap) / 2);
    margin-left: calc(-1* var(--col-gap) / 2);

  }
  .banner-packery .section__block-inner:nth-child(3){
    grid-column-start: 1;
    grid-column-end: 3;
    grid-row-start: 2;
    grid-row-end: 3;
  }
  .banner-packery .section__block-inner:nth-child(4){
    grid-column-start: 3;
    grid-column-end: 8;
    grid-row-start: 1;
    grid-row-end: 4;
  }
}
@media screen and (min-width:576px) and (max-width:1024.993px) {
  .banner-packery{
    grid-template-columns: repeat(2, 1fr);
  }
  .banner-packery .section__block-inner:nth-child(3){
    grid-column-start: 1;
    grid-column-end: 3;
    grid-row-start: 2;
    grid-row-end: 3;
  }
  .banner-packery .section__block-inner:nth-child(4){
    grid-column-start: 1;
    grid-column-end: 3;
  }
}
.product-list__banner{
  display: flex !important;
}

.product-carousel-banner {
  --color-heading: #111;
  max-width: 340px;
  width: 100%;
  background-color: var(--color-white);
  border-radius: var(--rounded-radius, 0);
  overflow: hidden;
}
.product-carousel-banner .product-item__information{
  margin: 0;
  padding: 2rem;
}
.sec__content:has(.product-banner){
  display: flex;
  flex-direction: column;
  justify-content: center;
}
@media (min-width: 992px) {
  .product-carousel-banner .product__media{
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
  }
}