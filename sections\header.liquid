{%- liquid
  assign section_st = section.settings
  assign theme_st = settings
  assign section_width = section_st.section_width
  assign transparent = section_st.transparent_header
  assign transparent_mobile = section_st.transparent_mobile
  assign transparent_color = section_st.transparent_text_icon_color
  assign sticky_header_type = section_st.sticky_header_type
  assign sticky_mobile = section_st.sticky_header_mobile
  assign color_scheme = section_st.color_scheme
  assign logo_position = section_st.logo_position
  assign top_search = section_st.show_search
  assign my_account = section_st.show_account_icon
  assign recently_viewed = section_st.show_recently_viewed
  assign wishlist = section_st.show_wishlist_icon
  assign minicart = section_st.show_shopping_cart
  assign show_country = section_st.show_currency
  assign show_language = section_st.show_language
  assign transparent_class = ''
  assign transparent_mobile_class = ''
  if request.page_type == 'index' and transparent
    assign transparent_class = ' transparent absolute-1025 inset-x-0 z-15'
    assign transparent_mobile_class = ' transparent-mobile absolute'
  endif
-%}
{%- capture style -%}
--section-pt: {{ section_st.padding_top }}; 
--section-pb: {{ section_st.padding_bottom }};
{%- endcapture -%}
{% if settings.search_type == 'default' %}
  {%- style -%}
    @media screen and (min-width: 768px) {
      header-inner.show-search-form.logo-left {
        grid-template-columns: auto auto 1fr;
      }
    }
  {%- endstyle -%}
{% endif %}
{% if transparent %}
  {%- style -%}
    .section-header:not(.shopify-section-header-sticky) .transparent,
    .section-header:not(.shopify-section-header-sticky) .transparent-mobile {
      background-color: transparent;
      --input-bg: transparent;
    }
    .section-header:not(.shopify-section-header-sticky) .transparent-mobile .header-color,
    .section-header:not(.shopify-section-header-sticky) .transparent-mobile header-inner .button-localization {
      color: var(--transparent-color);
    }
    @media screen and (min-width: 1025px) {
      .section-header:not(.shopify-section-header-sticky) .transparent .header-color,
      .section-header:not(.shopify-section-header-sticky) .transparent header-inner .button-localization {
        color: var(--transparent-color);
      }
      .section-header:not(.shopify-section-header-sticky) .transparent .level0 > menu-item > a {
        --color-heading: var(--transparent-color);
      }
      body:has(header.transparent) .h-full_screen {
        --height-header: 0px;
      }
    }
    @media screen and (max-width: 1024.98px) {
      .transparent:not(.transparent-mobile) .header__transparent-logo {
        display: none;
      }
      .transparent:not(.transparent-mobile) .header__normal-logo {
        display: block;
      }
    }
  {%- endstyle -%}
{% endif %}
<header
  class="header relative section{% if section_st.padding_top < 30 %} pt-min{% endif %}{% if section_st.padding_bottom < 30 %} pb-min{% endif %} gradient remove_spacing color-{{ color_scheme }}{{ transparent_class }}{% if transparent_mobile %}{{ transparent_mobile_class }}{% endif %}{% if request.page_type == 'index' and section_st.separator_line == 'homepage' %} border-bottom{% endif %}{% if section_st.separator_line == 'all-page' %} border-bottom{% endif %}{% if request.page_type != 'index' and section_st.separator_line == 'inner_page' %} border-bottom{% endif %} popup-search-mobile"
  data-section-id="{{ section.id }}"
  data-sticky="{{ sticky_header_type }}"
  data-sticky-mobile="{{ sticky_mobile }}"
  style="{{ style | strip | strip_newlines }}{% if transparent %}{% endif %}--transparent-color: {{ transparent_color }};"
>
  <div class="header__inner header__layout-1" id="header-sticky">
    <div class="{{ section_width }}">
      <header-inner class="logo-position grid logo-{{ logo_position }} gap-10 gap-xl-50 header-middle align-center">
        <div class="header__logo text-center text-left-1025 inline-flex align-center">
          {%- render 'logo', isTransparent: transparent -%}
        </div>
        <div class="header__menu {{  show_language }}">
          {%- render 'toggle-menu' -%}
          {%- liquid
            render 'horizontal-menu', class: 'min-h-1025-50', show_language: show_language
          -%}
        </div>
        <div class="header__action flex flex-end align-center">
          <svg hidden>
            <symbol id="icon-wishlist-header">
              <path d="M17.4693 1.46278C16.4831 0.52335 15.178 0 13.8216 0C12.4652 0 11.1601 0.52335 10.1739 1.46278L9.5 2.11096L8.8261 1.46278C7.84238 0.51698 6.53343 -0.00720742 5.17448 0.000442952C3.81807 -0.00598466 2.512 0.518175 1.53069 1.46278C1.04762 1.9183 0.662488 2.46909 0.399178 3.08097C0.135869 3.69285 0 4.35278 0 5.01983C0 5.68687 0.135869 6.3468 0.399178 6.95868C0.662488 7.57057 1.04762 8.12135 1.53069 8.57687L8.96715 15.7858C9.04006 15.8502 9.12189 15.9034 9.21006 15.9439C9.39624 16.0187 9.60376 16.0187 9.78994 15.9439C9.87811 15.9034 9.95994 15.8502 10.0329 15.7858L17.4693 8.57687C17.9524 8.12135 18.3375 7.57057 18.6008 6.95868C18.8641 6.3468 19 5.68687 19 5.01983C19 4.35278 18.8641 3.69285 18.6008 3.08097C18.3375 2.46909 17.9524 1.9183 17.4693 1.46278ZM17.4693 5.00402C17.4715 5.47163 17.3796 5.93483 17.1991 6.36555C17.0186 6.79627 16.7533 7.18553 16.4193 7.50976L9.5 14.2286L2.58856 7.50976C2.24936 7.18822 1.97903 6.80001 1.79425 6.36906C1.60946 5.93811 1.51413 5.47355 1.51413 5.00402C1.51413 4.53448 1.60946 4.06992 1.79425 3.63897C1.97903 3.20802 2.24936 2.81981 2.58856 2.49828C3.28454 1.82749 4.21191 1.45613 5.17448 1.46278C6.14183 1.45534 7.07438 1.82652 7.77606 2.49828L8.96715 3.66025C9.04006 3.72459 9.12189 3.77785 9.21006 3.81834C9.39624 3.89313 9.60376 3.89313 9.78994 3.81834C9.87811 3.77785 9.95994 3.72459 10.0329 3.66025L11.2318 2.49828C11.9277 1.82749 12.8551 1.45613 13.8177 1.46278C14.785 1.45534 15.7176 1.82652 16.4193 2.49828C16.7562 2.82115 17.0244 3.20981 17.2076 3.64059C17.3908 4.07137 17.4852 4.53526 17.485 5.00402H17.4693Z" fill="currentColor"/>
            </symbol>
            <symbol id="icon-recently-view">
              <path d="M16.99 8.495C16.99 7.91538 16.9357 7.34784 16.827 6.79238C16.7062 6.23691 16.5372 5.7056 16.3198 5.19843C16.1145 4.69127 15.8549 4.20826 15.541 3.74939C15.2391 3.29053 14.8949 2.86789 14.5085 2.48148C14.1221 2.10715 13.6995 1.763 13.2406 1.44904C12.7938 1.14716 12.3168 0.887537 11.8097 0.670181C11.3025 0.452825 10.7712 0.289808 10.2157 0.18113C9.66027 0.0603767 9.08669 0 8.495 0C7.90331 0 7.32973 0.0603767 6.77426 0.18113C6.2188 0.289808 5.68748 0.452825 5.18032 0.670181C4.67316 0.887537 4.19618 1.14716 3.74939 1.44904C3.29053 1.763 2.86789 2.10715 2.48148 2.48148C2.09507 2.86789 1.75092 3.29053 1.44904 3.74939C1.13508 4.20826 0.869424 4.69127 0.652068 5.19843C0.446787 5.7056 0.28377 6.23691 0.163017 6.79238C0.054339 7.34784 0 7.91538 0 8.495C0 9.08669 0.054339 9.66027 0.163017 10.2157C0.28377 10.7712 0.446787 11.3025 0.652068 11.8097C0.869424 12.3168 1.13508 12.7999 1.44904 13.2587C1.75092 13.7055 2.09507 14.1221 2.48148 14.5085C2.86789 14.8949 3.29053 15.2391 3.74939 15.541C4.19618 15.8549 4.67316 16.1206 5.18032 16.3379C5.68748 16.5553 6.2188 16.7183 6.77426 16.827C7.32973 16.9477 7.90331 17.0081 8.495 17.0081C9.08669 17.0081 9.66027 16.9477 10.2157 16.827C10.7712 16.7183 11.3025 16.5553 11.8097 16.3379C12.3168 16.1206 12.7938 15.8549 13.2406 15.541C13.6995 15.2391 14.1221 14.8949 14.5085 14.5085C14.8949 14.1221 15.2391 13.7055 15.541 13.2587C15.8549 12.7999 16.1145 12.3168 16.3198 11.8097C16.5372 11.3025 16.7062 10.7712 16.827 10.2157C16.9357 9.66027 16.99 9.08669 16.99 8.495ZM15.4504 8.495C15.4504 9.46103 15.2693 10.3667 14.907 11.212C14.5447 12.0572 14.0497 12.7938 13.4217 13.4217C12.7817 14.0497 12.0391 14.5447 11.1938 14.907C10.3606 15.2693 9.46103 15.4504 8.495 15.4504C7.52897 15.4504 6.62332 15.2693 5.77805 14.907C4.94485 14.5447 4.20826 14.0497 3.56826 13.4217C2.94034 12.7938 2.44526 12.0572 2.083 11.212C1.72074 10.3667 1.53961 9.46103 1.53961 8.495C1.53961 7.54105 1.72074 6.64144 2.083 5.79616C2.44526 4.95089 2.94034 4.21429 3.56826 3.58638C4.20826 2.95846 4.94485 2.46337 5.77805 2.10111C6.62332 1.72677 7.52897 1.53961 8.495 1.53961C9.46103 1.53961 10.3606 1.72677 11.1938 2.10111C12.0391 2.46337 12.7817 2.95846 13.4217 3.58638C14.0497 4.21429 14.5447 4.95089 14.907 5.79616C15.2693 6.64144 15.4504 7.54105 15.4504 8.495ZM7.71614 3.85807V8.495C7.71614 8.65198 7.75237 8.79085 7.82482 8.9116C7.90935 9.03235 8.01802 9.12292 8.15085 9.1833L11.2482 10.741C11.4293 10.8376 11.6225 10.8557 11.8278 10.7954C12.0331 10.7229 12.184 10.5901 12.2806 10.3969C12.3772 10.2037 12.3893 10.0044 12.3168 9.79914C12.2565 9.59386 12.1297 9.44291 11.9365 9.34631L9.27386 8.02406V3.85807C9.27386 3.65279 9.19537 3.4777 9.03839 3.33279C8.89349 3.17581 8.71236 3.09732 8.495 3.09732C8.27764 3.09732 8.09048 3.17581 7.9335 3.33279C7.78859 3.4777 7.71614 3.65279 7.71614 3.85807Z"/>
            </symbol>
            <symbol id="icon-search">
             <path fill="currentColor" d="M11.2 11.04c-.094 0-.094 0 0 0l-.094.095c-.47.467-1.035.842-1.788 1.122-.66.281-1.412.375-2.165.375-.753 0-1.506-.188-2.165-.468A5.52 5.52 0 0 1 3.2 10.947 5.993 5.993 0 0 1 1.976 9.17C1.694 8.608 1.6 7.953 1.6 7.205c0-.749.188-1.497.47-2.152.283-.749.66-1.31 1.13-1.872.565-.468 1.13-.842 1.788-1.123.66-.28 1.412-.467 2.165-.467.753 0 1.506.187 2.165.467.658.281 1.317.655 1.788 1.217.47.468.847 1.123 1.223 1.778.283.655.471 1.31.471 2.152 0 .748-.188 1.497-.47 2.152a8.78 8.78 0 0 1-1.13 1.684Zm4.518 3.463-2.918-2.9c.47-.656.847-1.31 1.13-2.06.282-.748.376-1.59.376-2.432 0-1.03-.188-1.965-.565-2.807-.376-.842-.847-1.59-1.506-2.246-.659-.654-1.412-1.122-2.259-1.497C9.13.187 8.188 0 7.153 0 6.212 0 5.27.187 4.329.561c-.847.375-1.6.843-2.258 1.497-.66.655-1.13 1.404-1.506 2.34C.188 5.24 0 6.175 0 7.205c0 .935.188 1.871.565 2.807.376.842.94 1.59 1.6 2.245.659.655 1.411 1.123 2.259 1.591.847.28 1.788.468 2.729.468.847 0 1.694-.094 2.447-.375.753-.28 1.506-.655 2.07-1.122l2.918 2.9c.189.187.377.281.565.281.188 0 .376-.094.565-.28.188-.188.282-.375.282-.562 0-.28-.094-.468-.282-.655Z"/>
            </symbol>
            <symbol id="icon-account">
            <path fill="currentColor" d="M15.899 14.84a9.143 9.143 0 0 0-3.35-3.292 9.203 9.203 0 0 0-9.101 0 9.143 9.143 0 0 0-3.35 3.293.773.773 0 0 0 .067.855c.064.08.145.146.238.193a.784.784 0 0 0 .39.101.737.737 0 0 0 .671-.373 7.57 7.57 0 0 1 2.774-2.727 7.619 7.619 0 0 1 7.537 0 7.57 7.57 0 0 1 2.773 2.727.78.78 0 0 0 1.062.28.716.716 0 0 0 .36-.458.772.772 0 0 0-.071-.598ZM8 9.6a4.777 4.777 0 0 0 3.363-1.387A4.866 4.866 0 0 0 12.8 4.844a4.867 4.867 0 0 0-1.406-3.425A4.778 4.778 0 0 0 8 0C6.727 0 5.506.51 4.606 1.419A4.867 4.867 0 0 0 3.2 4.844a4.866 4.866 0 0 0 1.436 3.369A4.777 4.777 0 0 0 8 9.6ZM4.8 4.844c0-.856.337-1.678.937-2.283A3.185 3.185 0 0 1 8 1.615c.848 0 1.662.34 2.262.946a3.245 3.245 0 0 1 0 4.567c-.6.606-1.414.946-2.262.946-.849 0-1.663-.34-2.263-.946A3.245 3.245 0 0 1 4.8 4.844Z"/>
              </symbol>
              <symbol id="icon-bag">
                <path d="M12.7518 2.91374L11.6276 1.44835H3.37237L2.24824 2.91374H12.7518ZM14.8419 3.18637V3.20341C14.8419 3.20341 14.8478 3.20341 14.8595 3.20341C14.8946 3.27157 14.9239 3.33972 14.9473 3.40788C14.9824 3.47604 15 3.54988 15 3.62939V13.819C15 14.1143 14.9356 14.3983 14.8068 14.6709C14.6897 14.9322 14.5316 15.1594 14.3326 15.3525C14.1335 15.557 13.8934 15.716 13.6124 15.8296C13.3431 15.9432 13.0562 16 12.7518 16H2.24824C1.94379 16 1.65105 15.9375 1.37002 15.8126C1.1007 15.699 0.866511 15.5456 0.667447 15.3525C0.456674 15.1594 0.29274 14.9322 0.175644 14.6709C0.058548 14.3983 0 14.1143 0 13.819V3.62939C0 3.58395 0.0058548 3.53852 0.0175644 3.49308C0.029274 3.43628 0.0468384 3.37948 0.0702576 3.32268C0.0819672 3.29996 0.0936768 3.27725 0.105386 3.25453C0.128806 3.23181 0.14637 3.20909 0.15808 3.18637L2.40632 0.28967C2.47658 0.198793 2.5644 0.130635 2.66979 0.085197C2.77518 0.028399 2.88642 0 3.00351 0H11.9965C12.1136 0 12.2248 0.028399 12.3302 0.085197C12.4356 0.130635 12.5234 0.198793 12.5937 0.28967L14.8419 3.18637ZM1.51054 4.36209V13.819C1.51054 13.9098 1.5281 14.0007 1.56323 14.0916C1.59836 14.1825 1.65105 14.262 1.72131 14.3301C1.79157 14.3983 1.87354 14.4494 1.96721 14.4835C2.06089 14.5176 2.15457 14.5346 2.24824 14.5346H12.7518C12.8571 14.5346 12.9508 14.5176 13.0328 14.4835C13.1265 14.4494 13.2084 14.3983 13.2787 14.3301C13.3489 14.262 13.4016 14.1825 13.4368 14.0916C13.4719 14.0007 13.4895 13.9098 13.4895 13.819V4.36209H1.51054ZM9.74824 6.54313C9.74824 6.33866 9.8185 6.16826 9.95902 6.03195C10.1112 5.88427 10.2927 5.81044 10.5035 5.81044C10.7026 5.81044 10.8724 5.88427 11.0129 6.03195C11.1651 6.16826 11.2412 6.33866 11.2412 6.54313C11.2412 7.04295 11.1417 7.51438 10.9426 7.9574C10.7553 8.40043 10.4918 8.78665 10.1522 9.11608C9.81265 9.44551 9.41452 9.70678 8.95785 9.89989C8.50117 10.0816 8.01522 10.1725 7.5 10.1725C6.98478 10.1725 6.49883 10.0816 6.04215 9.89989C5.58548 9.70678 5.18735 9.44551 4.84777 9.11608C4.5082 8.78665 4.23888 8.40043 4.03981 7.9574C3.85246 7.51438 3.75878 7.04295 3.75878 6.54313C3.75878 6.33866 3.82904 6.16826 3.96956 6.03195C4.12178 5.88427 4.29742 5.81044 4.49649 5.81044C4.70726 5.81044 4.8829 5.88427 5.02342 6.03195C5.17564 6.16826 5.25176 6.33866 5.25176 6.54313C5.25176 6.84984 5.3103 7.13383 5.4274 7.3951C5.5445 7.65637 5.70843 7.88356 5.9192 8.07668C6.11827 8.28115 6.35246 8.44018 6.62178 8.55378C6.8911 8.66738 7.18384 8.72417 7.5 8.72417C7.81616 8.72417 8.1089 8.66738 8.37822 8.55378C8.64754 8.44018 8.88173 8.28115 9.0808 8.07668C9.29157 7.88356 9.4555 7.65637 9.5726 7.3951C9.6897 7.13383 9.74824 6.84984 9.74824 6.54313Z" fill="currentColor"/>
                  </symbol>
          </svg>
          {% if show_language or show_country %}
            <div class="lang__currency-desktop me-20 hidden flex-1025 gap-15">
              {% if show_language %}
                {% render 'language-switcher' %}
              {% endif %}
              {% if show_country %}
                {% render 'country-switcher' %}
              {% endif %}
            </div>
          {% endif %}
          {%- liquid
            if top_search
              render 'top-search-header'
            endif
            if my_account
              render 'my-account'
            endif
            if recently_viewed
              render 'recently-viewed'
            endif
            if wishlist
              render 'wishlist'
            endif
            if minicart and theme_st.enable_catalog_mode == false
              render 'minicart_header'
            endif
          -%}
        </div>
      </header-inner>
    </div>
  </div>
</header>
{% schema %}
{
  "name": "t:sections.header.name",
  "limit": 1,
  "class": "section-header",
  "enabled_on": {
    "groups": ["header"]
  },
  "settings": [
    {
      "type": "select",
      "id": "section_width",
      "options": [
        {
          "value": "container",
          "label": "t:sections.all.section_width.container.label"
        },
        {
          "value": "fluid_container",
          "label": "t:sections.all.section_width.fluid_container.label"
        },
        {
          "value": "stretch_width",
          "label": "t:sections.all.section_width.stretch_width.label"
        },
        {
          "value": "full_width",
          "label": "t:sections.all.section_width.full_width.label"
        },
        {
          "value": "header_custom_width",
          "label": "Header Custom Width (1800px)"
        }
      ],
      "default": "header_custom_width",
      "label": "t:sections.all.section_width.label"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.color_scheme.label",
      "default": "default-color-scheme"
    },
    {
      "type": "select",
      "id": "logo_position",
      "options": [
        {
          "value": "left",
          "label": "t:sections.header.settings.logo_position.options__1.label"
        },
        {
          "value": "center",
          "label": "t:sections.header.settings.logo_position.options__2.label"
        }
      ],
      "default": "left",
      "info": "t:sections.header.settings.logo_position.info",
      "label": "t:sections.header.settings.logo_position.label"
    },
    {
      "type": "select",
      "id": "separator_line",
      "label": "t:sections.footer.settings.separator_line.label",
      "default": "unset",
      "options": [
        {
          "value": "unset",
          "label": "t:sections.footer.settings.separator_line.unset.label"
        },
        {
          "value": "homepage",
          "label": "t:sections.footer.settings.separator_line.show_on_homepage.label"
        },
        {
          "value": "inner_page",
          "label": "t:sections.footer.settings.separator_line.show_inner_page.label"
        },
        {
          "value": "all-page",
          "label": "t:sections.footer.settings.separator_line.show_all_page.label"
        }
      ]
    },
    {
      "type": "checkbox",
      "id": "enable_login_popup",
      "default": true,
      "label": "t:sections.all.section_header.login_popup.label"
    },
    {
      "type": "select",
      "id": "sticky_header_type",
      "options": [
        {
          "value": "none",
          "label": "t:sections.header.settings.sticky_header_type.options__1.label"
        },
        {
          "value": "on-scroll-up",
          "label": "t:sections.header.settings.sticky_header_type.options__2.label"
        },
        {
          "value": "always",
          "label": "t:sections.header.settings.sticky_header_type.options__3.label"
        }
      ],
      "default": "on-scroll-up",
      "label": "t:sections.header.settings.sticky_header_type.label"
    },
    {
      "type": "checkbox",
      "id": "sticky_header_mobile",
      "default": true,
      "label": "t:sections.header.settings.sticky_header_mobile.label"
    },
    {
      "type": "header",
      "content": "t:sections.header.settings.transparent_header.label",
      "info": "t:sections.header.settings.transparent_header.info"
    },
    {
      "type": "checkbox",
      "id": "transparent_header",
      "label": "t:sections.header.settings.transparent_header.label"
    },
    {
      "type": "checkbox",
      "id": "transparent_mobile",
      "label": "t:sections.header.settings.transparent_header.enable_mobile.label"
    },
    {
      "type": "color",
      "id": "transparent_text_icon_color",
      "label": "t:sections.header.settings.transparent_header.text_icon_color.label",
      "default": "#ffffff"
    },
    {
      "type": "header",
      "content": "t:sections.header.settings.mega_menu.label"
    },
    {
      "type": "link_list",
      "id": "mega_menu",
      "default": "main-menu",
      "label": "t:sections.header.settings.mega_menu.label"
    },
    {
      "type": "select",
      "id": "dropdowns_animation",
      "options": [
        {
          "value": "fade-in",
          "label": "t:sections.header.settings.mega_menu.dropdowns_animation.options__1.label"
        },
        {
          "value": "fade-in-down",
          "label": "t:sections.header.settings.mega_menu.dropdowns_animation.options__2.label"
        },
        {
          "value": "down-to-up",
          "label": "t:sections.header.settings.mega_menu.dropdowns_animation.options__3.label"
        }
      ],
      "default": "down-to-up",
      "label": "t:sections.header.settings.mega_menu.dropdowns_animation.label"
    },
    {
      "type": "checkbox",
      "id": "uppercase_first",
      "label": "t:sections.header.settings.mega_menu.uppercase_first.label"
    },
    {
      "type": "header",
      "content": "t:sections.header.settings.menu_label.label"
    },
    {
      "type": "paragraph",
      "content": "t:sections.header.settings.menu_label.label1.content"
    },
    {
      "type": "text",
      "id": "hot",
      "label": "t:sections.header.settings.menu_label.label1.text",
      "default": "Hot"
    },
    {
      "type": "color",
      "id": "label_color_hot",
      "label": "t:sections.header.settings.menu_label.label1.color",
      "default": "#fff"
    },
    {
      "type": "color",
      "id": "label_background_hot",
      "label": "t:sections.header.settings.menu_label.label1.background",
      "default": "#D0473E"
    },
    {
      "type": "paragraph",
      "content": "t:sections.header.settings.menu_label.label2.content"
    },
    {
      "type": "text",
      "id": "new",
      "label": "t:sections.header.settings.menu_label.label2.text",
      "default": "New"
    },
    {
      "type": "color",
      "id": "label_color_new",
      "label": "t:sections.header.settings.menu_label.label2.color",
      "default": "#fff"
    },
    {
      "type": "color",
      "id": "label_background_new",
      "label": "t:sections.header.settings.menu_label.label2.background",
      "default": "#516cf4"
    },
    {
      "type": "paragraph",
      "content": "t:sections.header.settings.menu_label.label3.content"
    },
    {
      "type": "text",
      "id": "sale",
      "label": "t:sections.header.settings.menu_label.label3.text",
      "default": "Sale"
    },
    {
      "type": "color",
      "id": "label_color_sale",
      "label": "t:sections.header.settings.menu_label.label3.color",
      "default": "#fff"
    },
    {
      "type": "color",
      "id": "label_background_sale",
      "label": "t:sections.header.settings.menu_label.label3.background",
      "default": "#D0473E"
    },
    {
      "type": "paragraph",
      "content": "t:sections.header.settings.menu_label.label4.content"
    },
    {
      "type": "text",
      "id": "popular",
      "label": "t:sections.header.settings.menu_label.label4.text",
      "default": "Popular"
    },
    {
      "type": "color",
      "id": "label_color_popular",
      "label": "t:sections.header.settings.menu_label.label4.color",
      "default": "#fff"
    },
    {
      "type": "color",
      "id": "label_background_popular",
      "label": "t:sections.header.settings.menu_label.label4.background",
      "default": "#14854E"
    },
    {
      "type": "header",
      "content": "t:sections.header.settings.menu_mobile.label"
    },
    {
      "type": "checkbox",
      "id": "redirect_to_link",
      "label": "t:sections.header.settings.menu_mobile.redirect_to_link.label",
      "default": true
    },
    {
      "type": "select",
      "id": "menu_tab",
      "options": [
        {
          "value": "unset",
          "label": "t:sections.header.settings.menu_mobile.menu_tab.options__1.label"
        },
        {
          "value": "custom-collections",
          "label": "t:sections.header.settings.menu_mobile.menu_tab.options__3.label"
        }
      ],
      "default": "unset",
      "label": "t:sections.header.settings.menu_mobile.menu_tab.label"
    },
    {
      "type": "collection_list",
      "id": "collection_list",
      "label": "t:sections.header.settings.menu_mobile.collection_list.label"
    },
    {
      "type": "header",
      "content": "t:sections.header.settings.menu_mobile.title"
    },
    {
      "type": "color",
      "id": "menu_mobile_color",
      "label": "t:sections.header.settings.menu_mobile.color_title",
      "default": "#fff"
    },
    {
      "type": "color",
      "id": "menu_mobile_background",
      "label": "t:sections.header.settings.menu_mobile.background_title",
      "default": "#D0473E"
    },
    {
      "type": "header",
      "content": "t:sections.header.settings.addons.label"
    },
    {
      "type": "checkbox",
      "id": "show_search",
      "label": "t:sections.header.settings.addons.show_search_icon.label",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_account_icon",
      "label": "t:sections.header.settings.addons.show_account_icon.label",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_wishlist_icon",
      "label": "t:sections.header.settings.addons.show_wishlist_icon.label",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_shopping_cart",
      "label": "t:sections.header.settings.addons.show_shopping_cart.label",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_currency",
      "label": "t:sections.header.settings.addons.show_currency.label",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_language",
      "label": "t:sections.header.settings.addons.show_language.label",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_recently_viewed",
      "label": "t:sections.header.settings.addons.show_recently_viewed.label",
      "default": true
    },
    {
      "type": "header",
      "content": "t:sections.all.section_padding.label"
    },
    {
      "type": "range",
      "id": "padding_top",
      "label": "t:sections.all.section_padding.top.label",
      "default": 10,
      "min": 0,
      "max": 20,
      "step": 1,
      "unit": "px"
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "label": "t:sections.all.section_padding.bottom.label",
      "default": 10,
      "min": 0,
      "max": 20,
      "step": 1,
      "unit": "px"
    }
  ],
  "blocks": [
    {
      "type": "menu_promotion",
      "name": "t:sections.header.settings.blocks.promotion.name",
      "settings": [
        {
          "type": "text",
          "id": "text",
          "label": "t:sections.header.settings.blocks.menu_item.label",
          "info": "t:sections.header.settings.blocks.menu_item.info"
        },
        {
          "type": "checkbox",
          "id": "open-link-newtab",
          "label": "t:sections.header.settings.blocks.menu_item.open_link_newtab.label",
          "default": true
        },
        {
          "type": "select",
          "id": "item_label",
          "options": [
            {
              "value": "unset",
              "label": "t:sections.header.settings.blocks.menu_item.item_label.unset"
            },
            {
              "value": "hot",
              "label": "t:sections.header.settings.blocks.menu_item.item_label.hot"
            },
            {
              "value": "new",
              "label": "t:sections.header.settings.blocks.menu_item.item_label.new"
            },
            {
              "value": "sale",
              "label": "t:sections.header.settings.blocks.menu_item.item_label.sale"
            },
            {
              "value": "popular",
              "label": "t:sections.header.settings.blocks.menu_item.item_label.popular"
            }
          ],
          "default": "unset",
          "label": "t:sections.header.settings.blocks.menu_item.item_label.label"
        },
        {
          "type": "header",
          "content": "t:sections.header.settings.blocks.menu_item.submenu.label"
        },
        {
          "type": "range",
          "id": "promotion_menu_column",
          "min": 2,
          "max": 5,
          "step": 1,
          "label": "t:sections.header.settings.blocks.menu_column.label",
          "default": 4
        },
        {
          "type": "range",
          "id": "mega_custom_width",
          "min": 600,
          "max": 1400,
          "unit": "px",
          "step": 10,
          "label": "t:sections.header.settings.blocks.mega_custom_width.label",
          "default": 1030
        },
        {
          "type": "checkbox",
          "id": "center_submenu",
          "label": "t:sections.header.settings.blocks.menu_item.center_submenu",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "full_width",
          "label": "t:sections.header.settings.blocks.menu_item.full_width.label",
          "info": "t:sections.header.settings.blocks.menu_item.full_width.info",
          "default": true
        },
        {
          "type": "header",
          "content": "t:sections.header.settings.blocks.promotion.label"
        },
        {
          "type": "select",
          "id": "promotion_position",
          "options": [
            {
              "value": "left",
              "label": "t:sections.header.settings.blocks.promotion.position.left"
            },
            {
              "value": "right",
              "label": "t:sections.header.settings.blocks.promotion.position.right"
            }
          ],
          "default": "right",
          "label": "t:sections.header.settings.blocks.promotion.position.label"
        },
        {
          "type": "range",
          "id": "promotion_image_width",
          "min": 0,
          "max": 50,
          "step": 1,
          "label": "t:sections.header.settings.blocks.promotion.upload_image_width",
          "default": 27,
          "unit": "%"
        },
        {
          "type": "header",
          "content": "t:sections.header.settings.blocks.promotion.promotion_1.label"
        },
        {
          "type": "image_picker",
          "id": "promotion_image_1",
          "label": "t:sections.header.settings.blocks.promotion.upload_image"
        },
        {
          "type": "url",
          "id": "promotion_link_1",
          "label": "t:sections.header.settings.blocks.promotion.promotion_1.link"
        },
        {
          "type": "checkbox",
          "id": "promotion_link_newtab_1",
          "label": "t:sections.header.settings.blocks.menu_item.open_link_newtab.label",
          "default": true
        },
        {
          "type": "header",
          "content": "t:sections.header.settings.blocks.promotion.promotion_2.label"
        },
        {
          "type": "image_picker",
          "id": "promotion_image_2",
          "label": "t:sections.header.settings.blocks.promotion.upload_image"
        },
        {
          "type": "url",
          "id": "promotion_link_2",
          "label": "t:sections.header.settings.blocks.promotion.promotion_2.link"
        },
        {
          "type": "checkbox",
          "id": "promotion_link_newtab_2",
          "label": "t:sections.header.settings.blocks.menu_item.open_link_newtab.label",
          "default": true
        }
      ]
    },
    {
      "type": "menu_product",
      "name": "t:sections.header.settings.blocks.product.name",
      "settings": [
        {
          "type": "text",
          "id": "text",
          "label": "t:sections.header.settings.blocks.menu_item.label",
          "info": "t:sections.header.settings.blocks.menu_item.info"
        },
        {
          "type": "checkbox",
          "id": "open-link-newtab",
          "label": "t:sections.header.settings.blocks.menu_item.open_link_newtab.label",
          "default": true
        },
        {
          "type": "select",
          "id": "item_label",
          "options": [
            {
              "value": "unset",
              "label": "t:sections.header.settings.blocks.menu_item.item_label.unset"
            },
            {
              "value": "hot",
              "label": "t:sections.header.settings.blocks.menu_item.item_label.hot"
            },
            {
              "value": "new",
              "label": "t:sections.header.settings.blocks.menu_item.item_label.new"
            },
            {
              "value": "sale",
              "label": "t:sections.header.settings.blocks.menu_item.item_label.sale"
            },
            {
              "value": "popular",
              "label": "t:sections.header.settings.blocks.menu_item.item_label.popular"
            }
          ],
          "default": "unset",
          "label": "t:sections.header.settings.blocks.menu_item.item_label.label"
        },
        {
          "type": "header",
          "content": "t:sections.header.settings.blocks.menu_item.submenu.label"
        },
        {
          "type": "range",
          "id": "product_menu_column",
          "min": 2,
          "max": 5,
          "step": 1,
          "label": "t:sections.header.settings.blocks.menu_column.label",
          "default": 4
        },
        {
          "type": "range",
          "id": "mega_custom_width",
          "min": 600,
          "max": 1400,
          "unit": "px",
          "step": 10,
          "label": "t:sections.header.settings.blocks.mega_custom_width.label",
          "default": 1030
        },
        {
          "type": "checkbox",
          "id": "center_submenu",
          "label": "t:sections.header.settings.blocks.menu_item.center_submenu",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "full_width",
          "label": "t:sections.header.settings.blocks.menu_item.full_width.label",
          "info": "t:sections.header.settings.blocks.menu_item.full_width.info",
          "default": true
        },
        {
          "type": "header",
          "content": "t:sections.header.settings.blocks.product.all.header"
        },
        {
          "type": "range",
          "id": "product_width",
          "min": 0,
          "max": 50,
          "unit": "%",
          "step": 1,
          "label": "t:sections.header.settings.blocks.product.width",
          "default": 50
        },
        {
          "type": "product",
          "id": "product_1",
          "label": "t:sections.header.settings.blocks.product.product_1"
        },
        {
          "type": "product",
          "id": "product_2",
          "label": "t:sections.header.settings.blocks.product.product_2"
        },
        {
          "type": "product",
          "id": "product_3",
          "label": "t:sections.header.settings.blocks.product.product_3"
        }
      ]
    },
    {
      "type": "menu_collection",
      "name": "t:sections.header.settings.blocks.collection.name",
      "settings": [
        {
          "type": "text",
          "id": "text",
          "label": "t:sections.header.settings.blocks.menu_item.label",
          "info": "t:sections.header.settings.blocks.menu_item.info"
        },
        {
          "type": "checkbox",
          "id": "open-link-newtab",
          "label": "t:sections.header.settings.blocks.menu_item.open_link_newtab.label",
          "default": true
        },
        {
          "type": "select",
          "id": "item_label",
          "options": [
            {
              "value": "unset",
              "label": "t:sections.header.settings.blocks.menu_item.item_label.unset"
            },
            {
              "value": "hot",
              "label": "t:sections.header.settings.blocks.menu_item.item_label.hot"
            },
            {
              "value": "new",
              "label": "t:sections.header.settings.blocks.menu_item.item_label.new"
            },
            {
              "value": "sale",
              "label": "t:sections.header.settings.blocks.menu_item.item_label.sale"
            },
            {
              "value": "popular",
              "label": "t:sections.header.settings.blocks.menu_item.item_label.popular"
            }
          ],
          "default": "unset",
          "label": "t:sections.header.settings.blocks.menu_item.item_label.label"
        },
        {
          "type": "header",
          "content": "t:sections.header.settings.blocks.menu_item.submenu.label"
        },
        {
          "type": "range",
          "id": "collection_menu_column",
          "min": 2,
          "max": 5,
          "step": 1,
          "label": "t:sections.header.settings.blocks.menu_column.label",
          "default": 4
        },
        {
          "type": "range",
          "id": "mega_custom_width",
          "min": 600,
          "max": 1400,
          "unit": "px",
          "step": 10,
          "label": "t:sections.header.settings.blocks.mega_custom_width.label",
          "default": 1030
        },
        {
          "type": "checkbox",
          "id": "center_submenu",
          "label": "t:sections.header.settings.blocks.menu_item.center_submenu",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "full_width",
          "label": "t:sections.header.settings.blocks.menu_item.full_width.label",
          "info": "t:sections.header.settings.blocks.menu_item.full_width.info",
          "default": true
        },
        {
          "type": "header",
          "content": "t:sections.header.settings.blocks.collection.all.header"
        },
        {
          "type": "range",
          "id": "collection_width",
          "min": 0,
          "max": 100,
          "unit": "%",
          "step": 1,
          "label": "t:sections.header.settings.blocks.collection.width",
          "default": 60
        },
        {
          "type": "collection",
          "id": "collection_1",
          "label": "t:sections.header.settings.blocks.collection.collection_1"
        },
        {
          "type": "collection",
          "id": "collection_2",
          "label": "t:sections.header.settings.blocks.collection.collection_2"
        },
        {
          "type": "collection",
          "id": "collection_3",
          "label": "t:sections.header.settings.blocks.collection.collection_3"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "t:sections.header.name",
      "blocks": [
        {
          "type": "menu_promotion"
        }
      ]
    }
  ]
}
{% endschema %}
