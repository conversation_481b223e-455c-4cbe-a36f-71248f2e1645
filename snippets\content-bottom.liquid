{%- liquid
  assign theme_st = settings
  assign money_format = shop.money_format
  if theme_st.currency_code_enabled
    assign money_format = shop.money_with_currency_format | strip_html
  endif
-%}
{% liquid
  assign enable_rtl = settings.rtl
  assign iso_code = request.locale.iso_code
  assign lis_language_rtl = settings.language_rtl
  if lis_language_rtl != blank
    if lis_language_rtl contains iso_code
      assign enable_rtl = true
    else
      assign enable_rtl = false
    endif
  endif
%}
<link rel="stylesheet" href="{{ 'tingle.min.css' | asset_url }}" media="print" onload="this.media='all'">
<a class="skip-to-content-link no-underline visually-hidden" href="#MainContent">
  {{ 'accessibility.skip_to_text' | t }}
</a>
<ul hidden>
  <li id="a11y-refresh-page-message">{{ 'accessibility.refresh_page' | t }}</li>
  <li id="a11y-new-window-message">{{ 'accessibility.link_messages.new_window' | t }}</li>
</ul>
{%- if theme_st.page_terms_conditions != blank and pages[theme_st.page_terms_conditions].content != blank -%}
  <div
    id="popup-terms-conditions"
    class="hidden"
    data-text="{{ 'general.cart.terms_conditions_text' | t }}"
  >
    <div class="terms-conditions-content popup-content">
      {{ pages[theme_st.page_terms_conditions].content }}
    </div>
  </div>
{% elsif theme_st.page_terms_conditions != blank and pages['term-and-conditions'].content != null %}
  <div
    id="popup-terms-conditions"
    class="hidden"
    data-text="{{ 'general.cart.terms_conditions_text' | t }}"
  >
    <div class="terms-conditions-content popup-content">
      {{ pages['term-and-conditions'].content }}
    </div>
  </div>
{%- endif -%}
{%- if theme_st.enable_cookies != blank -%}
  <cookie-bar
    id="cookie"
    class="maxcookie hidden text-center gradient fixed shadow p-30 z-10 rounded-15 bottom-30{% if enable_rtl %} left-30{% else %} right-30{% endif %}"
  >
    {% if theme_st.heading_cookies != blank or theme_st.content_cookies != blank %}
      <div class="cookie-content">
        {% if theme_st.heading_cookies != blank %}
          <div class="cookie-heading h4 mt-0">
            {{- theme_st.heading_cookies }}
          </div>
        {% endif %}
        {% if theme_st.content_cookies != blank %}
          <div class="cookie-message rich__text-m0 fs-small-1">
            {{- theme_st.content_cookies }}
          </div>
        {% endif %}
      </div>
    {% endif %}
    <div class="cookie-compliance w-full mt-20">
      {%- if theme_st.label_allow != blank -%}
        <button class="cookie-btn cookie-dismiss w-full btn-primary" id="cookie-dismiss">
          {{ theme_st.label_allow }}
        </button>
      {%- endif -%}
      {% if theme_st.label_refuse != blank %}
        <button class="cookie-btn cookie-dismiss w-full btn-outline mt-15" id="cookie-refuse">
          {{ theme_st.label_refuse }}
        </button>
      {% endif %}
    </div>
  </cookie-bar>
{%- endif -%}
<script>
  
  
  window.shopUrl = '{{ request.origin }}';
  window.routes = {
    cart_add_url: '{{ routes.cart_add_url }}',
    cart_change_url: '{{ routes.cart_change_url }}',
    cart_update_url: '{{ routes.cart_update_url }}',
    cart_url: '{{ routes.cart_url }}',
    search_url: '{{ routes.search_url }}',
    predictive_search_url: '{{ routes.predictive_search_url }}',
  };
  window.cartStrings = {
    error: `{{ 'sections.cart.cart_error' | t }}`,
    no_shipping: `{{ 'sections.cart.no_shipping' | t }}`,
    shipping_rate: `{{ 'sections.cart.shipping_rate' | t }}`,
    quantityError: `{{ 'sections.cart.cart_quantity_error_html' | t: quantity: '[quantity]' }}`,
    money_format: {{ money_format | json }},
    quick_edit: `{{ 'sections.cart.quick_edit' | t }}`,
    cart_symbol: '{{ cart.currency.symbol }}',
    discount_removed: `{{ 'general.content.discount_code_remove' | t }}`,
    discount_applied: `{{ 'general.content.discount_code_applied' | t }}`,
    discount_already: `{{ 'general.content.discount_code_already' | t }}`,
    discount_error: `{{ 'general.content.discount_code_error' | t }}`
  }
      
  window.themeGlobalVariables = {
    settings: {
      money_format: {{ shop.money_format | json }},
    }
  }
  window.variantStrings = {
    incoming_with_date: `{{ 'products.product.inventory_status.incoming_with_date' | t }}`,
    incoming: `{{ 'products.product.inventory_status.incoming' | t }}`,
    addToCart: `{{ 'products.product.add_to_cart' | t }}`,
    addToCartBundle: `{{ 'products.product.actions.add_to_bundle.label_bundle' | t }}`,
    addedToCartBundle: `{{ 'products.product.actions.add_to_bundle.label_bundle_added' | t }}`,
    soldOut: `{{ 'products.product.sold_out' | t }}`,
    unavailable: `{{ 'products.product.unavailable' | t }}`,
    outStock: `{{ 'products.product.outstock' | t }}`,
    preOrder: `{{ 'products.product.pre_order' | t }}`,
    save: `{{ 'products.product.save' | t }}`,
    inStock: `{{ 'products.product.instock' | t }}`,
    addSuccess: `{{ 'products.product.addSuccess' | t }}`,
    addGiftCard: `{{ 'products.product.addGiftCard' | t  }}`,
    removeCartItem: `{{ 'products.product.removeCartItem' | t }}`,
    unavailable_with_option: `{{ 'products.product.value_unavailable' | t: option_value: '[value]' }}`,
    sale: `{{ 'products.product.label.sale_label' | t }}`
  };

  window.flashingBrowseTab = {
    enable: {{ theme_st.enable_browser_tab_notifications }},
    firstNotification: `{{ theme_st.first_notification }}`,
    secondaryNotification: `{{ theme_st.secondary_notification }}`
  }
</script>
