.collection-packery--grid
  :is(
    .collection-item,
    .collection-item__wrapper,
    .collection-item__media,
    .collection-item__media a
  ) {
  height: 100%;
}
.grid-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}
.grid-3 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}
.grid-3 .collection-item:first-child {
  grid-area: 1/1/3/2;
}
.grid-3 .collection-item:last-child {
  grid-area: 2/2/3/3;
}
@media only screen and (max-width: 479.98px) {
  .col-mb-1.grid-4 {
    grid-template-columns: repeat(1, 100%);
  }
  .col-mb-2:is(.grid-4, .grid-5) {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  .col-mb-2:is(.grid-4, .grid-5) .collection-item:first-child {
    grid-area: 1/1/3/2;
  }
  .col-mb-2:is(.grid-4, .grid-5) .collection-item:nth-child(2) {
    grid-area: 3/1/4/2;
  }
  .col-mb-2:is(.grid-4, .grid-5) .collection-item:nth-child(3) {
    grid-area: 1/2/2/3;
  }
  .col-mb-2:is(.grid-4, .grid-5) .collection-item:nth-child(4) {
    grid-area: 2/2/4/3;
  }
  .col-mb-2.grid-5 .collection-item:nth-child(5) {
    grid-area: 4 / 1 / 5 / 3;
  }
}
@media only screen and (min-width: 480px) {
  .grid-5 {
    --gap: var(--col-gap);
    --space: calc(3 * var(--gap) / 4);
    grid-template-columns:
      calc(30% - var(--space)) calc(20% - var(--space)) calc(20% - var(--space))
      calc(30% - var(--space));
  }
}
@media only screen and (min-width: 768px) {
  .grid-4 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .grid-4 .collection-item:first-child {
    grid-area: 1/1/3/2;
  }

  .grid-4 .collection-item:nth-child(2) {
    grid-area: 1/2/2/3;
  }

  .grid-4 .collection-item:nth-child(3) {
    grid-area: 2/2/3/3;
  }

  .grid-4 .collection-item:nth-child(4) {
    grid-area: 1/3/3/4;
  }
}
@media only screen and (min-width: 480px) and (max-width: 991px) {
  .grid-5 .collection-item:first-child {
    grid-area: 1/1/3/3;
  }
  .grid-5 .collection-item:nth-child(2) {
    grid-area: 3/1/4/5;
  }
  .grid-5 .collection-item:nth-child(3) {
    grid-area: 4/1/5/3;
  }
  .grid-5 .collection-item:nth-child(4) {
    grid-area: 4/3/5/5;
  }
  .grid-5 .collection-item:last-child {
    grid-area: 1/3/3/5;
  }
}
@media only screen and (min-width: 992px) {
  .grid-5 {
    --gap: var(--col-gap-desktop);
  }
  .grid-5 .collection-item:first-child {
    grid-area: 1/1/3/2;
  }
  .grid-5 .collection-item:nth-child(2) {
    grid-area: 1/2/2/4;
  }
  .grid-5 .collection-item:last-child {
    grid-area: 1/4/3/5;
  }
  .grid-3 .collection-item:first-child {
    grid-area: 1/1/3/2;
  }
  .grid-3 .collection-item:last-child {
    grid-area: 2/2/3/3;
  }
}
