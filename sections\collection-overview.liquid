{{ 'banner.css' | asset_url | stylesheet_tag }}
{{ 'collapsible.css' | asset_url | stylesheet_tag }}

{%- liquid
  assign section_st = section.settings

  assign section_width = ''
  assign position = section_st.position
  assign color_scheme = section_st.color_scheme
  assign heading = section_st.heading
  assign description = section_st.description
  assign collection_image = ''
  assign image_ratio = section_st.image_ratio
  assign custom_ratio = section_st.custom_ratio
  assign show_image = section_st.show_image

  for block in section.blocks
    if block.settings.collection_image != blank
      assign collection_image = block.settings.collection_image
      break
    endif
  endfor
  if section_st.section_width == 'full_width overflow-hidden'
    assign reset_spacing = ' remove_spacing'
  else
    assign section_width = section_st.section_width
  endif

  assign column_gap = section_st.column_gap
  if section_st.reset_spacing
    assign reset_spacing = ' remove_spacing'
  endif

  assign scroll_animation = section_st.scroll_animation
  if section_st.header_size == 'small'
    assign header_size = 'h3'
  elsif section_st.header_size == 'large'
    assign header_size = 'h1-size'
  endif
  assign scroll_animation = settings.scroll_animation

  assign ratio = ''
  if image_ratio != 'adapt'
    case image_ratio
      when 'square'
        assign ratio = '1/1'
      when 'landscape'
        assign ratio = '4/3'
      when 'portrait'
        assign ratio = '3/4'
      else
        if custom_ratio != empty
          assign ratio = custom_ratio | replace: ':', '/'
        else
          assign ratio = '3/4'
        endif
    endcase
  else
    if collection_image != blank
      assign ratio = collection_image.aspect_ratio
    else
      assign ratio = '1/1'
    endif
  endif
-%}

{%- capture style -%}
  --section-pt: {{ section_st.padding_top }};
  --section-pb: {{ section_st.padding_bottom }};

  {% if column_gap < 31 %}
    --col-gap: {{ column_gap }}px;
  {% else %}
    --col-gap: 15px;
    --col-gap-desktop: {{  column_gap }}px;
  {% endif %}
{%- endcapture -%}

{%- style -%}
  .multi-product__product [class*='btn-']::after {
    display: none;
  }
{%- endstyle -%}

<div
  class="section{% if section_st.padding_top < 30 %} pt-min{% endif %}{% if section_st.padding_bottom < 30 %} pb-min{% endif %} sec__collection-overview color-{{ color_scheme }} gradient{{ reset_spacing }}"
  style="{{ style | strip | strip_newlines }}"
>
  <collection-overviews>
    <div class="{{ section_width }}">
      {% if section.blocks.size > 0 %}
        <div
          class="section__block-inner w-full flex gradient flex-column-reverse-768 {% if position == 'right' %} flex-row-reverse{% endif %}"
          style="gap: {{ section_st.column_gap }}px;"
          id="{{ section.id }}"
        >
          {% assign width_value = section_st.child_width %}

          <div
            class="collection-img w-full-768 relative"
            style="width: {{ width_value }}%; --aspect-ratio: {{ ratio }};"
          >
            {%- for block in section.blocks -%}
              {%- liquid
                assign block_st = block.settings
              -%}
              <div
                class="banner__media banner__media-collection rounded absolute inset-0 active"
                data-index="{{ forloop.index }}"
                style="--aspect-ratio: {{ ratio }};"
              >
                {% liquid
                  assign image = block_st.collection_image
                %}
                {%- if image != blank -%}
                  {%- assign image_alt = image.alt | escape | default: 'Images' -%}
                  {% render 'responsive-image',
                    type: 'banner',
                    container: section_width,
                    image: image,
                    image_alt: image_alt
                  %}
                {%- else -%}
                  {%- render 'placeholder-render' -%}
                {% endif %}
                {% if block_st.image_link != blank %}
                  <a
                    class="absolute inset-0 z-2 block"
                    aria-label="{{ block_st.heading }}"
                    href="{{ block_st.image_link }}"
                  ></a>
                {% endif %}
              </div>
            {%- endfor -%}
          </div>

          <div
            class="collection-content flex w-full-768 flex-column justify-{{ section_st.vertical_align }} align-{{ section_st.vertical_align }}"
            style="width: calc(100% - {{ width_value }}%)"
          >
            {% if heading != blank or description != blank or section_st.subheading != blank %}
              <div
                class="section__header w-full mb-30 text-{{ section_st.header_alignment }}"
              >
                {%- if section_st.subheading != blank -%}
                  <motion-element
                    data-motion="fade-up-lg"
                    data-motion-delay="50"
                    {% if scroll_animation != 'slide_in' %}
                      hold
                    {% endif %}
                    class="block {% if scroll_animation != 'none' %} scroll-trigger {{ scroll_animation }}{% endif %}"
                    style="
                      {% if scroll_animation != 'none' %}
                        --animation-order: 0;
                      {% endif %}
                    "
                  >
                    <div
                      class="sec__content-subheading heading-color heading fs-12 {% if description != blank %} mb-10{% else %} mb-0{% endif %}"
                    >
                      {{ section_st.subheading | escape }}
                    </div>
                  </motion-element>
                {% endif %}
                {% if heading != blank %}
                  {% assign width_heading = section_st.width_heading %}
                  <motion-element
                    data-motion="fade-up-lg"
                    data-motion-delay="50"
                    {% if scroll_animation != 'slide_in' %}
                      hold
                    {% endif %}
                    class="block {% if scroll_animation != 'none' %} scroll-trigger {{ scroll_animation }}{% endif %}"
                    style="
                      {% if scroll_animation != 'none' %}
                        --animation-order: 0;
                      {% endif %}
                    "
                  >
                    <div class="w-full flex justify-{{ section_st.header_alignment }} {% if section_st.subheading != blank or description != blank %} mb-10{% else %} mb-0{% endif %}">
                      <h2
                        class="section__header-heading fontsize-heading-1025 {{ header_size  }} w-full-768 heading-letter-spacing m-0"
                        style="width: {{ width_heading }}%;"
                      >
                        {{ heading }}
                      </h2>
                    </div>
                  </motion-element>
                {% endif %}
                {% if description != blank %}
                  <motion-element
                    data-motion="fade-up-lg"
                    data-motion-delay="50"
                    {% if scroll_animation != 'slide_in' %}
                      hold
                    {% endif %}
                    class="block {% if scroll_animation != 'none' %} scroll-trigger {{ scroll_animation }}{% endif %}"
                    style="
                      {% if scroll_animation != 'none' %}
                        --animation-order: 0;
                      {% endif %}
                    "
                  >
                    <div
                      class="section__header-des rich__text-m0 "
                    >
                      {{ description }}
                    </div>
                  </motion-element>
                {% endif %}
              </div>
            {% endif %}

            <div class="w-full wrapper-content-item flex {% if show_image %}show-image{% else %}flex-column{% endif %}">
              {% for block in section.blocks %}
                {% assign block_st = block.settings %}
                <div
                  class="sec__content-overview w-full justify-{{ section_st.header_alignment }} link_list"
                  data-index="{{ forloop.index }}"
                >
                  <motion-element
                    data-motion="fade-up-lg"
                    data-motion-delay="50"
                    {% if scroll_animation != 'slide_in' %}
                      hold
                    {% endif %}
                    class="block {% if scroll_animation != 'none' %} scroll-trigger {{ scroll_animation }}{% endif %}"
                    style="
                      {% if scroll_animation != 'none' %}
                        --animation-order: 0;
                      {% endif %}
                    "
                  >
                    <div
                      tabindex="0"
                      data-index="{{ forloop.index }}"
                      class="pointer content-overview-block newsletter my-md-0"
                    >
                      <div
                        class="sec__content-inner pb-custom px-custom relative{% if block_st.content_padding_inline < 35 %} x-min-value{% endif %}{% if block_st.content_padding_block < 35 %} y-min-value{% endif %}"
                      >
                        {% if show_image %}
                          {% liquid
                            assign image = block_st.collection_image
                          %}
                          <div class="rounded custom-w m-auto"
                            style="--aspect-ratio: 1;--custom-width: {{ section_st.image_width_heading }}px; max-width: var(--custom-width);"
                          >
                            {% if block_st.image_link != blank %}
                              <a
                                class="absolute inset-0 z-2 block rounded"
                                aria-label="{{ block_st.heading }}"
                                href="{{ block_st.image_link }}"
                              >
                            {% endif %}
                            {%- if image != blank -%}
                              {%- assign image_alt = image.alt | escape | default: 'Images' -%}
                              {% render 'responsive-image',
                                type: 'banner',
                                container: section_width,
                                image: image,
                                image_alt: image_alt
                              %}
                            {%- else -%}
                              {%- render 'placeholder-render' -%}
                            {% endif %}
                            {% if block_st.image_link != blank %}
                              </a>
                            {% endif %}
                          </div>
                        {% endif %}
                        {% if block_st.heading != blank %}
                          <div class="collapsible-heading-overview">
                            <h2
                              class="sec__content-heading flex justify-{{ section_st.header_alignment }} w-full btn-icon relative heading-letter-spacing mt-0{% if block_st.heading_spacing_bottom > 41 %} mb-big{% elsif block_st.heading_spacing_bottom > 30 %} mb-medium{% else %} mb-custom{% endif %}{% if block_st.heading_uppercase %} {% endif %}{% if block_st.heading_font_size > 41 %} fs-big{% elsif block_st.heading_font_size > 24 %} fs-medium{% else %} fs-custom{% endif %} {{ block_st.heading_font_weight }}"
                              style="--font-size: {{ block_st.heading_font_size }};padding-bottom: 10px;padding-top: 10px;"
                              {{ block.shopify_attributes }}
                            >
                              <span class="word-break reversed-links relative">{{ block_st.heading }}</span>
                            </h2>
                          </div>
                        {% endif %}
                      </div>
                    </div>
                  </motion-element>
                </div>
              {% endfor %}
            </div>

            {% if section_st.first_button_label != blank  %}
              
              <div class="sec__content-btn align-center flex flex-wrap gap-15 justify-content-{{ section_st.header_alignment }}">
                  <motion-element
                    data-motion="fade-up-lg"
                    data-motion-delay="50"
                    {% if scroll_animation != 'slide_in' %}
                      hold
                    {% endif %}
                    class="block {% if scroll_animation != 'none' %} scroll-trigger {{ scroll_animation }}{% endif %}"
                    style="
                      {% if scroll_animation != 'none' %}
                        --animation-order: 0;
                      {% endif %}
                    "
                  >
                  {% if section_st.first_button_label != blank %}
                    <a
                      {% if section_st.first_button_link == blank %}
                        role="link" aria-disabled="true"
                      {% else %}
                        href="{{ section_st.first_button_link | default: "#" }}"
                      {% endif %}
                      aria-label="{{ section_st.first_button_label }}"
                      class="relative z-3 inline-flex no-underline btn-{{ section_st.first_button_type }}"
                    >
                      {{ section_st.first_button_label }}
                    </a>
                  {% endif %}
                </motion-element>
              </div>
             
            {% endif %}
          </div>
        </div>
      {% endif %}
    </div>
  </collection-overviews>
</div>

{%- style -%}
  .banner__media-collection {
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.8s ease, transform 0.8s ease-in-out;
    z-index: 0;
  }

  .banner__media-collection.active {
    opacity: 1;
    visibility: visible;
    z-index: 1;
  }

  .sec__collection-overview picture.image-picture {
    width: 100%;
  }

  .sec__collection-overview .collection-item__information {
    display: none;
  }

  .sec__content-overview .btn-icon:after {
    height: 13px;
    margin-top: 5px;
  }

  .text-none {
    text-transform: none;
  }

  @media (max-width: 1024.99px) {
    .mb-20-1024 {
      margin-bottom: 20px;
    }
  }
{%- endstyle -%}

{% schema %}
{
  "name": "t:sections.collection_overview.name",
  "tag": "section",
  "disabled_on": {
    "groups": ["header", "footer", "custom.overlay"]
  },
  "settings": [
    {
      "type": "select",
      "id": "section_width",
      "label": "t:sections.all.section_width.label",
      "default": "fluid_container",
      "options": [
        {
          "value": "container",
          "label": "t:sections.all.section_width.container.label"
        },
        {
          "value": "fluid_container",
          "label": "t:sections.all.section_width.fluid_container.label"
        },
        {
          "value": "stretch_width",
          "label": "t:sections.all.section_width.stretch_width.label"
        },
        {
          "value": "full_width",
          "label": "t:sections.all.section_width.full_width.label"
        },
        {
          "value": "full_width overflow-hidden",
          "label": "Full width + overflow hidden"
        }
      ]
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.color_scheme.label",
      "default": "scheme-1"
    },
    {
      "type": "header",
      "content": "t:sections.all.section_header.label"
    },
    {
      "type": "text",
      "id": "heading",
      "label": "t:sections.all.section_header.heading.label",
      "default": "What are you looking for?"
    },
    {
      "type": "text",
      "id": "subheading",
      "label": "t:sections.all.contents.subheading.label"
    },
    {
      "type": "richtext",
      "id": "description",
      "label": "t:sections.all.section_header.description.label",
      "default": "<p>Tops Collection</p>"
    },
    {
      "type": "select",
      "id": "header_size",
      "label": "t:sections.all.section_header.header_size.label",
      "default": "medium",
      "options": [
        {
          "value": "small",
          "label": "t:sections.all.section_header.header_size.small.label"
        },
        {
          "value": "medium",
          "label": "t:sections.all.section_header.header_size.medium.label"
        },
        {
          "value": "large",
          "label": "t:sections.all.section_header.header_size.large.label"
        }
      ]
    },

    {
      "type": "select",
      "id": "header_alignment",
      "label": "t:sections.all.section_header.alignment.label",
      "default": "left",
      "options": [
        {
          "value": "left",
          "label": "t:sections.all.content_settings.content_alignment.left.label"
        },
        {
          "value": "center",
          "label": "t:sections.all.content_settings.content_alignment.center.label"
        },
        {
          "value": "right",
          "label": "t:sections.all.content_settings.content_alignment.right.label"
        }
      ]
    },
    {
      "type": "text",
      "id": "first_button_label",
      "label": "t:sections.all.contents.button.first_button_label.label",
      "info": "t:sections.all.contents.button.first_button_label.info"
    },
    {
      "type": "url",
      "id": "first_button_link",
      "label": "t:sections.all.contents.button.first_button_link.label"
    },
    {
      "type": "select",
      "id": "first_button_type",
      "label": "t:sections.all.contents.button.button_type.label",
      "default": "primary",
      "options": [
        {
          "value": "primary",
          "label": "t:sections.all.contents.button.button_type.primary.label"
        },
        {
          "value": "outline",
          "label": "t:sections.all.contents.button.button_type.outline.label"
        },
        {
          "value": "link",
          "label": "t:sections.all.contents.button.button_type.link.label"
        }
      ]
    },
    {
      "type": "header",
      "content": "t:sections.all.content_settings.label",
      "info": "t:sections.all.content_setting.info"
    },
    {
      "type": "checkbox",
      "id": "show_image",
      "default": false,
      "label": "t:sections.collection_overview.settings.show_image_with_heading.label"
    },
    {
      "type": "range",
      "id": "image_width_heading",
      "label": "t:sections.collection_overview.settings.image_width_heading.label",
      "default": 150,
      "min": 100,
      "max": 350,
      "step": 5,
      "unit": "px"
    },
    {
      "type": "range",
      "id": "column_gap",
      "label": "t:sections.all.items.column_gap.label",
      "default": 60,
      "min": 0,
      "max": 60,
      "step": 5,
      "unit": "px"
    },
    {
      "type": "select",
      "id": "vertical_align",
      "options": [
        {
          "value": "start",
          "label": "t:sections.multi_content.block_settings.vertical_align.top.label"
        },
        {
          "value": "center",
          "label": "t:sections.multi_content.block_settings.vertical_align.middle.label"
        },
        {
          "value": "end",
          "label": "t:sections.multi_content.block_settings.vertical_align.bottom.label"
        }
      ],
      "default": "center",
      "label": "t:sections.multi_content.block_settings.vertical_align.label"
    },
    {
      "type": "range",
      "id": "child_width",
      "label": "t:sections.collection_overview.block_settings.width_img.label",
      "min": 35,
      "max": 65,
      "step": 1,
      "default": 50,
      "unit": "%"
    },
    {
      "type": "select",
      "id": "position",
      "options": [
        {
          "value": "left",
          "label": "t:sections.collection_overview.block_settings.position.left"
        },
        {
          "value": "right",
          "label": "t:sections.collection_overview.block_settings.position.right"
        }
      ],
      "default": "right",
      "label": "t:sections.collection_overview.block_settings.position.label"
    },

    {
      "type": "header",
      "content": "t:sections.collection_overview.block_settings.setting_image.label"
    },

    {
      "type": "select",
      "id": "image_ratio",
      "label": "t:sections.all.image.image_ratio.label",
      "options": [
        {
          "value": "adapt",
          "label": "t:sections.all.image.image_ratio.adapt.label"
        },
        {
          "value": "square",
          "label": "t:sections.all.image.image_ratio.square.label"
        },
        {
          "value": "portrait",
          "label": "t:sections.all.image.image_ratio.portrait.label"
        },
        {
          "value": "landscape",
          "label": "t:sections.all.image.image_ratio.landscape.label"
        },
        {
          "value": "custom",
          "label": "t:sections.all.image.image_ratio.custom.label"
        }
      ],
      "default": "adapt"
    },
    {
      "type": "text",
      "id": "custom_ratio",
      "label": "t:sections.all.image.image_ratio.custom_ratio.label"
    },

    {
      "type": "header",
      "content": "t:sections.all.section_padding.label"
    },
    {
      "type": "range",
      "id": "padding_top",
      "label": "t:sections.all.section_padding.top.label",
      "default": 60,
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px"
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "label": "t:sections.all.section_padding.bottom.label",
      "default": 60,
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px"
    },
    {
      "type": "checkbox",
      "id": "reset_spacing",
      "label": "t:sections.all.section_padding.reset_spacing.label",
      "default": false
    }
  ],
  "blocks": [
    {
      "type": "content_item",
      "name": "t:sections.collection_overview.block_settings.content_item.label",
      "limit": 5,
      "settings": [
        {
          "type": "header",
          "content": "t:sections.collection-packery.blocks.collection"
        },

        {
          "type": "image_picker",
          "id": "collection_image",
          "label": "t:sections.collection-packery.blocks.collection_image.label",
          "info": "t:sections.collection-packery.blocks.collection_image.info"
        },

        {
          "type": "url",
          "id": "image_link",
          "label": "t:sections.all.image.link"
        },

        {
          "type": "header",
          "content": "t:sections.all.contents.label"
        },
        {
          "type": "text",
          "id": "heading",
          "label": "t:sections.all.contents.heading.label",
          "default": "Eco Vero Collection"
        },
        {
          "type": "header",
          "content": "t:sections.all.content_settings.typography.label",
          "info": "t:sections.all.content_setting.info"
        },
        {
          "type": "paragraph",
          "content": "t:sections.all.content_settings.heading.label"
        },
        {
          "type": "range",
          "id": "heading_font_size",
          "label": "t:sections.all.content_settings.font_size.label",
          "default": 18,
          "min": 10,
          "max": 40,
          "step": 1,
          "unit": "px"
        },
        {
          "type": "select",
          "id": "heading_font_weight",
          "label": "t:sections.all.content_settings.font_weight.label",
          "default": "heading_weight",
          "options": [
            {
              "value": "body_weight",
              "label": "t:sections.all.content_settings.font_weight.body_weight.label"
            },
            {
              "value": "subheading_weight",
              "label": "t:sections.all.content_settings.font_weight.subheading_weight.label"
            },
            {
              "value": "heading_weight",
              "label": "t:sections.all.content_settings.font_weight.heading_weight.label"
            }
          ]
        },
        {
          "type": "checkbox",
          "id": "heading_uppercase",
          "label": "t:sections.all.text_transform.uppercase.label",
          "default": true
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "t:sections.collection_overview.name",
      "blocks": [
        {
          "type": "content_item"
        },
        {
          "type": "content_item"
        }
      ]
    }
  ]
}
{% endschema %}
