{%- liquid
  assign section_st = section.settings
  assign section_width = section_st.section_width
  assign color_scheme = section_st.color_scheme
  if section_st.reset_spacing
    assign reset_spacing = ' remove_spacing'
  endif
  assign column_gap = section_st.column_gap
  assign items_per_row = section_st.items_per_row
  assign items_per_row_tablet = 3
  if items_per_row < 3
    assign items_per_row_tablet = items_per_row
  endif
  assign items_per_row_mobile = section_st.items_per_row_mobile
  assign heading = section_st.heading
  assign description = section_st.description
  assign header_size = ''
  if section_st.header_size == 'small'
    assign header_size = 'h3'
  elsif section_st.header_size == 'large'
    assign header_size = 'h1-size'
  endif
  assign show_arrow = section_st.show_arrow
  assign carousel_pagination = section_st.carousel_pagination
  assign infinite = section_st.infinite
  assign autoplay = section_st.autoplay
  assign autorotate_speed = section_st.autorotate_speed
  assign reveal = section_st.reveal
  assign img_width = section_st.img_width
  assign image_ratio = section_st.image_ratio
  assign custom_ratio = section_st.custom_ratio
  assign logo_opacity = section_st.logo_opacity
  assign framed = ''
  if section_st.framed
    assign framed = 'border'
  endif
  assign scroll_animation = settings.scroll_animation
-%}
{%- capture style -%}
  --section-pt: {{ section_st.padding_top }}; --section-pb: {{ section_st.padding_bottom }}; --section-image-width: {{ img_width }}px;
  --logo-opacity: {{ logo_opacity }}%;
{%- endcapture -%}
{%- capture style_ratio -%}
  {%- liquid
    assign ratio = ''
    if image_ratio != 'adapt'
      case image_ratio
        when 'square'
          assign ratio = '1/1'
        when 'landscape'
          assign ratio = '4/3'
        when 'portrait'
          assign ratio = '3/4'
        else
          if custom_ratio != empty
            assign ratio = custom_ratio | replace: ':', '/'
          else
            assign ratio = '3/2'
          endif
      endcase
    else
      assign ratio = '3/2'
    endif
  -%}
  --aspect-ratio: {{ ratio }};--custom-width: {{ section_st.img_width }}px
{%- endcapture -%}
{%- style -%}
  .brand-image {
    opacity: var(--logo-opacity);
  }
  .brand-image:hover {
    opacity: 1;
  }
{%- endstyle -%}
<div
  class="section{% if section_st.padding_top < 30 %} pt-min{% endif %}{% if section_st.padding_bottom < 30 %} pb-min{% endif %} sec__brand-logo color-{{ color_scheme }} gradient{{ reset_spacing }}{% if reveal %} overflow-hidden{% endif %}"
  style="{{ style | strip | strip_newlines }}"
>
  <div class="{{ section_width }}">
    {%- if heading != blank or description != blank -%}
      <div class="section__header mb-33 mb-sm-20 text-{{ section_st.header_alignment }}{% if section_st.section_width == 'full_width' %} px-20{% endif %}{% if section_st.show_view_all_button %} flex gap-15 gap-md-30 flex-wrap {% if section_st.header_alignment == 'center'  %} justify-content-{{ section_st.header_alignment }} {% else %} justify-between {% endif %} align-center{% if section_st.header_alignment == 'right' %} flex-row-reverse{% endif %}{% endif %}">
        {%- if section_st.heading != blank -%}
          <motion-element
             data-motion="fade-up-lg"
                data-motion-delay="50"
                {% if scroll_animation != 'slide_in' %}
                  hold
                {% endif %}
            class="block  {% if scroll_animation != 'none' %} scroll-trigger {{ scroll_animation }}{% endif %}"
            style="
            {%- if scroll_animation != 'none' -%}
              --animation-order: 0;
            {% endif %}
          "
          >
          <h2
            class="section__header-heading heading-letter-spacing {{ header_size  }} mt-0{% if section_st.description != blank %} mb-10{% else %} mb-0{% endif %}"
          >
            {{ section_st.heading }}
          </h2>
        </motion-element>
        {% endif %}
        {%- if section_st.description != blank -%}
          <motion-element
            data-motion="fade-up-lg"
            data-motion-delay="150"
            {% if scroll_animation != 'slide_in' %}
              hold
            {% endif %}
            class="section__header-des block {% if scroll_animation != 'none' -%} scroll-trigger {{ scroll_animation }}{% endif %} rich__text-m0"
            style="
              {%- if scroll_animation != 'none' -%}
                --animation-order: 1
              {% endif %}
            "
          >
            {{ section_st.description }}
          </motion-element>
        {% endif %}
        {% if section_st.show_view_all_button and section_st.header_alignment != 'center' %}
          <a
            class="btn_view-all no-underline inline-flex btn-link"
            href="{% if section_st.view_all_button_link != blank %}{{ section_st.view_all_button_link }}{% else %}#{% endif %}"
            role="link"
            aria-label="{{- 'general.view_all' | t -}}"
          >
            {{- 'general.view_all' | t -}}
          </a>
        {% endif %}
      </div>
    {% endif %}
    <slide-section
      class="swiper{% if reveal %} reveal_on_scroll{% endif %}"
      data-section-id="{{ section.id }}"
      data-autoplay="{{ autoplay }}"
      data-effect="slide"
      data-loop="{{ infinite }}"
      data-autoplay-speed="{{ autorotate_speed }}"
      data-spacing="{{ column_gap }}"
      data-mobile="{{ items_per_row_mobile }}"
      data-tablet="{{ items_per_row_tablet }}"
      data-desktop="{{ items_per_row }}"
      data-pagination-progressbar="{% if carousel_pagination == 'show_progress_bar' %}true{% else %}false{% endif %}"
    >
      {% if show_arrow %}
        {%- render 'swiper-navigation' -%}
      {% endif %}
      <div class="swiper-wrapper">
        {%- for block in section.blocks -%}
          {%- liquid
            assign block_st = block.settings
            assign image = block_st.image
          -%}
          <div
            {{ block.shopify_attributes }}
            class="swiper-slide "
          >
            {% if forloop.index <= items_per_row %}
              {% assign scroll_animation_custom = scroll_animation %}
            {% else %}
              {% assign scroll_animation_custom = blank %}
            {% endif %}
            <motion-element
              data-motion="fade-up-lg"
              {% if scroll_animation != 'slide_in' %}
                hold
              {% endif %}
              data-motion-delay="{{ forloop.index0 | times: 50 }}"
              class="brand-item block {% if scroll_animation_custom != 'none' %} scroll-trigger {{ scroll_animation_custom }}{% endif %}"
              style="
                {%- if scroll_animation_custom != 'none' -%}
                  --animation-order: {{  forloop.index }}
                {% endif %}
              "
            >
              <a
                aria-label="{{ 'sections.brand_logo.alt' | t }}"
                {% if block_st.link == blank %}
                  role="link"
                  aria-disabled="true"
                {% else %}
                  href="{{ block_st.link }}"
                {% endif %}
                {%- if section_st.open_link_newtab != blank -%}
                  target="_blank"
                {%- else -%}
                  target="_self"
                {% endif %}
              >
                <div
                  class="brand-image transition mx-auto w-custom max-w-100 {{ framed }} rounded"
                  style="{{ style_ratio | strip | strip_newlines }}"
                >
                  {% if image != blank %}
                    {%- liquid
                      assign alt = 'sections.brand_logo.alt' | t
                      if image.alt
                        assign alt = image.alt | escape
                      endif
                      assign sizes = block_st.image_width | append: 'px'
                    -%}
                    {% render 'responsive-image',
                      type: 'other',
                      container: section_width,
                      image: image,
                      image_alt: alt,
                      colunm: items_per_row,
                      colunm_mobile: items_per_row_mobile,
                      padding: column_gap,
                      sizes: sizes,
                      class: 'brand-image transition rounded '
                    %}
                  {% else %}
                    {% render 'placeholder-render', class: 'rounded' %}
                  {% endif %}
                </div>
              </a>
            </motion-element>
          </div>
        {%- endfor -%}
      </div>
      {%- if carousel_pagination == 'show_dots'
        or carousel_pagination == 'show_dots_on_mobile'
        or carousel_pagination == 'show_progress_bar'
      -%}
      <motion-element
        data-motion="fade-up-sm"
        {% if scroll_animation != 'slide_in' %}
          hold
        {% endif %}
        data-motion-delay="150"
        class="swiper-pagination  flex flex-wrap  {% if scroll_animation == 'slide_in' %} slide_in scroll-trigger {% endif %}   px-15 lh-1 bottom-30 {% if carousel_pagination == 'show_dots_on_mobile' %} hidden-md{% endif %} justify-content-center{% if section_st.content_below_image %} absolute-md-impo{% endif %}"
        style="--swiper-pagination-bottom: 3rem;--swiper-pagination-position: static;z-index: 2"
      >   
      </motion-element>
      {% endif %}
    </slide-section>
  </div>
</div>
{% schema %}
{
  "name": "t:sections.brand-logo.name",
  "tag": "section",
  "class": "section",
  "disabled_on": {
    "groups": ["header", "footer", "custom.overlay"]
  },
  "settings": [
    {
      "type": "select",
      "id": "section_width",
      "options": [
        {
          "value": "container",
          "label": "t:sections.all.section_width.container.label"
        },
        {
          "value": "fluid_container",
          "label": "t:sections.all.section_width.fluid_container.label"
        },
        {
          "value": "stretch_width",
          "label": "t:sections.all.section_width.stretch_width.label"
        },
        {
          "value": "full_width",
          "label": "t:sections.all.section_width.full_width.label"
        }
      ],
      "default": "fluid_container",
      "label": "t:sections.all.section_width.label"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.color_scheme.label",
      "default": "default-color-scheme"
    },
    {
      "type": "header",
      "content": "t:sections.all.section_header.label"
    },
    {
      "type": "text",
      "id": "heading",
      "default": "Brand logo",
      "label": "t:sections.all.section_header.heading.label"
    },
    {
      "type": "richtext",
      "id": "description",
      "label": "t:sections.all.section_header.description.label"
    },
    {
      "type": "checkbox",
      "id": "show_view_all_button",
      "label": "t:sections.all.section_header.show_view_all_button.label"
    },
    {
      "type": "url",
      "id": "view_all_button_link",
      "label": "t:sections.all.section_header.show_view_all_button.label_link"
    },
    {
      "type": "select",
      "id": "header_size",
      "label": "t:sections.all.section_header.header_size.label",
      "default": "medium",
      "options": [
        {
          "value": "small",
          "label": "t:sections.all.section_header.header_size.small.label"
        },
        {
          "value": "medium",
          "label": "t:sections.all.section_header.header_size.medium.label"
        },
        {
          "value": "large",
          "label": "t:sections.all.section_header.header_size.large.label"
        }
      ]
    },
    {
      "type": "select",
      "id": "header_alignment",
      "label": "t:sections.all.section_header.alignment.label",
      "default": "center",
      "options": [
        {
          "value": "left",
          "label": "t:sections.all.content_settings.content_alignment.left.label"
        },
        {
          "value": "center",
          "label": "t:sections.all.content_settings.content_alignment.center.label"
        },
        {
          "value": "right",
          "label": "t:sections.all.content_settings.content_alignment.right.label"
        }
      ]
    },
    {
      "type": "header",
      "content": "t:sections.brand-logo.settings.brand_setting.label"
    },
    {
      "type": "select",
      "id": "image_ratio",
      "label": "t:sections.all.image.image_ratio.label",
      "options": [
        {
          "value": "adapt",
          "label": "t:sections.all.image.image_ratio.adapt.label"
        },
        {
          "value": "square",
          "label": "t:sections.all.image.image_ratio.square.label"
        },
        {
          "value": "portrait",
          "label": "t:sections.all.image.image_ratio.portrait.label"
        },
        {
          "value": "landscape",
          "label": "t:sections.all.image.image_ratio.landscape.label"
        },
        {
          "value": "custom",
          "label": "t:sections.all.image.image_ratio.custom.label"
        }
      ],
      "default": "adapt"
    },
    {
      "type": "text",
      "id": "custom_ratio",
      "label": "t:sections.all.image.image_ratio.custom_ratio.label"
    },
    {
      "type": "range",
      "id": "img_width",
      "label": "t:sections.brand-logo.settings.brand_setting.img_width.label",
      "info": "t:sections.brand-logo.settings.brand_setting.img_width.info",
      "min": 80,
      "max": 300,
      "step": 5,
      "default": 300,
      "unit": "px"
    },
    {
      "type": "range",
      "id": "logo_opacity",
      "label": "t:sections.brand-logo.settings.brand_setting.logo_opacity",
      "min": 50,
      "max": 100,
      "step": 5,
      "default": 100,
      "unit": "%"
    },
    {
      "type": "checkbox",
      "id": "open_link_newtab",
      "label": "t:sections.brand-logo.settings.brand_setting.open_link_newtab",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "framed",
      "label": "t:sections.brand-logo.settings.brand_setting.framed",
      "default": true
    },
    {
      "type": "range",
      "id": "items_per_row",
      "label": "t:sections.all.items.items_per_row.label",
      "min": 4,
      "max": 6,
      "step": 1,
      "default": 6
    },
    {
      "type": "range",
      "id": "column_gap",
      "label": "t:sections.all.items.column_gap.label",
      "min": 0,
      "max": 50,
      "step": 5,
      "default": 30,
      "unit": "px"
    },
    {
      "type": "header",
      "content": "t:sections.all.carousel_settings.label"
    },
    {
      "type": "checkbox",
      "id": "show_arrow",
      "label": "t:sections.all.carousel_settings.show-next-back.label",
      "default": false
    },
    {
      "type": "select",
      "id": "carousel_pagination",
      "label": "t:sections.all.carousel_settings.pagination.label",
      "options": [
        {
          "value": "disable",
          "label": "t:sections.all.carousel_settings.pagination.disable.label"
        },
        {
          "value": "show_dots",
          "label": "t:sections.all.carousel_settings.pagination.show_dots.label"
        },
        {
          "value": "show_dots_on_mobile",
          "label": "t:sections.all.carousel_settings.pagination.show_dots_on_mobile.label"
        },
        {
          "value": "show_progress_bar",
          "label": "t:sections.all.carousel_settings.pagination.show_progress_bar.label"
        }
      ]
    },
    {
      "type": "checkbox",
      "id": "infinite",
      "label": "t:sections.all.carousel_settings.infinite.label",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "autoplay",
      "label": "t:sections.all.carousel_settings.auto_change.label",
      "default": false
    },
    {
      "type": "range",
      "id": "autorotate_speed",
      "label": "t:sections.all.carousel_settings.change_slides_every.label",
      "max": 6,
      "min": 2,
      "step": 1,
      "unit": "s",
      "default": 5
    },
    {
      "type": "checkbox",
      "id": "reveal",
      "label": "t:sections.all.carousel_settings.reveal.label",
      "info": "t:sections.all.carousel_settings.reveal.info",
      "default": false
    },
    {
      "type": "header",
      "content": "t:sections.all.mobile_options.label"
    },
    {
      "type": "range",
      "id": "items_per_row_mobile",
      "label": "t:sections.all.items.items_per_row.label",
      "min": 1,
      "max": 3,
      "step": 0.5,
      "default": 1
    },
    {
      "type": "header",
      "content": "t:sections.all.section_padding.label"
    },
    {
      "type": "range",
      "id": "padding_top",
      "label": "t:sections.all.section_padding.top.label",
      "default": 0,
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px"
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "label": "t:sections.all.section_padding.bottom.label",
      "default": 0,
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px"
    },
    {
      "type": "checkbox",
      "id": "reset_spacing",
      "label": "t:sections.all.section_padding.reset_spacing.label",
      "default": false
    }
  ],
  "blocks": [
    {
      "type": "brand_items",
      "name": "t:sections.brand-logo.settings.block.brand",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "t:sections.brand-logo.settings.block.image",
          "info": "t:sections.brand-logo.settings.brand_setting.img_width.info_block"
        },
        {
          "type": "url",
          "id": "link",
          "label": "t:sections.brand-logo.settings.block.link"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "t:sections.brand-logo.presets.name",
      "blocks": [
        {
          "type": "brand_items"
        },
        {
          "type": "brand_items"
        },
        {
          "type": "brand_items"
        },
        {
          "type": "brand_items"
        },
        {
          "type": "brand_items"
        },
        {
          "type": "brand_items"
        }
      ]
    }
  ]
}
{% endschema %}
