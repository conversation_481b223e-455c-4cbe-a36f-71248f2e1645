{%- liquid
  assign section_st = section.settings
  assign section_width = section_st.section_width
  assign color_scheme = section_st.color_scheme
  assign column_gap = section_st.column_gap
  assign items_per_row_mobile = section_st.items_per_row_mobile
  assign heading = section_st.heading
  assign description = section_st.description
  assign show_arrow = section_st.show_arrow
  assign carousel_pagination = section_st.carousel_pagination
  assign collection_information_position = section_st.collection_information_position
  assign design = section_st.design
  assign infinite = section_st.infinite
  assign autoplay = section_st.autoplay
  assign autorotate_speed = section_st.autorotate_speed
  assign reveal = section_st.reveal
  assign items_per_row = section_st.items_per_row
  assign header_size = ''
  if section_st.header_size == 'small'
    assign header_size = 'h3'
  elsif section_st.header_size == 'large'
    assign header_size = 'h1-size'
  endif
  if section_st.items_per_row_mobile > 1 and section_st.items_per_row_mobile < 2
    assign data_free_scroll = 'true'
  endif
  assign rounded_image = section_st.rounded_image
  assign image_custom_width = section_st.image_custom_width
  assign scroll_animation = settings.scroll_animation
-%}
{%- capture style -%}
  --section-pt: {{ section_st.padding_top }}; --section-pb: {{ section_st.padding_bottom }};
  {% if column_gap < 15 %}--col-gap: {{ column_gap }}px;{% else %}--col-gap: 15px;--col-gap-desktop: {{  column_gap }}px;{% endif %}
{%- endcapture -%}
{% if collection_information_position == 'below_image' and image_custom_width and rounded_image %}
  {%- style -%}
    .collection-item__media--ratio {
      --aspect-ratio: 1 !important;
    }
  {%- endstyle -%}
{% endif %}
{% if collection.metafields.custom.subcollection.value %}
  {% assign sub_collection = collection.metafields.custom.subcollection.value %}
  {% if sub_collection != blank %}
    <div
      class="section remove_spacing mb-60{% if section_st.padding_top < 30 %} pt-min{% endif %}{% if section_st.padding_bottom < 30 %} pb-min{% endif %} sec__collection-list color-{{ color_scheme }} gradient{% if reveal %} overflow-hidden{% endif %}"
      style="{{ style | strip | strip_newlines }}"
    >
      <div class="{{ section_width }}">
        {%- if heading != blank or description != blank -%}
          <div class="section__header mb-33 mb-sm-20 text-{{ section_st.header_alignment }}{% if section_st.section_width == 'full_width' %} px-20{% endif %}">
            {%- if section_st.heading != blank -%}
              <motion-element
                data-motion="fade-up-lg"
                data-motion-delay="50"
                {% if scroll_animation != 'slide_in' %}
                  hold
                {% endif %}
                class="block  {% if scroll_animation != 'none' %} scroll-trigger {{ scroll_animation }}{% endif %}"
                style="
                  {%- if scroll_animation != 'none' -%}
                    --animation-order: 0;
                  {% endif %}
                "
              >
              <h2
                class="section__header-heading heading-letter-spacing {{ header_size  }} mt-0{% if section_st.description != blank %} mb-10{% else %} mb-0{% endif %}"
                style="
                  {%- if scroll_animation != 'none' -%}
                    --animation-order: 0
                  {% endif %}
                "
              >
                {{ section_st.heading }}
              </h2>
            </motion-element>
            {% endif %}
            {%- if section_st.description != blank -%}
              <motion-element
                data-motion="fade-up-lg"
                data-motion-delay="150"
                {% if scroll_animation != 'slide_in' %}
                  hold
                {% endif %}
                class="section__header-des block {% if scroll_animation != 'none' %} scroll-trigger {{ scroll_animation }} {% endif %} rich__text-m0"
                style="
                  {%- if scroll_animation != 'none' -%}
                    --animation-order: 1
                  {% endif %}
                "
              >
                {{ section_st.description }}
              </motion-element>
            {% endif %}
          </div>
        {% endif %}
        <slide-section
          class="swiper{% if reveal %} reveal_on_scroll{% endif %}"
          data-section-id="{{ section.id }}"
          data-autoplay="{{ autoplay }}"
          data-effect="slide"
          data-loop="{{ infinite }}"
          data-speed="500"
          data-autoplay-speed="{{ autorotate_speed }}"
          data-spacing="{{ column_gap }}"
          data-mobile="{{ items_per_row_mobile }}"
          data-desktop="{{ items_per_row }}"
          data-free-scroll="{{ data_free_scroll }}"
          data-pagination-progressbar="{% if carousel_pagination == 'show_progress_bar' %}true{% else %}false{% endif %}"
        >
          {% if show_arrow %}
            {%- render 'swiper-navigation' -%}
          {% endif %}
          <div class="swiper-wrapper">
            {% for collection in sub_collection %}
              <div class="swiper-slide">
                <motion-element
                  data-motion="fade-up-lg"
                  {% if scroll_animation != 'slide_in' %}
                    hold
                  {% endif %}
                  data-motion-delay="{{ forloop.index0 | times: 50 }}"
                  class="collection-item block {% if scroll_animation != 'none' and scroll_animation != null %} scroll-trigger {{ scroll_animation }} {% endif %} {{ settings.hover_effect }}"
                  {%- if scroll_animation != 'none' -%}
                    style="--animation-order: {{  forloop.index }};"
                  {%- endif -%}
                >
                  {% render 'collection-item',
                    card_collection: collection,
                    section: section,
                    collection_information_position: collection_information_position,
                    design: design
                  %}
                </motion-element>
              </div>
            
            {% endfor %}
          </div>
          {%- if carousel_pagination == 'show_dots'
            or carousel_pagination == 'show_dots_on_mobile'
            or carousel_pagination == 'show_progress_bar'
          -%}
          <motion-element
            data-motion="fade-up-sm"
            {% if scroll_animation != 'slide_in' %}
              hold
            {% endif %} 
            data-motion-delay="150"
            class="swiper-pagination  flex flex-wrap  {% if scroll_animation == 'slide_in' %} slide_in scroll-trigger {% endif %}   px-15 lh-1 bottom-30 {% if carousel_pagination == 'show_dots_on_mobile' %} hidden-md{% endif %} justify-content-center{% if section_st.content_below_image %} absolute-md-impo{% endif %}"
            style="--swiper-pagination-bottom: 3rem;--swiper-pagination-position: static;z-index: 2"
          >   
          </motion-element>
          {% endif %}
        </slide-section>
      </div>
    </div>
  {% endif %}
{% endif %}
{% schema %}
{
  "name": "t:sections.subcollection-list.name",
  "tag": "section",
  "settings": [
    {
      "type": "select",
      "id": "section_width",
      "label": "t:sections.all.section_width.label",
      "default": "fluid_container",
      "options": [
        {
          "value": "container",
          "label": "t:sections.all.section_width.container.label"
        },
        {
          "value": "fluid_container",
          "label": "t:sections.all.section_width.fluid_container.label"
        },
        {
          "value": "stretch_width",
          "label": "t:sections.all.section_width.stretch_width.label"
        },
        {
          "value": "full_width",
          "label": "t:sections.all.section_width.full_width.label"
        }
      ]
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.color_scheme.label",
      "default": "scheme-1"
    },
    {
      "type": "header",
      "content": "t:sections.all.section_header.label"
    },
    {
      "type": "text",
      "id": "heading",
      "label": "t:sections.all.section_header.heading.label",
      "default": "Collection list"
    },
    {
      "type": "richtext",
      "id": "description",
      "label": "t:sections.all.section_header.description.label"
    },
    {
      "type": "select",
      "id": "header_size",
      "label": "t:sections.all.section_header.header_size.label",
      "default": "medium",
      "options": [
        {
          "value": "small",
          "label": "t:sections.all.section_header.header_size.small.label"
        },
        {
          "value": "medium",
          "label": "t:sections.all.section_header.header_size.medium.label"
        },
        {
          "value": "large",
          "label": "t:sections.all.section_header.header_size.large.label"
        }
      ]
    },
    {
      "type": "select",
      "id": "header_alignment",
      "label": "t:sections.all.section_header.alignment.label",
      "default": "left",
      "options": [
        {
          "value": "left",
          "label": "t:sections.all.section_header.alignment.left.label"
        },
        {
          "value": "center",
          "label": "t:sections.all.section_header.alignment.center.label"
        },
        {
          "value": "right",
          "label": "t:sections.all.section_header.alignment.right.label"
        }
      ]
    },
    {
      "type": "header",
      "content": "t:sections.collection-list.settings.collection.header"
    },
    {
      "type": "select",
      "id": "image_ratio",
      "label": "t:sections.all.image.image_ratio.label",
      "options": [
        {
          "value": "adapt",
          "label": "t:sections.all.image.image_ratio.adapt.label"
        },
        {
          "value": "square",
          "label": "t:sections.all.image.image_ratio.square.label"
        },
        {
          "value": "portrait",
          "label": "t:sections.all.image.image_ratio.portrait.label"
        },
        {
          "value": "landscape",
          "label": "t:sections.all.image.image_ratio.landscape.label"
        },
        {
          "value": "custom",
          "label": "t:sections.all.image.image_ratio.custom.label"
        }
      ],
      "default": "adapt"
    },
    {
      "type": "text",
      "id": "custom_ratio",
      "label": "t:sections.all.image.image_ratio.custom_ratio.label"
    },
    {
      "type": "range",
      "id": "items_per_row",
      "label": "t:sections.all.items.items_per_row.label",
      "min": 2,
      "max": 6,
      "step": 1,
      "default": 4
    },
    {
      "type": "range",
      "id": "column_gap",
      "label": "t:sections.all.items.column_gap.label",
      "min": 0,
      "max": 50,
      "step": 5,
      "default": 30,
      "unit": "px"
    },
    {
      "type": "header",
      "content": "t:sections.collection-list.settings.collection_information.header"
    },
    {
      "type": "select",
      "id": "collection_information_position",
      "label": "t:sections.collection-list.settings.collection.collection_information_position.label",
      "options": [
        {
          "value": "overlay_image",
          "label": "t:sections.collection-list.settings.collection.collection_information_position.overlay_image"
        },
        {
          "value": "below_image",
          "label": "t:sections.collection-list.settings.collection.collection_information_position.below_image"
        }
      ],
      "default": "overlay_image"
    },
    {
      "type": "select",
      "id": "design",
      "label": "t:sections.all.design.label",
      "options": [
        {
          "value": "default",
          "label": "t:sections.all.design.default.label"
        },
        {
          "value": "morden",
          "label": "t:sections.all.design.morden.label"
        }
      ],
      "default": "default"
    },
    {
      "type": "select",
      "id": "text_alignment",
      "label": "t:sections.all.content_settings.content_alignment.label",
      "default": "center",
      "options": [
        {
          "value": "left",
          "label": "t:sections.all.content_settings.content_alignment.left.label"
        },
        {
          "value": "center",
          "label": "t:sections.all.content_settings.content_alignment.center.label"
        },
        {
          "value": "right",
          "label": "t:sections.all.content_settings.content_alignment.right.label"
        }
      ]
    },
    {
      "type": "checkbox",
      "id": "show_product_count",
      "label": "t:sections.collection-list.settings.collection.show_product_count"
    },
    {
      "type": "paragraph",
      "content": "t:sections.collection-list.settings.collection_information.collection_name"
    },
    {
      "type": "range",
      "id": "font_size",
      "label": "t:sections.all.content_settings.font_size.label",
      "min": 12,
      "max": 20,
      "step": 1,
      "default": 16,
      "unit": "px"
    },
    {
      "type": "select",
      "id": "font_weight",
      "label": "t:sections.all.content_settings.font_weight.label",
      "default": "heading_weight",
      "options": [
        {
          "value": "body_weight",
          "label": "t:sections.all.content_settings.font_weight.body_weight.label"
        },
        {
          "value": "subheading_weight",
          "label": "t:sections.all.content_settings.font_weight.subheading_weight.label"
        },
        {
          "value": "heading_weight",
          "label": "t:sections.all.content_settings.font_weight.heading_weight.label"
        }
      ]
    },
    {
      "type": "header",
      "content": "t:sections.collection-list.settings.collection.collection_information_position.content_below_image"
    },
    {
      "type": "range",
      "id": "information_spacing",
      "label": "t:sections.all.content_settings.spacing.spacing_top.label",
      "min": 0,
      "max": 30,
      "step": 1,
      "default": 12,
      "unit": "px"
    },
    {
      "type": "checkbox",
      "id": "rounded_image",
      "label": "t:sections.collection-list.settings.collection.rounded_image",
      "info": "t:sections.collection-list.settings.collection.image_custom_width.info"
    },
    {
      "type": "checkbox",
      "id": "image_custom_width",
      "label": "t:sections.collection-list.settings.collection.image_custom_width.label",
      "info": "t:sections.collection-list.settings.collection.image_custom_width.info"
    },
    {
      "type": "range",
      "id": "image_width",
      "label": "t:sections.collection-list.settings.collection.image_width",
      "min": 100,
      "max": 300,
      "step": 5,
      "default": 200,
      "unit": "px"
    },
    {
      "type": "header",
      "content": "t:sections.all.carousel_settings.label"
    },
    {
      "type": "checkbox",
      "id": "show_arrow",
      "label": "t:sections.all.carousel_settings.show-next-back.label",
      "default": false
    },
    {
      "type": "select",
      "id": "carousel_pagination",
      "label": "t:sections.all.carousel_settings.pagination.label",
      "options": [
        {
          "value": "disable",
          "label": "t:sections.all.carousel_settings.pagination.disable.label"
        },
        {
          "value": "show_dots",
          "label": "t:sections.all.carousel_settings.pagination.show_dots.label"
        },
        {
          "value": "show_dots_on_mobile",
          "label": "t:sections.all.carousel_settings.pagination.show_dots_on_mobile.label"
        },
        {
          "value": "show_progress_bar",
          "label": "t:sections.all.carousel_settings.pagination.show_progress_bar.label"
        }
      ]
    },
    {
      "type": "checkbox",
      "id": "infinite",
      "label": "t:sections.all.carousel_settings.infinite.label",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "autoplay",
      "label": "t:sections.all.carousel_settings.auto_change.label",
      "default": false
    },
    {
      "type": "range",
      "id": "autorotate_speed",
      "label": "t:sections.all.carousel_settings.change_slides_every.label",
      "max": 6,
      "min": 2,
      "step": 1,
      "unit": "s",
      "default": 5
    },
    {
      "type": "checkbox",
      "id": "reveal",
      "label": "t:sections.all.carousel_settings.reveal.label",
      "info": "t:sections.all.carousel_settings.reveal.info",
      "default": false
    },
    {
      "type": "header",
      "content": "t:sections.all.mobile_options.label"
    },
    {
      "type": "range",
      "id": "items_per_row_mobile",
      "label": "t:sections.all.items.items_per_row.label",
      "min": 1,
      "max": 3,
      "step": 0.5,
      "default": 1
    },
    {
      "type": "header",
      "content": "t:sections.all.section_padding.label"
    },
    {
      "type": "range",
      "id": "padding_top",
      "label": "t:sections.all.section_padding.top.label",
      "default": 0,
      "min": 0,
      "max": 100,
      "step": 2,
      "unit": "px"
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "label": "t:sections.all.section_padding.bottom.label",
      "default": 0,
      "min": 0,
      "max": 100,
      "step": 2,
      "unit": "px"
    }
  ]
}
{% endschema %}
