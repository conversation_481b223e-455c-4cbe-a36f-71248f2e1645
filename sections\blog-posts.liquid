{%- liquid
  assign section_st = section.settings
  assign section_width = section_st.section_width
  assign color_scheme = section_st.color_scheme
  if section_st.reset_spacing
    assign reset_spacing = ' remove_spacing'
  endif
  assign column_gap = section_st.column_gap
  assign items_per_row = section_st.items_per_row
  assign items_to_show = section_st.items_to_show
  assign items_per_row_mobile = section_st.items_per_row_mobile
  assign heading = section_st.heading
  assign description = section_st.description
  assign header_size = ''
  if section_st.header_size == 'small'
    assign header_size = 'h3'
  elsif section_st.header_size == 'large'
    assign header_size = 'h1-size'
  endif
  assign show_arrow = section_st.show_arrow
  assign carousel_pagination = section_st.carousel_pagination
  assign infinite = section_st.infinite
  assign autoplay = section_st.autoplay
  assign autorotate_speed = section_st.autorotate_speed
  assign reveal = section_st.reveal
  assign show_category = section_st.show_category
  assign show_date = section_st.show_date
  assign show_comment_count = section_st.show_comment_count
  assign show_excerpt = section_st.show_excerpt
  assign show_author = section_st.show_author
  assign image_ratio = section_st.image_ratio
  assign custom_ratio = section_st.custom_ratio
  assign content_alignment = section_st.content_alignment
  assign design = section_st.design
  if section_st.items_per_row_mobile > 1 and section_st.items_per_row_mobile < 2
    assign data_free_scroll = 'true'
  endif
  assign scroll_animation = settings.scroll_animation
-%}
{%- capture style -%}
  --section-pt: {{ section_st.padding_top }}; --section-pb: {{ section_st.padding_bottom }};
{%- endcapture -%}
{%- capture style_ratio -%}
  {%- liquid
    assign ratio = ''
    if image_ratio != 'adapt'
      case image_ratio
        when 'square'
          assign ratio = '1/1'
        when 'landscape'
          assign ratio = '4/3'
        when 'portrait'
          assign ratio = '3/4'
        else
          if custom_ratio != empty
            assign ratio = custom_ratio | replace: ':', '/'
          else
            assign ratio = '3/2'
          endif
      endcase
    else
      if article.image != blank
        assign ratio = article.image.aspect_ratio
      else
        assign ratio = '3/2'
      endif
    endif
  -%}
  --aspect-ratio: {{ ratio }};
{%- endcapture -%}
{%- capture col_style -%}
{% if column_gap < 15 %}--col-gap: {{ column_gap }}px;{% else %}--col-gap: 15px;--col-gap-desktop: {{  column_gap }}px;{% endif %}
{%- endcapture -%}
<div
  class="section{% if section_st.padding_top < 30 %} pt-min{% endif %}{% if section_st.padding_bottom < 30 %} pb-min{% endif %} sec__blog-post color-{{ color_scheme }} gradient{{ reset_spacing }} {{ settings.hover_effect }}{% if reveal %} overflow-hidden{% endif %}"
  style="{{ style | strip | strip_newlines }}"
>
  <div class="{{ section_width }}">
    {%- if heading != blank or description != blank or section_st.show_view_all != blank -%}
      <div class="section__header mb-33 mb-sm-20 text-{{ section_st.header_alignment }}{% if section_st.section_width == 'full_width' %} px-20{% endif %}{% if section_st.show_view_all != blank and section_st.header_alignment != 'center' %} flex gap-15 gap-md-30 flex-wrap {% if section_st.header_alignment == 'center'  %} justify-content-{{ section_st.header_alignment }} {% else %} justify-between {% endif %} align-center{% if section_st.header_alignment == 'right' %} flex-row-reverse{% endif %}{% endif %}">
        {%- if heading != blank or description != blank -%}
          <div class="secion__header-inner">
            {%- if section_st.heading != blank -%}
              <motion-element
                data-motion="fade-up-lg"
                data-motion-delay="50"
                {% if scroll_animation != 'slide_in' %}
                  hold
                {% endif %}
                class="block  {% if scroll_animation != 'none' %} scroll-trigger {{ scroll_animation }}{% endif %}"
                style="
                  {%- if scroll_animation != 'none' -%}
                    --animation-order: 0;
                  {% endif %}
                "
              >
                <h2
                  class="section__header-heading heading-letter-spacing  {{ header_size  }} mt-0{% if section_st.description != blank %} mb-10{% else %} mb-0{% endif %}"
                >
                  {{ section_st.heading }}
                </h2>
              </motion-element>
            {% endif %}
            {%- if section_st.description != blank -%}
              <motion-element
                data-motion="fade-up-lg"
                data-motion-delay="150"
                {% if scroll_animation != 'slide_in' %}
                  hold
                {% endif %}
                class="section__header-des block rich__text-m0 {% if scroll_animation != 'none' %} scroll-trigger {{ scroll_animation }}{% endif %}"
                style="
                  {%- if scroll_animation != 'none' -%}
                    --animation-order: 1
                  {% endif %}
                "
              >
                {{ section_st.description }}
              </motion-element>
            {% endif %}
          </div>
        {% endif %}
        {% if section_st.show_view_all != blank and section_st.header_alignment != 'center' %}
          <a
            href="{{ section_st.blog.url }}"
            aria-label="{{ 'blog_post.view_all' | t }}"
            class="btn_view-all no-underline inline-flex btn-link"
          >
            {{ 'blog_post.view_all' | t }}
          </a>
        {% endif %}
      </div>
    {% endif %}
    <div class="blog-posts-main {{ design }}">
      {% if section_st.items_per_row_mobile > 1 and section_st.items_per_row_mobile < 2 %}
        <div class="free-scroll">
      {% endif %}
      {%- assign blog = blogs[section_st.blog] -%}
      {%- if blog.articles.size > 0 -%}
        <slide-section
          class="swiper{% if reveal %} reveal_on_scroll{% endif %}"
          data-section-id="{{ section.id }}"
          data-autoplay="{{ autoplay }}"
          data-effect="slide"
          data-loop="{{ infinite }}"
          data-autoplay-speed="{{ autorotate_speed }}"
          data-spacing="{{ column_gap }}"
          data-mobile="{{ items_per_row_mobile }}"
          data-desktop="{{ items_per_row }}"
          data-pagination-progressbar="{% if carousel_pagination == 'show_progress_bar' %}true{% else %}false{% endif %}"
          data-free-scroll="{{ data_free_scroll }}"
          style="{{ col_style | strip | strip_newlines }}"
          data-arrow-centerimage="1"
        >
          {% if show_arrow %}
            {%- render 'swiper-navigation' -%}
          {% endif %}
          <div class="swiper-wrapper">
            {%- for article in blog.articles limit: items_to_show -%}
              <div
                class="swiper-slide blog-posts-item"
              >
                {% if items_per_row > items_to_show %}
                  {% assign scroll_animation_custom = scroll_animation %}

                {% else %}
                  {% if forloop.index <= items_per_row %}
                    {% assign scroll_animation_custom = scroll_animation %}
                  {% else %}
                    {% assign scroll_animation_custom = '' %}
                  {% endif %}
                {% endif %}
                {%- render 'article-card',
                  article: article,
                  scroll_animation: scroll_animation_custom,
                  show_category: show_category,
                  show_date: show_date,
                  show_excerpt: show_excerpt,
                  show_author: show_author,
                  show_comment_count: show_comment_count,
                  section_width: section_width,
                  items_per_row: items_per_row,
                  items_per_row_mobile: items_per_row_mobile,
                  column_gap: column_gap,
                  image_ratio: image_ratio,
                  custom_ratio: custom_ratio,
                  indexFor: forloop.index
                -%}
              </div>
            {%- endfor -%}
          </div>
          {%- if carousel_pagination == 'show_dots'
            or carousel_pagination == 'show_dots_on_mobile'
            or carousel_pagination == 'show_progress_bar'
          -%}
          <motion-element
            data-motion="fade-up-sm"
            {% if scroll_animation != 'slide_in' %}
              hold
            {% endif %}
            data-motion-delay="150"
            class="swiper-pagination  flex flex-wrap  {% if scroll_animation == 'slide_in' %} slide_in scroll-trigger {% endif %}   px-15 lh-1 bottom-30 {% if carousel_pagination == 'show_dots_on_mobile' %} hidden-md{% endif %} justify-content-center{% if section_st.content_below_image %} absolute-md-impo{% endif %}"
            style="--swiper-pagination-bottom: 3rem;--swiper-pagination-position: static;z-index: 2"
          >   
          </motion-element>
          {% endif %}
        </slide-section>
      {%- else -%}
        <slide-section
          class="swiper{% if reveal %} reveal_on_scroll{% endif %}"
          data-section-id="{{ section.id }}"
          data-autoplay="{{ autoplay }}"
          data-effect="slide"
          data-loop="{{ infinite }}"
          data-autoplay-speed="{{ autorotate_speed }}"
          data-spacing="{{ column_gap }}"
          data-mobile="{{ items_per_row_mobile }}"
          data-desktop="{{ items_per_row }}"
          data-pagination-progressbar="{% if carousel_pagination == 'show_progress_bar' %}true{% else %}false{% endif %}"
          data-free-scroll="{{ data_free_scroll }}"
          style="{{ col_style | strip | strip_newlines }}"
        >
          {% if show_arrow %}
            {%- render 'swiper-navigation' -%}
          {% endif %}
          <div class="swiper-wrapper">
            {% for i in (1..items_to_show) %}
              <div
                class="swiper-slide "
              >
                {% if items_per_row > items_to_show %}
                  {% assign add_animation = true %}

                {% else %}
                  {% if forloop.index <= items_per_row %}
                    {% assign add_animation = true %}
                  {% endif %}
                {% endif %}
                <motion-element
                  data-motion="fade-up-lg"
                  {% if scroll_animation != 'slide_in' %}
                    hold
                  {% endif %}
                  data-motion-delay="{{ forloop.index0 | times: 50 }}"
                  class="blog-posts-item block {% if scroll_animation != 'none' %} scroll-trigger {{ scroll_animation }}{% endif %}"
                  {% if scroll_animation and add_animation == true %}
                    data-cascade
                    style="--animation-order: {{ i }};"
                  {% endif %}
                >
                  <div class="blog-posts-image rounded hover-effect" style="{{ style_ratio | strip | strip_newlines }}">
                    {% render 'placeholder-render', class: 'rounded transition' %}
                  </div>
                  <div
                    class="blog-posts-info text-{{ content_alignment }} mt-30"
                  >
                    {%- if show_category -%}
                      <div class="blog-posts-category fs-13 subheading_weight mb-15 text-{{ content_alignment }}">
                        <a
                          href="#"
                          class="inline-flex btn-rounded border py-8 px-20 lh-normal no-underline heading-color"
                        >
                          <span>{{ 'blog_post.category' | t }}</span>
                        </a>
                      </div>
                    {% endif %}
                    {%- if article.title != blank -%}
                      <h3 class="blog-posts-title fs-20 mt-0 mb-10 heading-letter-spacing">
                        <a href="#" arial-label="{{ 'blog_post.title' | t }}" class="no-underline heading-color">
                          {{ 'blog_post.title' | t }}
                        </a>
                      </h3>
                    {% endif %}
                    {%- if show_excerpt -%}
                      <div class="blog-posts-content mb-20">
                        {{ 'blog_post.short_content' | t }}
                      </div>
                    {% endif %}
                    <div class="blog-posts-bottom flex gap-10 justify-content-{{ content_alignment }}">
                      {%- if show_date or show_author -%}
                        {%- if show_date -%}
                          <div class="blog-posts-date">
                            <span>
                              {{ 'blog_post.date' | t }}
                            </span>
                          </div>
                        {% endif %}
                        {%- if show_author -%}
                          <div class="blog-posts-author">
                            <span>
                              {{- 'blog_post.post_by' | t }}
                              {{ 'blog_post.author' | t -}}
                            </span>
                          </div>
                        {% endif %}
                      {% endif %}
                    </div>
                  </div>
                </motion-element>
              </div>
            {% endfor %}
          </div>
          {%- if carousel_pagination == 'show_dots'
            or carousel_pagination == 'show_dots_on_mobile'
            or carousel_pagination == 'show_progress_bar'
          -%}
          <motion-element
            data-motion="fade-up-sm"
            {% if scroll_animation != 'slide_in' %}
              hold
            {% endif %}
            data-motion-delay="150"
            class="swiper-pagination  flex flex-wrap  {% if scroll_animation == 'slide_in' %} slide_in scroll-trigger {% endif %}   px-15 lh-1 bottom-30 {% if carousel_pagination == 'show_dots_on_mobile' %} hidden-md{% endif %} justify-content-center{% if section_st.content_below_image %} absolute-md-impo{% endif %}"
            style="--swiper-pagination-bottom: 3rem;--swiper-pagination-position: static;z-index: 2"
          >   
          </motion-element>
          {% endif %}
        </slide-section>
      {% endif %}
      {% if section_st.items_per_row_mobile > 1 and section_st.items_per_row_mobile < 2 %}
        </div>
      {% endif %}
      {% if section_st.show_view_all != blank and section_st.header_alignment == 'center' %}
        <div class="text-center mt-30">
          <a
            href="{{ section_st.blog.url }}"
            aria-label="{{ 'blog_post.view_all' | t }}"
            class="inline-flex btn-primary load-more no-underline relative"
          >
            {{ 'blog_post.view_all' | t }}
          </a>
        </div>
      {% endif %}
    </div>
  </div>
</div>
{% schema %}
{
  "name": "t:sections.blog-post.name",
  "tag": "section",
  "class": "section",
  "disabled_on": {
    "groups": ["header", "footer", "custom.overlay"]
  },
  "settings": [
    {
      "type": "select",
      "id": "section_width",
      "options": [
        {
          "value": "container",
          "label": "t:sections.all.section_width.container.label"
        },
        {
          "value": "fluid_container",
          "label": "t:sections.all.section_width.fluid_container.label"
        },
        {
          "value": "stretch_width",
          "label": "t:sections.all.section_width.stretch_width.label"
        },
        {
          "value": "full_width",
          "label": "t:sections.all.section_width.full_width.label"
        }
      ],
      "default": "fluid_container",
      "label": "t:sections.all.section_width.label"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.color_scheme.label",
      "default": "default-color-scheme"
    },
    {
      "type": "header",
      "content": "t:sections.all.section_header.label"
    },
    {
      "type": "text",
      "id": "heading",
      "default": "Blog post",
      "label": "t:sections.all.section_header.heading.label"
    },
    {
      "type": "richtext",
      "id": "description",
      "label": "t:sections.all.section_header.description.label"
    },
    {
      "type": "checkbox",
      "id": "show_view_all",
      "label": "t:sections.all.section_header.show_view_all_button.label"
    },
    {
      "type": "select",
      "id": "header_size",
      "label": "t:sections.all.section_header.header_size.label",
      "default": "medium",
      "options": [
        {
          "value": "small",
          "label": "t:sections.all.section_header.header_size.small.label"
        },
        {
          "value": "medium",
          "label": "t:sections.all.section_header.header_size.medium.label"
        },
        {
          "value": "large",
          "label": "t:sections.all.section_header.header_size.large.label"
        }
      ]
    },
    {
      "type": "select",
      "id": "header_alignment",
      "label": "t:sections.all.section_header.alignment.label",
      "default": "center",
      "options": [
        {
          "value": "left",
          "label": "t:sections.all.content_settings.content_alignment.left.label"
        },
        {
          "value": "center",
          "label": "t:sections.all.content_settings.content_alignment.center.label"
        },
        {
          "value": "right",
          "label": "t:sections.all.content_settings.content_alignment.right.label"
        }
      ]
    },
    {
      "type": "header",
      "content": "t:sections.blog-post.settings.header.label"
    },
    {
      "type": "blog",
      "id": "blog",
      "label": "t:sections.blog-post.settings.blog.label"
    },
    {
      "type": "select",
      "id": "image_ratio",
      "label": "t:sections.all.image.image_ratio.label",
      "options": [
        {
          "value": "adapt",
          "label": "t:sections.all.image.image_ratio.adapt.label"
        },
        {
          "value": "square",
          "label": "t:sections.all.image.image_ratio.square.label"
        },
        {
          "value": "portrait",
          "label": "t:sections.all.image.image_ratio.portrait.label"
        },
        {
          "value": "landscape",
          "label": "t:sections.all.image.image_ratio.landscape.label"
        },
        {
          "value": "custom",
          "label": "t:sections.all.image.image_ratio.custom.label"
        }
      ],
      "default": "square"
    },
    {
      "type": "text",
      "id": "custom_ratio",
      "label": "t:sections.all.image.image_ratio.custom_ratio.label"
    },
    {
      "type": "checkbox",
      "id": "show_category",
      "label": "t:sections.blog-post.settings.blog_setting.show_category",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_author",
      "label": "t:sections.blog-post.settings.blog_setting.show_author",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_date",
      "label": "t:sections.blog-post.settings.blog_setting.show_date",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_comment_count",
      "label": "t:sections.blog-post.settings.blog_setting.show_comment_count",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_excerpt",
      "label": "t:sections.blog-post.settings.blog_setting.show_excerpts",
      "default": true
    },
    {
      "type": "select",
      "id": "content_alignment",
      "label": "t:sections.all.content_settings.content_alignment.label",
      "default": "left",
      "options": [
        {
          "value": "left",
          "label": "t:sections.all.content_settings.content_alignment.left.label"
        },
        {
          "value": "center",
          "label": "t:sections.all.content_settings.content_alignment.center.label"
        },
        {
          "value": "right",
          "label": "t:sections.all.content_settings.content_alignment.right.label"
        }
      ]
    },
    {
      "type": "select",
      "id": "design",
      "label": "t:sections.all.design.label",
      "options": [
        {
          "value": "default",
          "label": "t:sections.all.design.default.label"
        },
        {
          "value": "morden",
          "label": "t:sections.all.design.morden.label"
        }
      ],
      "default": "default"
    },
    {
      "type": "range",
      "id": "items_to_show",
      "label": "t:sections.all.items.items_to_show.label",
      "min": 2,
      "max": 8,
      "step": 1,
      "default": 4
    },
    {
      "type": "range",
      "id": "items_per_row",
      "label": "t:sections.all.items.items_per_row_on_desktop.label",
      "min": 2,
      "max": 5,
      "step": 1,
      "default": 4
    },
    {
      "type": "range",
      "id": "column_gap",
      "label": "t:sections.all.items.column_gap.label",
      "min": 0,
      "max": 50,
      "step": 5,
      "default": 30,
      "unit": "px"
    },
    {
      "type": "header",
      "content": "t:sections.all.carousel_settings.label"
    },
    {
      "type": "checkbox",
      "id": "show_arrow",
      "label": "t:sections.all.carousel_settings.show-next-back.label",
      "default": false
    },
    {
      "type": "select",
      "id": "carousel_pagination",
      "label": "t:sections.all.carousel_settings.pagination.label",
      "options": [
        {
          "value": "disable",
          "label": "t:sections.all.carousel_settings.pagination.disable.label"
        },
        {
          "value": "show_dots",
          "label": "t:sections.all.carousel_settings.pagination.show_dots.label"
        },
        {
          "value": "show_dots_on_mobile",
          "label": "t:sections.all.carousel_settings.pagination.show_dots_on_mobile.label"
        },
        {
          "value": "show_progress_bar",
          "label": "t:sections.all.carousel_settings.pagination.show_progress_bar.label"
        }
      ]
    },
    {
      "type": "checkbox",
      "id": "infinite",
      "label": "t:sections.all.carousel_settings.infinite.label",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "autoplay",
      "label": "t:sections.all.carousel_settings.auto_change.label",
      "default": false
    },
    {
      "type": "range",
      "id": "autorotate_speed",
      "label": "t:sections.all.carousel_settings.change_slides_every.label",
      "max": 6,
      "min": 2,
      "step": 1,
      "unit": "s",
      "default": 5
    },
    {
      "type": "checkbox",
      "id": "reveal",
      "label": "t:sections.all.carousel_settings.reveal.label",
      "info": "t:sections.all.carousel_settings.reveal.info",
      "default": false
    },
    {
      "type": "header",
      "content": "t:sections.all.mobile_options.label"
    },
    {
      "type": "range",
      "id": "items_per_row_mobile",
      "label": "t:sections.all.items.items_per_row.label",
      "min": 1,
      "max": 2,
      "step": 0.5,
      "default": 1
    },
    {
      "type": "header",
      "content": "t:sections.all.section_padding.label"
    },
    {
      "type": "range",
      "id": "padding_top",
      "label": "t:sections.all.section_padding.top.label",
      "default": 0,
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px"
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "label": "t:sections.all.section_padding.bottom.label",
      "default": 0,
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px"
    },
    {
      "type": "checkbox",
      "id": "reset_spacing",
      "label": "t:sections.all.section_padding.reset_spacing.label",
      "default": false
    }
  ],
  "presets": [
    {
      "name": "t:sections.blog-post.presets.name"
    }
  ]
}
{% endschema %}
