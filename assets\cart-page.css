.main-cart .checkmark:before {
  --grey-color: var(--color-white);
}
.main-cart textarea {
  min-height: 12rem;
}
cart-remove-button .underline {
  text-underline-offset: 2px;
}
.cart_page-countdown {
  border: 1px dashed var(--color-primary);
  background-color: rgba(var(--color-primary-rgb), 0.08);
}
.cart_page-recommend.cart-recommend.inside .product-item__wrapper {
  border-color: var(--color-border);
}
.js-contents .caption-large a {
  text-decoration: none;
}
.js-contents .caption-large a:not(:hover) {
  color: var(--color-heading);
}
@media screen and (min-width: 1025px) {
  .cart-item__details {
    width: 35%;
  }
  .cart__page-price {
    width: 20%;
  }
}
@media (min-width: 1025px) and (max-width: 1200px) {
  .page-cart {
    --col-width: 38rem !important;
  }
}
@media screen and (max-width: 767.98px) {
  .page__cart-item {
    display: grid;
    padding-bottom: 2rem;
    margin-bottom: 2rem;
    border-bottom: 1px solid var(--color-border);
    --custom-width: 10rem !important;
    position: relative;
    grid-template-columns: 10rem 1fr;
    column-gap: 1.5rem;
  }
  .page__cart-item:last-child {
    margin-bottom: 0;
  }
  .page__cart-item td {
    padding: 0;
    border: 0 !important;
  }
  .cart-item__media {
    grid-row: 1 / 3;
  }
  .cart__page-price {
    display: none;
  }
  thead.hidden-on-small {
    display: none;
  }
  .page__cart-item
    td:not(.cart-item__media, .cart__page-price, .cart-item__totals) {
    display: block;
  }
  .cart-item__quantity {
    position: absolute;
    bottom: 2rem;
    right: 0;
  }
  .cart-item__quantity .ps-20 {
    padding-inline-start: 1rem;
  }
  .cart-item__quantity .pe-20 {
    padding-inline-end: 1rem;
  }
  .cart-item__quantity input {
    --input-height: 30px !important;
    width: 3rem;
  }
  .rounded-style .cart-item__quantity {
    --btn-radius: 3px;
  }
  .cart-item__totals {
    display: flex;
    align-items: end;
  }
  .cart_info cart-remove-button{
    position: absolute;
    right: 0;
    top: 0;
  }
  .cart_info cart-remove-button .cart-remove{
    width: 15px;
    height: 15px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    position: relative;
  }
}
body:has(cart-items.is-empty) .page-title {
  display: none;
}
