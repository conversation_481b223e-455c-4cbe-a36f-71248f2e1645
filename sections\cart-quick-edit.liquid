{%- liquid
  assign theme_st = settings
  assign option_name = 'Color'
  if theme_st.color_swatch_trigger != blank
    assign option_name = theme_st.color_swatch_trigger | split: ','
  endif
  assign show_add_cart = theme_st.show_add_cart
  assign hidden_price = theme_st.hidden_price
  assign swatch_item_type = theme_st.swatch_item_type
  assign show_quantity = theme_st.show_quantity_input
-%}
<div id="product-form-quick-edit" data-template-quick-cart-edit>
  <product-form-quick-edit class="product-form">
    {%- liquid
      assign variants_available_arr = product.variants | map: 'available'
      assign variants_option_1_arr = product.variants | map: 'option1'
      assign variants_option_2_arr = product.variants | map: 'option2'
      assign variants_option_3_arr = product.variants | map: 'option3'
      assign product_form_id = 'product-form-quick-edit-' | append: section.id
    -%}
    {%- form 'product',
      product,
      id: product_form_id,
      class: 'form',
      novalidate: 'novalidate',
      data-type: 'add-to-cart-form'
    -%}
      <input
        type="hidden"
        name="id"
        value="{{ product.selected_or_first_available_variant.id }}"
        disabled
      >
      <div
        class="product-form-quick-edit flex flex-wrap w-full gap-15 gap-sm-30 mb-30"
        style="--col-width: 58%;--col-gap-desktop: 30px;"
      >
        <div class="product-quick-edit-left flex gap-15 w-full col-sm-w-custom border-sm-inline-end pe-sm-30">
          <div class="product__media w-custom flex-auto" style="--custom-width: 10.5rem;">
            {%- assign featured_media = product.selected_or_first_available_variant.featured_media -%}
            {% if featured_media != blank %}
              {%- assign image_alt = featured_media.alt | default: 'product' -%}
              <div class="product-pickup-img">
                {% render 'responsive-image',
                  type: 'product',
                  image: featured_media,
                  image_alt: image_alt,
                  class: 'rounded-5',
                  no_animate: true
                %}
              </div>
            {% else %}
              {% assign first_image_found = false %}
              {% for media in product.media %}
                {% if media.media_type == 'image' and first_image_found == false %}
                  <div class="product-pickup-img">
                    {%- assign image_alt = media.alt | default: 'product' | escape -%}
                    {% render 'responsive-image',
                      type: 'product',
                      image: media,
                      image_alt: image_alt,
                      class: 'rounded-5',
                      no_animate: true
                    %}
                  </div>
                  {% assign first_image_found = true %}
                {% endif %}
              {% endfor %}
              {% if first_image_found == false %}
                {% render 'placeholder-render', class: 'rounded-5' %}
              {% endif %}
            {% endif %}
          </div>
          <div class="product__info flex-1 flex flex-column">
            <h3
              class="product-item__name unset heading-style no-underline fs-custom block lh-normal my-0"
              style="--font-size: {{ settings.product_size }}"
            >
              {{ product.title }}
            </h3>
            {% if hidden_price == false %}
              <div class="product__price price mb-15">
                {%- render 'price' | scope: 'detail' | product: product,  class: 'flex gap-5 mt-5' -%}
              </div>
            {% endif %}
            <div
              class="cart-item__error inline-flex align-start"
              role="alert"
            >
              <span class="cart-item__error-text"></span>
              {%- render 'icon-error' -%}
            </div>
            {%- if show_add_cart == true and theme_st.enable_catalog_mode == false and show_quantity -%}
              <div class="product-form__input mb-10 product-form__quantity grow-1 flex align-start">
                <quantity-input class="quantity rounded-3 border inline-flex text-center grey-bg">
                  <button
                    class="quantity__button pointer border-0 no-js-hidden w-custom grey-bg inline-flex content-center ps-12"
                    name="minus"
                    type="button"
                    data-id="{{- product.key -}}"
                    style="--custom-width: 3rem"
                    arial-label="minus"
                  >
                    <svg width="9" height="1" fill="none">
                      <path fill="#111" d="M9 0v1H0V0h9Z"/>
                    </svg>
                  </button>
                  <label class="visually-hidden" for="Quantity-{{ section.id }}">
                    {{ 'general.cart.headings.quantity' | t }}
                  </label>
                  <input
                    class="quantity-input bg-unset text-center appearance-none p-0-important w-custom"
                    type="number"
                    name="quantity"
                    id="Quantity-{{ section.id }}"
                    min="1"
                    value="1"
                    form="{{ product_form_id }}"
                    style="--input-padding: 0;--inputs-border-width: 0;--input-height: 33px;--custom-width: 3.8rem;--input-border-radius: 0;--input-bg: transparent"
                  >
                  <button
                    class="quantity__button pointer border-0 no-js-hidden w-custom grey-bg pe-12"
                    name="plus"
                    type="button"
                    data-id="{{- product.key -}}"
                    style="--custom-width: 3rem"
                    arial-label="plus"
                  >
                    <svg width="9" height="9" fill="none">
                      <path fill="#111" fill-rule="evenodd" d="M4 9h1V5h4V4H5V0H4v4H0v1h4v4Z" clip-rule="evenodd"/>
                    </svg>
                  </button>
                </quantity-input>
              </div>
            {% endif %}
          </div>
        </div>
        <div class="product-quick-edit-right w-full col-sm-remaining">
          {%- unless product.has_only_default_variant -%}
            <variant-radios-quick-edit
              id="variant-radios"
              class="no-js-hidden"
              data-section="{{ section.id }}"
              data-url="{{ product.url }}"
            >
              {%- for option in product.options_with_values -%}
                {%- liquid
                  assign is_color_option = false
                  for colorItem in option_name
                    if option.name == colorItem
                      assign is_color_option = true
                    endif
                  endfor
                -%}
                <fieldset class="js product-form__input flex flex-wrap mb-20 last-0 mx-0 p-0 border-0{% if is_color_option %} color gap-15{% else %} gap-15{% endif %}">
                  <legend class="form__label gap-10 w-full mb-12 lh-normal">
                    <span class="swatch-attribute-label">{{ option.name }}:</span>
                    <span class="swatch-selected-value heading-style uppercase-first-letter inline-block">
                      {{ option.selected_value }}
                    </span>
                  </legend>
                  {%- for value in option.values -%}
                    {%- liquid
                      assign option_disabled = true
                      for option1_name in variants_option_1_arr
                        case option.position
                          when 1
                            if variants_option_1_arr[forloop.index0] == value and variants_available_arr[forloop.index0]
                              assign option_disabled = false
                            endif
                          when 2
                            if option1_name == product.selected_or_first_available_variant.option1 and variants_option_2_arr[forloop.index0] == value and variants_available_arr[forloop.index0]
                              assign option_disabled = false
                            endif
                          when 3
                            if option1_name == product.selected_or_first_available_variant.option1 and variants_option_2_arr[forloop.index0] == product.selected_or_first_available_variant.option2 and variants_option_3_arr[forloop.index0] == value and variants_available_arr[forloop.index0]
                              assign option_disabled = false
                            endif
                        endcase
                      endfor
                      assign op = 'option' | append: option.position
                      assign variant = product.variants | where: op, value
                      assign img = ''
                      for i in variant
                        if i.featured_image.src
                          assign img = i.featured_image.src
                          break
                        endif
                      endfor
                      if value.swatch.image
                        assign image_url = value.swatch.image | image_url
                        assign swatch_value = 'url(' | append: image_url | append: ')'
                      elsif value.swatch.color
                        assign swatch_value = 'rgb(' | append: value.swatch.color.rgb | append: ')'
                      else
                        assign swatch_value = null
                      endif
                    -%}
                    <div
                      class="{% if is_color_option %}product__color-swatches--js product__color-swatch inline-flex align-center rounded-50 swatch-large pointer {% endif %}relative tooltip option-swatch-js{% if option.selected_value == value %} active{% endif %}{% if option_disabled %} option-disabled{% endif %}"
                      data-value="{% if is_color_option %}{{ value }}{% else %}{{ value | escape }}{% endif %}"
                      data-color="{{ value | downcase }}"
                      data-custom-value="custom__color-swatches--{{ value | downcase | replace: ' ', '-' | replace: '"', "'" }}"
                      {% if img != blank %}
                        data-image="{{ img | image_url: width: 100, height: 100 }}"
                      {% endif %}
                      {% if is_color_option %}
                        data-swatch-type="{{ swatch_item_type }}"
                        style="background-color: {{ value | downcase }};"
                      {% endif %}
                      data-option-swatch-value="{{ swatch_value }}"
                    >
                      {% if is_color_option %}
                        <label
                          class="product-swatches"
                        >
                          <span class="tooltip-content invisible rounded-3 absolute pointer-none">{{ value }}</span>
                        </label>
                      {% else %}
                        <label
                          class="product-swatches tooltip pointer product__item-option inline-flex align-center border rounded-3 subheading_weight btn-hover lh-normal px-20 py-8 transition relative {{ value | escape }}"
                        >
                          <span class="swatche-item">{{ value | escape }}</span>
                        </label>
                      {% endif %}
                    </div>
                  {%- endfor -%}
                </fieldset>
              {%- endfor -%}
              <script type="application/json">
                {{ product.variants | json }}
              </script>
            </variant-radios-quick-edit>
          {%- endunless -%}
        </div>
      </div>
      {%- if show_add_cart == true and theme_st.enable_catalog_mode == false -%}
        <button
          type="submit"
          name="add"
          class="product-form__submit relative w-full whitespace-nowrap min-height-48 animation btn-primary"
          {% if product.selected_or_first_available_variant.available == false %}
            disabled
          {% endif %}
        >
          <span class="hidden-on-load">
            {%- if product.selected_or_first_available_variant.available -%}
              {{ 'products.product.add_to_cart' | t }}
            {%- else -%}
              {{ 'products.product.sold_out' | t }}
            {% endif %}
          </span>
          <svg width="14" height="14" viewBox="0 0 14 14" fill="none" class="spin opacity-0 icon-load">
            <use href="#icon-load"></use>
          </svg>
        </button>
      {% endif %}
    {%- endform -%}
  </product-form-quick-edit>
</div>
