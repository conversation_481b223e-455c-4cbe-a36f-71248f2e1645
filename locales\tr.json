{"accessibility": {"skip_to_text": "İçeriğe atla", "refresh_page": "<PERSON><PERSON> seçimin seçilmesi tam sayfanın yenilenmesiyle sonuçlanır.", "unit_price_separator": "YAPILACAKLAR", "link_messages": {"new_window": "<PERSON>ni bir pencerede açılır."}, "error": "<PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "complementary_products": "Tamamlayıcı ürünler"}, "page_sale": {"use_code": "Kodu kullanın:"}, "recipient": {"form": {"checkbox": "<PERSON><PERSON>u hediye olarak göndermek istiyorum", "expanded": "Hediye kartı alıcı formu genişletildi", "collapsed": "Hediye kartı alıcı formu daraltıldı", "email_label": "Alıcı e-postası...", "email_label_optional_for_no_js_behavior": "Alıcı e-postası (isteğe bağlı)", "email": "E-posta", "name_label": "<PERSON><PERSON><PERSON>ı adı (isteğe bağlı)...", "name": "İsim", "message_label": "<PERSON><PERSON> (isteğe bağlı)...", "message": "<PERSON><PERSON>", "max_characters": "{{ max_chars }} maks<PERSON><PERSON> karakter", "send_on": "YYYY-AA-GG", "send_on_label": "<PERSON><PERSON><PERSON> (isteğe bağlı)"}}, "general": {"content": {"discount_code_error": "İndirim kodu sepetinize uygulanamaz", "discount_code_remove": "İndirim kodu kaldırıldı.", "discount_code_applied": "İndirim kodu uygulandı.", "discount_code_already": "İndirim kodu zaten uygulandı"}, "outfit-idea": {"view_products": "Ürünleri Görüntüle", "shop_the_look": "Görünümü Satın Alın"}, "password_page": {"login_form_heading": "<PERSON><PERSON><PERSON>i k<PERSON>anarak mağazaya girin:", "login_password_button": "<PERSON><PERSON><PERSON> girin", "login_form_password_label": "Şifre", "login_form_password_placeholder": "Şifreniz", "login_form_error": "Yanlış şifre!", "login_form_submit": "Girmek", "admin_link_html": "Mağaza sahibi misiniz? <a href=\"/admin\" class=\"link underlined-link\"><PERSON><PERSON><PERSON> giri<PERSON> ya<PERSON>ın</a>", "powered_by_shopify_html": "Bu mağaza tarafından desteklenecek {{ shopify }}"}, "contact": {"success": "Bize ulaştığınız için teşekkür ederiz! "}, "show_all": "Tümünü <PERSON>ö<PERSON>", "show_less": "<PERSON><PERSON> a<PERSON> g<PERSON>", "continue_shopping": "Alışverişe Devam Et", "social": {"links": {"twitter": "heyecan", "facebook": "Facebook", "pinterest": "Pinterest'te", "instagram": "instagram", "tumblr": "Tumblr", "snapchat": "Snapchat'te", "youtube": "YouTube", "vimeo": "Vimeo", "tiktok": "TikTok", "linkedin": "LinkedIn", "whatsapp": "Whatsapp"}}, "pagination": {"label": "Sayfalandırma", "page": "<PERSON><PERSON> {{ number }}", "next": "<PERSON><PERSON><PERSON>", "previous": "<PERSON><PERSON><PERSON>", "load_more": "<PERSON>ha fazlasını yükle", "result": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {{ amount }} ile ilgili {{ count }} sonuç"}, "breadcrumb": {"home": "Ev"}, "placeholder": {"label": "Resim yok"}, "search": {"search": "<PERSON><PERSON><PERSON><PERSON><PERSON>…", "more_results": "<PERSON><PERSON><PERSON>ları Görüntüle", "quick_search": "Trend <PERSON>", "all_categories": "<PERSON><PERSON><PERSON>", "popular_products": "<PERSON><PERSON><PERSON>", "view_all": "<PERSON><PERSON><PERSON>ları Görüntüle", "results_with_count": {"one": "Kurmak {{ count }} \" i<PERSON><PERSON> son<PERSON>{{ terms }}\"", "other": "Kurmak {{ count }} \" i<PERSON><PERSON>{{ terms }}\""}, "title": "Mağazamızı arayın"}, "cart": {"label": "Sepet", "view": "<PERSON><PERSON><PERSON> ({{ count }})", "item_added": "Ürün sepetinize eklendi", "title": "Alışveriş Sepeti", "cart_edit": "Seçeneği Düzenle", "remove_title": "Kaldırmak", "close": "<PERSON><PERSON><PERSON>", "subtitle": "Sepetiniz şu anda boş!.", "empty": "Sepetiniz boş", "description": "Mevcut tüm ürünleri inceleyebilir ve mağazadan satın alabilirsiniz.", "timeout_message": "Zamanınız doldu! ", "countdown_message_html": "<PERSON><PERSON><PERSON><PERSON><PERSON> sını<PERSON>ı<PERSON>ır, ödeme süresi içinde", "countdown_cart_message_html": "Lütfen acele edin!  {{ html }}", "cart_thres1_html": "üzeri tüm siparişlerde Ücretsiz Kargo <span class=\"price\">{{ price }}</span>", "cart_thres2_html": "Harcamak <span class=\"price\">{{ price }}</span> daha fazla keyif almak <span class=\"subheading_weight primary-color\">Ücretsiz kargo!</span>", "cart_thres3_html": "<span class=\"congratulations\">Tebrikler!</span> Ücretsiz kargo fırsatınız var!", "free_shipping": "Harcamak <span class=\"price\">{{ amount }}</span> daha fazla keyif almak <span class=\"subheading_weight primary-color\">Ücretsiz kargo!</span>", "free_shipping_avaiable": "Tebrikler! ", "terms_conditions_text": "Hizmet Şartları", "taxes_and_shipping_policy_at_checkout_html": "Vergiler ve <a href=\"{{ link }}\">Nakliye</a> ve ödeme sırasında hesaplanan indirimler", "taxes_included_but_shipping_at_checkout": "<PERSON>ergi da<PERSON> ve gönderim bedeli ödeme sırasında hesaplanır", "taxes_included_and_shipping_policy_html": "<PERSON><PERSON><PERSON> da<PERSON>. <a href=\"{{ link }}\">Nakliye</a> ve ödeme sırasında hesaplanan indirimler.", "taxes_and_shipping_at_checkout": "<PERSON>deme sı<PERSON>ında hesaplanan vergiler ve gönderim bedeli.", "return_shop": "Alışverişe Devam Et", "remove": "<PERSON><PERSON> öğ<PERSON> kaldır", "edit": "<PERSON><PERSON><PERSON> d<PERSON>", "subtotal": "<PERSON>", "viewcart": "Sepeti Görüntüle", "checkout": "Çık<PERSON>ş yapmak", "save": "<PERSON><PERSON>me<PERSON>", "cancel": "İptal etmek", "heading_payment": "<PERSON><PERSON><PERSON>", "heading_delivery": "Teslimat Bilgileri", "heading_guarantee": "30 <PERSON><PERSON><PERSON>", "note": {"title": "Sipariş Notu Ekle", "placeholder": "Size nasıl yardımcı olabiliriz?"}, "gift": {"title": "<PERSON><PERSON><PERSON>", "gift_wrap_html": "Lütfen ürünü dikkatlice sarın.  <span class=\"price heading-style\">{{ price }}</span>. ", "button_text": "Hediye Paketi <PERSON>"}, "shipping": {"title": "<PERSON><PERSON><PERSON>", "estimate_shipping_title": "<PERSON><PERSON><PERSON>", "estimate_shipping_button": "<PERSON><PERSON><PERSON>", "cart_estimate_shipping_button": "<PERSON><PERSON><PERSON>"}, "coupon": {"title": "<PERSON><PERSON><PERSON>", "enter_discount_code": "<PERSON><PERSON><PERSON> kodu"}, "headings": {"product": "<PERSON><PERSON><PERSON><PERSON>", "price": "<PERSON><PERSON><PERSON>", "total": "Toplam", "quantity": "<PERSON><PERSON><PERSON>", "image": "<PERSON><PERSON><PERSON><PERSON>i"}, "delivery_days": {"one": "g<PERSON>n", "other": "<PERSON><PERSON><PERSON><PERSON>"}, "login": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>z var mı?", "paragraph_html": "<a href=\"{{ link }}\" class=\"link underlined-link\"><PERSON><PERSON><PERSON></a> <PERSON>ha hızlı kontrol etmek için."}}, "page_cart": {"checkout": "Çık<PERSON>ş yapmak"}, "collections": {"label": "Koleksiyon", "collection_count_multiple": "{{ number }} <PERSON><PERSON><PERSON><PERSON><PERSON>", "collection_count_single": "{{ number }} <PERSON><PERSON><PERSON><PERSON>", "collection_count_multiple_noP": "({{ number }})", "collection_count_single_noP": "({{ number }})", "collection_count_multiple_noCP": "{{ number }}", "collection_count_single_noCP": "{{ number }}"}, "product": {"view_detail": "Ayrıntıları görüntüle", "size_guide": "<PERSON><PERSON>"}, "hotspot": {"dot": "Noktalar", "plus": "Artı"}, "banner": "A<PERSON>ş", "view_all": "<PERSON>ümü<PERSON><PERSON>", "policies_html": "katılıyorum <a href=\"/policies/privacy-policy\" target=\"_blank\" title=\"Privacy Policy\"><strong>Gizlilik Politikası</strong></a> web sitesinin."}, "blogs": {"article": {"blog": "Blog", "tags": "<PERSON><PERSON><PERSON><PERSON>", "by": "<PERSON><PERSON> {{ author }}", "read_more_title": "Devamını oku: {{ title }}", "comments": {"one": "{{ count }} <PERSON><PERSON>", "other": "{{ count }} <PERSON><PERSON><PERSON>"}, "sharing": {"share": "Paylaşmak", "twitter": "heyecan", "facebook": "Facebook", "pinterest": "Pinterest'te"}, "previous_post": "Önceki yazı", "next_post": "<PERSON><PERSON><PERSON>", "moderated": "Lütfen yorumların yayınlanmadan önce onaylanması gerektiğini unutmayın.", "comment_form_title": "<PERSON><PERSON> b<PERSON>n", "info": "E-posta adresiniz yayımlanmayacaktır. ", "name": "Adınız*", "email": "E-postanız*", "message": "Yorumunuz*", "post": "<PERSON><PERSON>", "back_to_blog": "<PERSON><PERSON><PERSON> geri <PERSON>n", "share": "<PERSON><PERSON> makal<PERSON>i <PERSON>", "empty": "Seç<PERSON>le eşleşen gönderileri bulamıyoruz.", "success": "Yorumunuz gönderildi. ", "success_moderated": "Yorumunuz başarıyla gönderildi. ", "label_all": "<PERSON><PERSON><PERSON>"}}, "collections": {"sidebar": {"clear_all": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> temizle", "selected": "Seçildi", "clear": "Temizlemek", "apply": "<PERSON><PERSON><PERSON><PERSON>"}, "pagination": {"load_more": "<PERSON><PERSON>", "load_more_amount": "Gösteriliyor {{ amount }} ile ilgili {{ count }} <PERSON><PERSON><PERSON><PERSON><PERSON>"}, "toolbar": {"filter": "Filtre", "progress_bar": {"list": "Liste", "grid": "Izgara", "columns": "{{ amount }} <PERSON><PERSON><PERSON><PERSON>"}}}, "subscribe": {"label": "E-postanızı girin", "success": "Abone olduğunuz için teşekkürler", "button_label": "<PERSON><PERSON>", "first_name": "İlk adı", "last_name": "<PERSON>y isim"}, "newsletter": {"label": "E-posta", "success": "Abone olduğunuz için teşekkürler", "error": "Geçersiz e-posta veya zaten abone olunmuş", "button_label": "<PERSON><PERSON>"}, "templates": {"search": {"no_results": "\"\" terimi bulunamadı{{ terms }}”. ", "page": "Say<PERSON>", "products": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tooltip": "Aramak", "no_products_found": "<PERSON><PERSON><PERSON><PERSON> bulunamadı", "use_fewer_filters_html": "<PERSON>ha az filtre kullanın veya <a class=\"{{ class }}\" href=\"{{ link }}\">he<PERSON><PERSON> kaldır</a>", "results_with_count": {"one": "{{ count }} sonuç", "other": "{{ count }} sonu<PERSON>lar"}, "results_with_count_and_term": {"none": "\"\" terimi bulunamadı{{ terms }}”", "one": "{{ count }} \" i<PERSON><PERSON> bulunan sonuç{{ terms }}”", "other": "{{ count }} \" i<PERSON><PERSON> bulunan son<PERSON>{{ terms }}”"}, "title_no_search": "Aramak", "title": "Sitemizde Arayın", "search_for_html": "“ i<PERSON>in ara<span class=\"heading-color\">{{ terms }}</span>”", "search_empty_html": "Aramanızla eşleşen hiçbir şey yok \"<span>{{ terms }}</span>”", "suggestions": "<PERSON><PERSON><PERSON>", "pages": "Say<PERSON>", "article": "<PERSON><PERSON>"}, "rvp": {"title": "<PERSON>", "rvp": "<PERSON><PERSON>", "no_product": "Son gö<PERSON><PERSON><PERSON><PERSON><PERSON>n sayfada hiçbir ürün yoktu.", "redirect": "Alışverişe geri dön"}, "wishlist": {"wishlist": "İstek listesi", "no_product": "İstek listeniz boş", "empty_des": "İstek listesi sayfasına hiçbir ürün eklenmedi.", "redirect": "Alışverişe geri dön", "title_remove": "Kaldırmadan önce istek listesine eklensin mi?", "action_yes": "<PERSON><PERSON>", "action_no": "HAYIR"}, "compare": {"no_product": "Karşılaş<PERSON><PERSON><PERSON><PERSON><PERSON> boş", "empty_des": "Ka<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sayfasına ürün eklenmedi.", "redirect": "Alışverişe geri dön"}, "recently_viewed": {"recently_viewed_products": "<PERSON> g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ürü<PERSON>ler"}, "contact": {"form": {"title": "İletişim formu", "name": "Adınız*", "email": "E-postanız *", "phone": "Telefon numaranız", "comment": "Mesajınız*", "send": "<PERSON><PERSON><PERSON>", "note": "* Zorunlu alanlar boş bırakılamaz.", "send_contact": "Mesajınızı Gönderin", "post_success": "Bizimle iletişime geçtiğiniz için teşekkür ederiz. ", "error_heading": "Lütfen aşağıdakileri ayarlayın:"}}}, "main_menu": {"horizontal": {"close": "<PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON>"}, "vertical": {"title": "<PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "more": "<PERSON><PERSON><PERSON>", "hide": "<PERSON><PERSON><PERSON><PERSON>"}, "categories": {"close": "<PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON>"}, "currency": {"title": "Para birimi"}, "language": {"title": "Dil"}}, "customer": {"account": {"my_account": "He<PERSON>b<PERSON>m", "title": "<PERSON><PERSON><PERSON>", "dashboard": "Ko<PERSON>rol <PERSON>i", "details": "<PERSON>sap <PERSON>", "view_addresses": "Adresleri görü<PERSON>üle", "your_addresses": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "your_wishlist": "İstek Listeniz", "return": "<PERSON><PERSON><PERSON>", "welcome": "<PERSON><PERSON> geldin", "no": "Olumsuz?", "required": "*zorunlu alandır"}, "account_fallback": "<PERSON><PERSON><PERSON>", "activate_account": {"title": "Hesabı etkinleştir", "subtext": "Hesabınızı etkinleştirmek için şifrenizi oluşturun.", "password": "Şifre", "password_confirm": "<PERSON><PERSON><PERSON><PERSON>", "submit": "Hesabı etkinleştir", "cancel": "<PERSON><PERSON>", "or": "veya"}, "addresses": {"title": "<PERSON><PERSON><PERSON>", "default": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "add_new": "<PERSON><PERSON> bir adres ekle", "edit_address": "<PERSON><PERSON><PERSON>", "first_name": "İlk adı", "last_name": "<PERSON>y isim", "company": "Şirket", "address1": "Adres 1", "address2": "Adres 2", "city": "Şehir", "country": "<PERSON><PERSON><PERSON>/bölge", "province": "Vilayet", "zip": "Posta/Posta kodu", "phone": "Telefon", "set_default": "Varsayılan adres o<PERSON> a<PERSON>", "add": "<PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "cancel": "İptal etmek", "edit": "D<PERSON><PERSON>lemek", "delete": "<PERSON><PERSON><PERSON>", "delete_confirm": "Bu adresi silmek istediğinizden emin misiniz?", "name": "İsim", "email": "E-posta"}, "log_in": "<PERSON><PERSON><PERSON>", "log_out": "<PERSON><PERSON><PERSON><PERSON>", "login_menu_mobile": "G<PERSON>ş yap / <PERSON><PERSON><PERSON> ol", "login_page": {"cancel": "İptal etmek", "create_account": "<PERSON><PERSON><PERSON>", "create_account_info": "Hesap oluşturmak için lütfen aşağıdan ka<PERSON>.", "email": "E-posta", "forgot_password": "Şifrenizi mi unuttunuz?", "forgot_password_info": "Şifrenizi sıfırlamak için bir bağlantı almak için lütfen aşağıdaki alana e-posta adresinizi girin.", "guest_continue": "<PERSON><PERSON>", "guest_title": "<PERSON><PERSON><PERSON> o<PERSON> de<PERSON> et", "password": "Şifre", "title": "<PERSON><PERSON><PERSON>", "sign_in": "<PERSON><PERSON><PERSON> olmak", "sign_in_info": "Oturum açmak için lütfen bilgilerinizi aşağıya girin.", "submit": "<PERSON><PERSON><PERSON> olmak", "placeholder_email": "E-postanız*", "placeholder_pass": "Şifre*"}, "order": {"title": "Emir {{ name }}", "date_html": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {{ date }}", "cancelled_html": "Sipariş İptal Edildi {{ date }}", "cancelled_reason": "Sebep: {{ reason }}", "billing_address": "<PERSON><PERSON>", "payment_status": "<PERSON><PERSON><PERSON>", "shipping_address": "<PERSON><PERSON><PERSON><PERSON>", "fulfillment_status": "<PERSON><PERSON>", "discount": "İndirim", "shipping": "Nakliye", "tax": "<PERSON><PERSON><PERSON>", "product": "<PERSON><PERSON><PERSON><PERSON>", "sku": "SKU", "price": "<PERSON><PERSON><PERSON>", "quantity": "<PERSON><PERSON><PERSON>", "total": "Toplam", "fulfilled_at_html": "<PERSON><PERSON> {{ date }}", "track_shipment": "Gönderiyi takip edin", "tracking_url": "İzleme bağlantısı", "tracking_company": "Taşıyıcı", "tracking_number": "Takip numa<PERSON>ı", "subtotal": "<PERSON>", "total_duties": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "orders": {"title": "Sipariş geçmişi", "order_number": "<PERSON>ir", "order_number_link": "Sipariş numarası {{ number }}", "date": "<PERSON><PERSON><PERSON>", "payment_status": "<PERSON><PERSON><PERSON>", "fulfillment_status": "<PERSON><PERSON> du<PERSON>u", "total": "Toplam", "make": "İlk siparişinizi verin.", "none": "Hen<PERSON>z sipariş vermediniz."}, "recover_password": {"title": "Parolanızı mı unuttunuz", "email": "E-posta", "submit": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "subtext": "Şifrenizi mi kaybettiniz? ", "success": "Size şifrenizi güncellemeniz için bir bağlantı içeren bir e-posta gönderdik."}, "register": {"title": "<PERSON><PERSON><PERSON>", "first_name": "İlk adı", "last_name": "<PERSON>y isim", "email": "E-posta", "password": "Şifre", "submit": "<PERSON><PERSON><PERSON>", "title_content": "<PERSON><PERSON>", "content": "<PERSON><PERSON><PERSON> erişiminin yanı sıra size özel yeni <PERSON>, trendler ve promosyonlar için kaydolun. ", "content_form_html": "Kişisel verileriniz bu web sitesindeki deneyiminizi desteklemek, hesabınıza erişimi yönetmek ve Şartnamemizde açıklanan diğer amaçlar için kullanılacaktır. <a href=\"{{ link }}\">gizlilik politikası.</a>"}, "reset_password": {"title": "Hesap <PERSON> sıfırla", "subtext": "<PERSON><PERSON> bir <PERSON><PERSON><PERSON> girin", "password": "Şifre", "password_confirm": "<PERSON><PERSON><PERSON><PERSON>", "submit": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>"}}, "gift_cards": {"issued": {"title": "İşte senin {{ value }} için hediye kartı {{ shop }}!", "subtext": "Hediye kartınız", "gift_card_code": "Hediye kartı kodu", "shop_link": "Alışverişe Devam Et", "remaining_html": "<PERSON><PERSON><PERSON> ka<PERSON> {{ balance }}", "add_to_apple_wallet": "Apple Wallet'a ekle", "qr_image_alt": "QR kodu — hediye kartını kullanmak için tarayın", "copy_code": "Kodu k<PERSON>ala", "expired": "Günü geçmiş", "copy_code_success": "Kod başarıyla kopyalandı", "print_gift_card": "Yazdır", "expiration_date": "Son kullanma tarihi {{ expires_on }}"}}, "onboarding": {"collection_description": "Koleksiyonunuzun ayrıntılarının kısa bir açıklamasını sağlamak için bu bölümü kullanın", "product_title_example": "Örnek ürün başlığı", "collection_title": "Koleksiyon başlığı", "collection_count": "0 Ürün", "default_description": "Ürününüzün ayrıntılarının kısa bir açıklamasını sağlamak için bu bölümü kullanın. "}, "products": {"price": {"from_price_html": "İtibaren {{ price }}", "regular_price": "Normal fiyat", "sale_price": "Satış fiyatı", "unit_price": "<PERSON><PERSON><PERSON>ı"}, "product": {"label_set": "Stil #{{ index }}", "review_app": "Müşteri Yorumları", "shop_now": "Şimdi alışveriş yap", "addSuccess": "Sepeti başarıyla gü<PERSON>lle", "addGiftCard": "Hediye kartını başarıyla ekleyin", "removeCartItem": "Öğ<PERSON>i başarıyla kaldır", "loading": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "label": {"sale_label": "Satış", "new_label": "<PERSON><PERSON>", "subscription": "Abonelik"}, "price": {"from_price_html": "İtibaren {{ price }}", "regular_price": "Normal fiyat", "sale_price": "Satış fiyatı", "unit_price": "<PERSON><PERSON><PERSON>ı", "unit_price_separator": "ba<PERSON><PERSON>na"}, "actions": {"quickview": {"label": "Hızlı görünüm"}, "select_options": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "wishlist": {"add": "İstek listesine ekle", "remove": "İstek listesinden kaldır", "redirect": "İstek listesine göz atın"}, "compare": {"add": "Karşılaştırmak", "remove": "Ka<PERSON><PERSON><PERSON>laş<PERSON><PERSON><PERSON><PERSON> kaldır", "redirect": "Göz at karşılaştır"}, "add_to_cart": {"default": {"label": "Sepete ekle"}, "sold_out": {"label": "Stoklar tükendi"}}, "property": {"custom_text": "<PERSON><PERSON> metin", "custom_image": "<PERSON><PERSON> resim"}, "add_to_bundle": {"label_bundle": "<PERSON><PERSON>", "label_bundle_added": "Pakete eklendi", "label_add_all": "<PERSON>üm Sep<PERSON>"}}, "bought_together": {"add_cart": "He<PERSON>ini Sepete Ekle", "text": "Sık sık birlikte satın alınır", "total": "Toplam Fiyat", "save": "<PERSON><PERSON><PERSON><PERSON>"}, "general": {"sku": "SKU", "available": "Mevcut", "collections": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tags": "<PERSON><PERSON><PERSON><PERSON>", "vendor": "Satıcı", "type": "Tip", "view_full_details": "Tüm Ayrıntıları Görüntüle", "instock": "<PERSON><PERSON><PERSON> var", "outstock": "Stoklar tükendi", "pre_order": "<PERSON><PERSON>", "add_to_cart": "Sepete ekle"}, "quantity": {"label": "<PERSON><PERSON><PERSON>", "input_label": "Miktar {{ product }}", "increase": "<PERSON><PERSON><PERSON> için miktarı artırın: {{ product }}", "decrease": "<PERSON><PERSON>un için miktarı azalt: {{ product }}", "minimum_of": "Minimum {{ quantity }}", "maximum_of": "Ma<PERSON><PERSON>um {{ quantity }}", "multiples_of": "Artışlar {{ quantity }}", "in_cart_html": "<span class=\"quantity-cart\">{{ quantity }}</span> sepette"}, "countdown": {"days": "<PERSON><PERSON><PERSON><PERSON>", "hours": "saat", "mins": "<PERSON><PERSON><PERSON>", "secs": "saniye", "day": "D", "hour": "H", "min": "M", "sec": "S"}, "addons": {"compare_colors": "<PERSON><PERSON><PERSON>", "ask_question": "Bir soru sor", "share": {"share": "Paylaşmak", "share_popup": "Paylaşmak:", "copy_link": "Bağlantıyı kopyala"}, "compare_colors_header": "<PERSON><PERSON>", "ask_question_header": "<PERSON><PERSON>"}, "pickup_availability": {"view_store_info": "Mağaza bilgilerini görü<PERSON>üle", "check_other_stores": "<PERSON><PERSON><PERSON>alardaki stok durumunu kontrol edin", "pick_up_available": "<PERSON><PERSON><PERSON> alma mevcut", "pick_up_available_at_html": "Teslim alma şu adreste mevcuttur: <span class=\"color-foreground bold heading-color\">{{ location_name }}. </span>", "pick_up_unavailable_at_html": "Teslim alma şu anda mevcut değil <span class=\"color-foreground bold heading-color\">{{ location_name }}. </span>", "unavailable": "Teslim alma kullanılabilirliği yüklenemedi", "refresh": "<PERSON><PERSON><PERSON>", "pickup_location": "Google haritasında göster", "address": "<PERSON><PERSON>", "city": "Şehir", "country": "<PERSON><PERSON><PERSON>/bölge", "phone": "Telefon"}, "model_size_title": {"model_size": "<PERSON><PERSON>:", "model_height": "Yükseklik:", "model_weight": "Ağırlık:", "model_shoulder_width": "<PERSON><PERSON><PERSON> g<PERSON>ği:", "model_bust_waist_hips": "Göğüs/bel/kalça:"}, "inventory_status": {"incoming": "<PERSON><PERSON><PERSON><PERSON> ha<PERSON> stok", "incoming_with_date": "Stok şu tarihte gelecek: {{ date }}"}, "add_cart_error_qty": "Lütfen ürün miktarını seçiniz!", "buy_it_now": "<PERSON><PERSON><PERSON> al", "barcode": "Barkod", "instock": "<PERSON><PERSON><PERSON> var", "outstock": "Stokta yok", "save": "<PERSON><PERSON>me<PERSON>", "sale": "{{discount}}% kapalı", "value_unavailable": "{{ option_value }} - Mev<PERSON> de<PERSON>il", "sold_out": "Stoklar tükendi", "pre_order": "<PERSON><PERSON>", "unavailable": "Kullanılamıyor", "add_to_cart": "Sepete ekle", "fake_sold": "{{ sold }} en son satıldı {{ hours }} saat", "xr_button": "Alanınızda görüntüleyin", "xr_button_label": "Alanınızda görü<PERSON><PERSON>in, öğeyi artırılmış gerçeklik penceresinde yükler", "product_title": "<PERSON><PERSON><PERSON><PERSON>", "product_compare": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "back_to_product": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> geri dön", "availability": "Kullanılabilirlik", "available": "Mevcut", "vendor": "Satıcı", "tags": "<PERSON><PERSON><PERSON><PERSON>", "review": "Gözden geçirmek", "sku": "SKU", "type": "Tip", "collections": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "size": "<PERSON><PERSON>", "color": "Renk"}, "facets": {"product_count": {"one": "{{ product_count }} ile ilgili {{ count }} ü<PERSON><PERSON>n", "other": "{{ product_count }} ile ilgili {{ count }} <PERSON><PERSON><PERSON><PERSON><PERSON>"}, "product_count_simple": {"one": "Var {{ count }} toplam sonuçlar", "other": "Var {{ count }} toplam sonuçlar"}, "sort_by_label": "<PERSON><PERSON><PERSON> s<PERSON>:"}}, "sections": {"times_bn": {"days": "<PERSON><PERSON><PERSON><PERSON>", "hour": "saat", "mins": "<PERSON><PERSON><PERSON>", "secs": "saniye"}, "header": {"announcement": "<PERSON><PERSON><PERSON>", "menu": "<PERSON><PERSON>", "cart_count": {"one": "{{ count }} <PERSON><PERSON><PERSON>", "other": "{{ count }} <PERSON><PERSON><PERSON><PERSON>"}}, "testimonial": {"alt": "Referans", "verified_buyer": "Doğrulanmış Alıcı"}, "brand_logo": {"alt": "Marka logosu"}, "button_comparison": {"label": "Karşılaştırmak", "before": "önce", "after": "<PERSON><PERSON><PERSON><PERSON>"}, "cart": {"cart_error": "Sepetiniz güncellenirken bir hata oluştu. ", "no_shipping": "<PERSON>u adrese g<PERSON><PERSON>.", "shipping_rate": "<PERSON><PERSON><PERSON> i<PERSON>: {{address}}", "quick_edit": "<PERSON>üzenleme se<PERSON>ği", "cart_quantity_error_html": "Yalnızca ekleyebilirsiniz {{ quantity }} Bu ürünün sepetinize."}, "collection_tab": {"tab_header_select": "sen içeridesin"}, "instagram": {"label": "instagram"}, "related_products": {"no_product": "İlgili ürün bulunamadı"}, "recently-viewed-products": {"no_product": "Yakın zamanda görüntülenen ürün bulunamadı"}, "collection_list": {"view_all": "<PERSON><PERSON>m koleksiyonları görün", "sample_name": "Koleksiyon başlığı", "item": "<PERSON><PERSON><PERSON>", "items": "<PERSON><PERSON><PERSON>r", "collection_list_image": "Koleksiyon listesi resmi"}, "collection_template": {"empty": "<PERSON><PERSON><PERSON><PERSON> bulunamadı", "title": "Koleksiyon", "use_fewer_filters_html": " <PERSON>ha az filtre kullanın veya <a class=\"{{ class }}\" href=\"{{ link }}\">he<PERSON><PERSON> kaldır</a>"}, "shopable_video": {"countdown_message": "<PERSON><PERSON> etmek! "}, "video_with_text_overlay": {"alt": "Metin yer paylaşımlı video"}}, "section": {"google_map": {"no_iframe": "<PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON> kullanmak için iframe harita kodunu girin."}}, "blog_post": {"view_all": "<PERSON>ümü<PERSON><PERSON>", "category": "<PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON>", "short_content": "Bunlar hayatınızı kolaylaştıran insanlardır. ", "date": "13 Ekim 2022", "post_by": "<PERSON><PERSON><PERSON><PERSON>", "author": "Blueskytech", "read_more": "Devamını oku"}, "popup": {"do_not": "<PERSON><PERSON><PERSON>, teşekkürler! ", "copy": "<PERSON><PERSON><PERSON> kodunu kopyala", "copied": "Kopyalandı"}, "newsletter_popup": {"do_not_show_again": "<PERSON><PERSON><PERSON>, teşekkürler! "}, "mobile_navbar": {"shop": "Mağaza", "homepage": "Ev", "account": "<PERSON><PERSON><PERSON>", "cart": "Sepet", "wishlist": "İstek listesi"}, "rich_text": {"see_all": "<PERSON><PERSON><PERSON> Bilgileri Gör", "hide_less": "<PERSON>ha az bilgiyi gizle"}, "fake_order": {"purchased": "<PERSON><PERSON>n alındı", "time_minutes": "<PERSON><PERSON><PERSON>", "product_name": "Blueskytechco'dan <PERSON>"}}