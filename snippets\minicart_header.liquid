
<div class="minicart">
  <div>
    <div
      id="header__minicart"
      class="header__minicart {% if type == 'header_mega' %}header__minicart--mega{% endif %} {{ action_after_add_cart }}{% if action_after_add_cart == 'show_popup' %} relative{% endif %}"
    >
      <a
        id="cart-icon-bubble"
        href="{{ routes.cart_url }}"
        arial-label="{{ 'general.cart.label' | t }}"
        class="no-underline {% if template != 'cart' and action_after_add_cart != 'go_to_cart_page' %}minicart__action{% endif %} header-icon header-color w-44 h-44 inline-flex content-center  {% if action_after_add_cart == 'show_popup' %}show-overlay{% else %}overlayminicart{% endif %}"
      >
        <h-count class="relative lh-1 fs-0">
          {% if type != 'header_mega' %}
            <svg width="15" height="16" viewBox="0 0 15 16" fill="none">
              <path d="M12.7518 2.91374L11.6276 1.44835H3.37237L2.24824 2.91374H12.7518ZM14.8419 3.18637V3.20341C14.8419 3.20341 14.8478 3.20341 14.8595 3.20341C14.8946 3.27157 14.9239 3.33972 14.9473 3.40788C14.9824 3.47604 15 3.54988 15 3.62939V13.819C15 14.1143 14.9356 14.3983 14.8068 14.6709C14.6897 14.9322 14.5316 15.1594 14.3326 15.3525C14.1335 15.557 13.8934 15.716 13.6124 15.8296C13.3431 15.9432 13.0562 16 12.7518 16H2.24824C1.94379 16 1.65105 15.9375 1.37002 15.8126C1.1007 15.699 0.866511 15.5456 0.667447 15.3525C0.456674 15.1594 0.29274 14.9322 0.175644 14.6709C0.058548 14.3983 0 14.1143 0 13.819V3.62939C0 3.58395 0.0058548 3.53852 0.0175644 3.49308C0.029274 3.43628 0.0468384 3.37948 0.0702576 3.32268C0.0819672 3.29996 0.0936768 3.27725 0.105386 3.25453C0.128806 3.23181 0.14637 3.20909 0.15808 3.18637L2.40632 0.28967C2.47658 0.198793 2.5644 0.130635 2.66979 0.085197C2.77518 0.028399 2.88642 0 3.00351 0H11.9965C12.1136 0 12.2248 0.028399 12.3302 0.085197C12.4356 0.130635 12.5234 0.198793 12.5937 0.28967L14.8419 3.18637ZM1.51054 4.36209V13.819C1.51054 13.9098 1.5281 14.0007 1.56323 14.0916C1.59836 14.1825 1.65105 14.262 1.72131 14.3301C1.79157 14.3983 1.87354 14.4494 1.96721 14.4835C2.06089 14.5176 2.15457 14.5346 2.24824 14.5346H12.7518C12.8571 14.5346 12.9508 14.5176 13.0328 14.4835C13.1265 14.4494 13.2084 14.3983 13.2787 14.3301C13.3489 14.262 13.4016 14.1825 13.4368 14.0916C13.4719 14.0007 13.4895 13.9098 13.4895 13.819V4.36209H1.51054ZM9.74824 6.54313C9.74824 6.33866 9.8185 6.16826 9.95902 6.03195C10.1112 5.88427 10.2927 5.81044 10.5035 5.81044C10.7026 5.81044 10.8724 5.88427 11.0129 6.03195C11.1651 6.16826 11.2412 6.33866 11.2412 6.54313C11.2412 7.04295 11.1417 7.51438 10.9426 7.9574C10.7553 8.40043 10.4918 8.78665 10.1522 9.11608C9.81265 9.44551 9.41452 9.70678 8.95785 9.89989C8.50117 10.0816 8.01522 10.1725 7.5 10.1725C6.98478 10.1725 6.49883 10.0816 6.04215 9.89989C5.58548 9.70678 5.18735 9.44551 4.84777 9.11608C4.5082 8.78665 4.23888 8.40043 4.03981 7.9574C3.85246 7.51438 3.75878 7.04295 3.75878 6.54313C3.75878 6.33866 3.82904 6.16826 3.96956 6.03195C4.12178 5.88427 4.29742 5.81044 4.49649 5.81044C4.70726 5.81044 4.8829 5.88427 5.02342 6.03195C5.17564 6.16826 5.25176 6.33866 5.25176 6.54313C5.25176 6.84984 5.3103 7.13383 5.4274 7.3951C5.5445 7.65637 5.70843 7.88356 5.9192 8.07668C6.11827 8.28115 6.35246 8.44018 6.62178 8.55378C6.8911 8.66738 7.18384 8.72417 7.5 8.72417C7.81616 8.72417 8.1089 8.66738 8.37822 8.55378C8.64754 8.44018 8.88173 8.28115 9.0808 8.07668C9.29157 7.88356 9.4555 7.65637 9.5726 7.3951C9.6897 7.13383 9.74824 6.84984 9.74824 6.54313Z" fill="currentColor"/>
            </svg>
          {% else %}
            <svg xmlns="http://www.w3.org/2000/svg" width="27" height="24" viewBox="0 0 27 24" fill="none">
              <path d="M19.3797 21.2192C19.0844 21.2192 18.8011 21.1017 18.5923 20.8924C18.3835 20.6832 18.2662 20.3994 18.2662 20.1035H16.0391C16.0391 20.9912 16.391 21.8426 17.0175 22.4703C17.644 23.098 18.4937 23.4506 19.3797 23.4506C20.2657 23.4506 21.1154 23.098 21.7419 22.4703C22.3684 21.8426 22.7203 20.9912 22.7203 20.1035H20.4932C20.4932 20.3994 20.3759 20.6832 20.1671 20.8924C19.9583 21.1017 19.675 21.2192 19.3797 21.2192Z" fill="currentColor"/>
              <path d="M11.5425 21.2192C11.2472 21.2192 10.9639 21.1017 10.7551 20.8924C10.5463 20.6832 10.4289 20.3994 10.4289 20.1035H8.19141C8.19141 20.5437 8.27794 20.9796 8.44607 21.3863C8.6142 21.7929 8.86062 22.1625 9.17128 22.4737C9.48194 22.785 9.85075 23.0319 10.2566 23.2003C10.6625 23.3688 11.0976 23.4555 11.5369 23.4555C11.9763 23.4555 12.4113 23.3688 12.8172 23.2003C13.2231 23.0319 13.5919 22.785 13.9025 22.4737C14.2132 22.1625 14.4596 21.7929 14.6278 21.3863C14.7959 20.9796 14.8824 20.5437 14.8824 20.1035H12.6553C12.6553 20.3993 12.5381 20.683 12.3294 20.8922C12.1207 21.1014 11.8377 21.219 11.5425 21.2192Z" fill="currentColor"/>
              <path d="M26.0699 2.76057C26.0092 2.75022 25.9477 2.74509 25.8862 2.74523H8.06945C7.77412 2.74523 7.49089 2.86278 7.28205 3.07201C7.07322 3.28124 6.95591 3.56502 6.95591 3.86092C6.95591 4.15682 7.07322 4.4406 7.28205 4.64984C7.49089 4.85907 7.77412 4.97662 8.06945 4.97662H24.5715L24.2771 6.74638L22.7161 16.1335H8.18985L4.4038 6.74638L2.14539 1.19581C2.02596 0.934695 1.81062 0.729787 1.54419 0.623743C1.27777 0.5177 0.980789 0.518691 0.715076 0.626511C0.449363 0.734331 0.23539 0.940672 0.117698 1.20258C5.8841e-06 1.46449 -0.012336 1.76179 0.0832412 2.03258L3.68904 10.8975L6.14998 17.4326C6.33162 17.9828 6.70675 18.3649 7.24821 18.3649H23.6591C23.9228 18.3651 24.178 18.2714 24.3792 18.1007C24.5804 17.93 24.7147 17.6932 24.758 17.4326L26.5355 6.74638L26.9851 4.04431C27.0336 3.75249 26.9644 3.45331 26.7928 3.21257C26.6212 2.97184 26.3612 2.80925 26.0699 2.76057Z" fill="currentColor"/>
            </svg>
          {% endif %}
          <span class="absolute lh-1 cart-count h-count inline-flex align-center justify-content-center rounded-50 overflow-hidden fs-10">
            {%- if cart.item_count < 100 -%}
              {{ cart.item_count }}
            {%- else -%}
              ~
            {% endif %}
            <span class="visually-hidden">{{ 'sections.header.cart_count' | t: count: cart.item_count }}</span>
          </span>
        </h-count>
        {% if type == 'header_mega' %}
          <header-total-price>
            <span class="text-icons  d-inline-flex fs-11 fw-500 ml-15 text-left d-none-mb">
              <span class="text-icons-top">{{ 'general.cart.label' | t }}</span>
              <span class="fs-14 menu-cart" data-cart-subtotal-price>
                {% if settings.currency_code_enabled %}
                  {{ cart.total_price | money_with_currency }}
                {% else %}
                  {{ cart.total_price | money }}
                {% endif %}
              </span>
            </span>
          </header-total-price>
        {% endif %}
      </a>
    </div>
  </div>
</div>
