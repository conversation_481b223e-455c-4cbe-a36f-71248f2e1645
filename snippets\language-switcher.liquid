{% if localization.available_languages.size > 1 %}
  <localization-form>
    {%- assign form_id = 'localization-language-' | append: section.id -%}
    {% form 'localization', id: form_id %}
      <input type="hidden" name="language_code" value="{{ localization.language.iso_code }}">
      <div class="disclosure relative flex align-center">
        <div class="{% if type == 'mobile-navigation' %}mobile-navigation__button {% endif %}button-localization  pointer disclosure__button show-overlay flex align-center gap-10">
          <span class="{% if type == 'mobile-navigation' %}flex flex-column gap-4 align-center{% endif %}">
            <span {% if type == 'mobile-navigation' %}style="font-size:14px;"{% endif %}>{{ localization.language.endonym_name | capitalize }}</span>
            {% if type == 'mobile-navigation' %}
              <span class="block fs-small heading-style">{{ 'main_menu.language.title' | t }}</span>
            {% endif %}
          </span>
          {% if type != 'mobile-navigation' %}
            <svg class="icon-down flex-10" width="10" height="6">
              <use href="#icon-arrow-down" />
            </svg>
          {% endif %}
        </div>
        <div class="disclosure__list custom-scrollbar transition shadow invisible z-10 absolute left-0 right-0 color-default">
          <icon-close class="pointer-none border inline-flex hidden-1025 mx-auto rounded-50 overflow-hidden absolute top-0 inset-x-0 w-50 h-50 content-center heading-color bg-white">
            <svg width="13" height="13" viewBox="0 0 13 13" fill="none">
              <use href="#icon-close" />
            </svg>
          </icon-close>
          <ul class="h-full list-unstyled">
            {% for language in localization.available_languages %}
              <li class="disclosure__item py-4" tabindex="-1">
                <a
                  href="#"
                  class="no-underline text-color hover-heading-color"
                  {% if language.iso_code == localization.language.iso_code %}
                    aria-current="true"
                  {% endif %}
                  hreflang="{{ language.iso_code }}"
                  lang="{{ language.iso_code }}"
                  data-value="{{ language.iso_code }}"
                  arial-label="{{ language.endonym_name | capitalize }}"
                >
                  {{ language.endonym_name | capitalize }}
                </a>
              </li>
            {% endfor %}
          </ul>
        </div>
      </div>
    {% endform %}
  </localization-form>
{% endif %}
