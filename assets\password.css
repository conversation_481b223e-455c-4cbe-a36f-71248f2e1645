.banner-with-text-overplay.banner-position .type-banner * {
  height: 100%;
  display: block;
}

.banner-with-text-overplay.banner-position .bls__banner-text--content {
  display: flex;
  flex-direction: column;
}

.banner-with-text-overplay:not(.text-bellow-mobile) .bls__banner-text--content {
  padding: 1rem;
}

.banner-with-text-overplay.banner-position > div {
  flex: 0 0 auto;
  width: 100%;
}

.bls__banner-text--grid .bls__banner-text--content {
  display: flex;
  flex-direction: column;
  align-self: stretch;
  flex: 1;
}

.bls__banner-text--content-inner.content-bg {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

@media (min-width: 768px) {
  .bls__banner-text--content-inner:not(.content-full-with) {
    width: var(--content-width);
  }

  .bls__banner-text--grid > * {
    position: sticky;
    top: var(--height-header);
  }

  .bls__banner-text--content-inner.content-bg {
    padding-left: 3rem;
    padding-right: 3rem;
  }
}

.content-horizontal-position-left .bls__banner-text--content-inner {
  margin-right: auto;
}

.content-horizontal-position-center .bls__banner-text--content-inner {
  margin-left: auto;
  margin-right: auto;
}

.content-horizontal-position-right .bls__banner-text--content-inner {
  margin-left: auto;
}

.banner-with-text-overplay .bls__banner-text--content-inner {
  position: relative;
  padding: var(--bs-gutter-x);
  z-index: 1;
}

.banner-with-text-overplay:not(.text-bellow-mobile)
  .bls__banner-text--content.has-bg {
  padding: 5rem 3rem;
}

.bg-overlay--content-inner {
  background: transparent;
}

.bg-text-column {
  background-color: var(--content-bg);
  background-image: var(--content-bg);
}

.bg-overlay--content-inner::before {
  content: "";
  opacity: var(--content-opacity);
  border-radius: 5px;
  inset: 0;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  position: absolute;
  pointer-events: none;
  z-index: -1;
  background-color: var(--content-bg);
  background-image: var(--content-bg);
}

.content-position-center {
  justify-content: center;
}

.content-position-bottom {
  justify-content: flex-end;
}

.content-position-top {
  justify-content: flex-start;
}

.bls__banner-text--img {
  margin-top: var(--img-mt);
  margin-bottom: var(--img-mb);
  max-width: var(--img-max-width);
}

.text-center .bls__banner-text--img {
  margin-left: auto;
  margin-right: auto;
}

.text-right .bls__banner-text--img {
  margin-left: auto;
}

.text-left .bls__banner-text--img {
  margin-right: auto;
}

.bls__banner-text--heading {
  font-family: var(--heading-font);
  color: var(--color-heading);
  font-size: clamp(3rem, 3.5vw, var(--text-heading-font-size));
  font-weight: var(
    --text-heading-font-weight,
    var(--bls__section-heading-weight)
  );
  text-transform: var(--text-heading-transform);
  margin-top: var(--text-heading-mt);
  margin-bottom: var(--text-heading-mb);
}

.bls__banner-text--subheading {
  color: var(--color-heading);
  font-size: var(--text-subheading-font-size);
  font-weight: var(--text-subheading-font-weight, var(--heading-font-weight));
  text-transform: var(--text-subheading-transform);
  margin-top: var(--text-subheading-mt);
  margin-bottom: var(--text-subheading-mb);
}

.bls__banner-text--des {
  margin-top: var(--text-mt);
  margin-bottom: var(--text-mb);
}

.bls__banner-text--button {
  margin-top: var(--b-mt);
  margin-bottom: var(--b-mb);
}

.bls__banner-text--html {
  margin-top: var(--html-mt);
  margin-bottom: var(--html-mb);
}

.bls__banner-text--des p {
  --base-color: #444;
  color: rgb(var(--base-color));
  font-size: var(--text-font-size);
  font-weight: var(--text-font-weight, var(--base-font-weight));
  text-transform: var(--text-transform);
}
.bls__banner-text--des p a {
  font-weight: 600;
  color: #111;
}
.bls__banner-subtext .mb-10 {
  margin-bottom: 1rem;
  line-height: 1;
}

.bls__banner-text--content.text-center .timer-banner {
  justify-content: center;
}

.bls__banner-text--content.text-right .timer-banner {
  justify-content: flex-end;
}

.timer-banner {
  margin-top: var(--bn-text-mt);
  margin-bottom: var(--bn-text-mb);
  flex-wrap: wrap;
}

.timer-banner .timer-block {
  color: rgb(var(--base-color));
  font-weight: var(--bn-text-font-weight);
  text-transform: var(--bn-text-transform);
  white-space: nowrap;
}

.timer-banner .timer-block__num {
  font-size: clamp(2.4rem, 3.5vw, var(--bn-number-font-size));
  color: rgb(var(--base-color));
}

.timer-banner .timer-block__text {
  font-size: var(--bn-text-font-size);
  text-transform: var(--bn-text-transform);
}

.countdown_design_1 .timer-banner .timer-block:not(:last-child):after {
  content: ":";
  font-size: 2.4rem;
  margin: 0 1rem;
  line-height: 1;
}

.countdown_design_2 .timer-banner {
  border: 1px solid #dedede;
  border-radius: 5px;
  position: relative;
}

.countdown_design_2 .timer-banner::after {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  border-radius: 5px;
  backdrop-filter: blur(5px);
  filter: blur(5px);
  z-index: 1;
}

.countdown_design_2 .timer-block {
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-width: clamp(5.1rem, 12vw, 14rem);
  min-height: clamp(5rem, 12vw, 14rem);
  padding: 1rem;
  position: relative;
  z-index: 2;
  text-align: center;
}

@media (max-width: 360px) {
  .countdown_design_2 .timer-block {
    padding: 1.5rem 0;
  }
}

.countdown_design_2 .timer-block::before {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background-color: var(--bn-text-color);
  opacity: 0.1;
}

.countdown_design_2 .timer-block:not(:last-child) {
  border-right: 1px solid #dedede;
}

.countdown_design_2 .timer-block__num {
  line-height: normal;
  vertical-align: middle;
}

.bls__banner-text--grid .countdown_design_2 .timer-banner {
  border: none;
  gap: 5px;
}

.banner-text-infor .info-label {
  color: var(--color-heading);
  font-weight: var(--bls__section-heading-weight);
}

.banner-text-infor {
  display: grid;
  grid-template-columns: auto 1fr;
  gap: 15px;
}

.info-item {
  display: contents;
  font-size: calc(var(--base-font-size) + 2px);
}

.bls__banner-text--grid .countdown_design_2 .timer-block {
  border: 1px solid #dedede;
  border-radius: 5px;
}

.countdown_design_3 .timer-block {
  display: flex;
  flex-direction: column;
}

.countdown_design_3 .timer-block:not(:last-child) {
  margin-right: 7rem;
}

.countdown_design_3 .timer-banner .timer-block__num {
  font-weight: 400;
}

@media (max-width: 475px) {
  .countdown_design_3 .timer-block:not(:last-child) {
    margin-right: 2.5rem;
  }
}

@media (min-width: 1200px) and (max-width: 1400px) {
  .countdown_design_3 .timer-block:not(:last-child) {
    margin-right: 3.5rem;
  }
}

.bls__banner-with-text.has-bg .row > div {
  margin-bottom: 0;
}

.bls__banner-with-text.has-bg .bls__banner-text--media {
  align-self: stretch;
}

.bls__banner-with-text.has-bg .bls__banner-text--media .hover-effect,
.bls__banner-with-text.has-bg .bls__banner-text--media .hover-effect > div,
.bls__banner-with-text.has-bg
  .bls__banner-text--media
  .hover-effect
  .bls__responsive-image {
  height: 100%;
}

.bls__banner-with-text .bls__banner-text--button {
  margin-right: 10px;
}

.bls__banner-with-text
  .bls__banner-text--content.text-right
  .bls__banner-text--button {
  margin-left: 10px;
  margin-left: 0;
}

.bls__banner-with-text
  .bls__banner-text--content.text-center
  .bls__banner-text--button {
  margin-left: 5px;
  margin-right: 5px;
}

@media (min-width: 768px) {
  .bls__banner-text--media.col-md-60 {
    flex: 0 0 auto;
    width: 60%;
  }
}

@media (max-width: 767px) {
  .banner-with-text-overplay.text-bellow-mobile .bls__banner-text--content,
  .banner-with-text-overplay.text-bellow-mobile .bls__banner-text--media {
    position: relative;
  }
}

@media (min-width: 1200px) {
  .bls__banner-text--media .video-banner.custom-height.height-full,
  .bls__banner-text--media.custom-height.height-full .bls__responsive-image {
    height: 100vh;
  }

  .bls__banner-text--media .video-banner.custom-height,
  .bls__banner-text--media.custom-height .bls__responsive-image {
    height: var(--desktop-height);
  }
}

@media (min-width: 768px) and (max-width: 1199px) {
  .bls__banner-text--media .video-banner.custom-height,
  .bls__banner-text--media.custom-height .bls__responsive-image {
    height: var(--tablet-height);
  }
}

@media (max-width: 767px) {
  .bls__banner-text--media .video-banner.custom-height,
  .bls__banner-text--media.custom-height .bls__responsive-image {
    height: var(--mobile-height);
  }

  .bls__banner-with-text
    .bls__banner-text--grid
    .bls__banner-text--content-inner:not(.content-bg) {
    padding-right: calc(var(--bs-gutter-x) * 0.5);
    padding-left: calc(var(--bs-gutter-x) * 0.5);
  }
}

@media (max-width: 375px) {
  .content-horizontal-position-center .bls__banner-text--content-inner {
    margin: initial;
  }
}
.bls__subcribe-input {
  width: 400px;
}
.bls__subcribe-submit {
  margin-bottom: var(--mb);
  margin-top: var(--mt);
}
.bls__subcribe-submit input {
  color: #888;
}
.bls__password-with-banner {
  width: var(--content-width);
}
.content-horizontal-center {
  margin-left: auto;
  margin-right: auto;
}
.content-horizontal-left {
  margin-right: auto;
}
.content-horizontal-right {
  margin-left: auto;
}
.password__footer-caption {
  display: flex;
}
.password__footer-caption svg {
  width: 100px;
  height: 20px;
  margin-left: -12px;
}
.pass_footer_content {
  display: flex;
  flex-direction: column;
  gap: 20px;
  justify-content: center;
  align-items: center;
}
.password__footer {
  padding-top: 20px;
}
.btn-primary:hover {
  color: #111111;
  background-color: white;
}
