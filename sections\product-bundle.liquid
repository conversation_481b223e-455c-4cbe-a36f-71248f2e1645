{%- liquid
    assign section_st = section.settings
    assign theme_st = settings
    assign section_width = section_st.section_width
    assign color_scheme = section_st.color_scheme
    if section_st.reset_spacing
      assign reset_spacing = ' remove_spacing'
    endif
    assign heading = section_st.heading
    assign description = section_st.description
    assign header_size = ''
    if section_st.header_size == 'small'
      assign header_size = 'h3'
    elsif section_st.header_size == 'large'
      assign header_size = 'h1-size'
    endif
    assign scroll_animation = settings.scroll_animation
    assign arr_product = ''
  -%}
  {%- capture style -%}
    --section-pt: {{ section_st.padding_top }}; --section-pb: {{ section_st.padding_bottom }};
  {%- endcapture -%}

  <div
    class="section{% if section_st.padding_top < 30 %} pt-min{% endif %}{% if section_st.padding_bottom < 30 %} pb-min{% endif %} sec__products-bundle color-{{ color_scheme }} gradient{{ reset_spacing }}"
    style="{{ style | strip | strip_newlines }}"
  >
    <div class="{{ section_width }}">
      {%- if heading != blank or description != blank -%}
        <div
          class="section__header mb-33 mb-sm-20 text-{{ section_st.header_alignment }}{% if section_st.section_width == 'full_width' %} px-20{% endif %}"
        >
          {%- if heading != blank or description != blank -%}
            <div class="secion__header-inner">
              {%- if section_st.heading != blank -%}
                <motion-element
                  data-motion="fade-up"           
                  {% if scroll_animation != 'slide_in' %}
                    hold
                  {% endif %}
                  data-motion-delay="50"
                  class="block  {% if scroll_animation != 'none' %} scroll-trigger {{ scroll_animation }} {% endif %}"
                  style="
                  {%- if scroll_animation != 'none' -%}
                    --animation-order: 0;
                  {% endif %}
                "
                >
                <h2
                  class="section__header-heading heading-letter-spacing  {{ header_size  }} mt-0{% if section_st.description != blank %} mb-10{% else %} mb-0{% endif %}"
                  {%- if scroll_animation != 'none' -%}
                    style="--animation-order: 0"
                  {% endif %}
                >
                  {{ section_st.heading }}
                </h2>
              </motion-element>
              {% endif %}
              {%- if section_st.description != blank -%}
                <motion-element
                  data-motion="fade-up"
                  data-motion-delay="150"
                  {% if scroll_animation != 'slide_in' %}
                    hold
                  {% endif %}
                  class="block {% if scroll_animation != 'none' -%} scroll-trigger {{ scroll_animation }}{% endif %} "
                  style="
                  {%- if scroll_animation != 'none' -%}
                    --animation-order: 1;
                  {% endif %}
                "
                >
                <div
                  class="section__header-des  rich__text-m0"
                  {%- if scroll_animation != 'none' -%}
                    style="--animation-order: 1"
                  {% endif %}
                >
                  {{ section_st.description }}
                </div>
              </motion-element>
              {% endif %}
            </div>
          {% endif %}
        </div>
      {% endif %}
      <product-bundle class="relative" id="product-bundle-{{ section.id }}">
        <div class="product-bundle-wrapper block flex-1025 lg:gap-40">
        {%- for block in section.blocks -%}
          {%- assign block_st = block.settings -%}
          {%- case block.type -%}
            {%- when 'product_discount' -%}
              {% liquid
                assign collection = block_st.collection
                assign products = block_st.products
                assign items_to_show = block_st.items_to_show
                assign items_per_row = block_st.items_per_row
                assign column_gap = block_st.column_gap
                assign carousel_on_mobile = block_st.carousel_on_mobile
                assign items_per_row_mobile = block_st.items_per_row_mobile
                if products != blank
                  assign arr_product = products
                else
                  assign arr_product = collection.products
                endif
                if items_per_row_mobile > 1 and items_per_row_mobile < 2
                    assign data_free_scroll = 'true'
                  endif
              %}
              {%- capture col_style -%}
                --col-desktop: {{ items_per_row }};--col-number:{{ items_per_row_mobile }};{% if items_per_row > 3 %}--col-tablet: 3;{% else %}--col-tablet: {{ items_per_row }};{% endif %}
                {% if column_gap < 15 %}--col-gap: {{ column_gap }}px;{% else %}--col-gap: 15px;--col-gap-desktop: {{  column_gap }}px;{% endif %}--repeat: {{ items_to_show }}; --col-mobile: {{ items_per_row_mobile }}
                {%- endcapture -%}
                <div class="grow-1">
                <grid-custom
                    class="grid grid-cols gap {% if column_gap < 31 %} row-gap-20 row-gap-md-30{% endif %}{% if carousel_on_mobile %} row-gap-mb-0{% endif %} flex-wrap product-ajax__append swiper{% if section_st.items_per_row_mobile > 1 and section_st.items_per_row_mobile < 2 %} grid_scroll{% endif %}"
                    style="{{ col_style | strip | strip_newlines }}"
                    data-enable="{{ carousel_on_mobile }}"
                    data-section-id="{{ section.id }}"
                    data-mobile="{{ items_per_row_mobile }}"
                    data-spacing="{{ column_gap }}"
                    data-free-scroll="{{ data_free_scroll }}"
                  >
                    {% if arr_product != blank %}
                      {% for product in arr_product limit: items_to_show %}
                        {% render 'product-item-bundle',
                          card_product: product,
                          section_id: block.id,
                          class: ' grid-custom-item',
                          template_enable_action: true,
                          template_enable_product_vendor: false,
                          template_enable_rate: true,
                          template_enable_product_short_description: false,
                          template_enable_color_swatches: true,
                          template_enable_price: true,
                          indexFor: forloop.index,
                          scroll_animation: scroll_animation,
                          template_enable_product_badges: true,
                          template_enable_add_cart: true,
                          type: 'product-bundle',
                        %}
                      {% endfor %}
                    {% else %}
                      {% for product in (1..3) %}
                        {% render 'product-item',
                          section_id: block.id,
                          class: ' grid-custom-item',
                          template_enable_action: true,
                          template_enable_product_vendor: false,
                          template_enable_rate: true,
                          template_enable_product_short_description: false,
                          template_enable_color_swatches: true,
                          template_enable_price: true,
                          indexFor: forloop.index,
                          scroll_animation: scroll_animation,
                          template_enable_product_badges: true,
                          template_enable_add_cart: true
                        %}
                      {% endfor %}
                    {% endif %}
                  </grid-custom>
                </div>
            {%- when 'your_bundle' -%}
              {%liquid
                assign heading = block_st.heading
                assign description = block_st.description
                assign heading_font_size = block_st.heading_font_size
                assign heading_font_weight = block_st.heading_font_weight
                assign heading_uppercase = block_st.heading_uppercase
                assign heading_spacing_bottom = block_st.heading_spacing_bottom
                assign des_font_size = block_st.des_font_size
                assign des_font_weight = block_st.des_font_weight
                assign des_spacing_bottom = block_st.des_spacing_bottom
              %}
              <div class="bundle-content pointer-none">
                <sticky-element class="product-bundle__sidebar flex flex-column pointer-events-auto">
                  {%- if heading != blank or description != blank -%}
                    <div class="bundle-content__header">
                      {%- if heading != blank -%}
                       <div class="flex align-center justify-between collapsible-bundle {% if heading_spacing_bottom > 41 %} mb-big{% elsif heading_spacing_bottom > 30 %} mb-medium {% else %} mb-custom{% endif %}"
                            style="--space-bottom: {{ heading_spacing_bottom }}"
                        >
                          <h2
                            class="sec__content-heading heading-letter-spacing mt-0 mb-0 {% if heading_uppercase %} uppercase{% endif %}{% if heading_font_size > 41 %} fs-big{% elsif heading_font_size > 24 %} fs-medium {% else %} fs-custom{% endif %} {{ heading_font_weight }}"
                            style="--font-size: {{ heading_font_size }};"
                            {{ block.shopify_attributes }}
                          >
                            {{ heading }}
                          </h2>
                          <span class="open-children-toggle hidden-1025 inline-flex align-center h-40 w-40">
                            <span class="icon_plus-animation"> </span>
                          </span>
                       </div>
                      {%- endif -%}
                      {%- if description != blank -%}
                        <div
                          class="sec__content-des rich__text-m0 {% if des_spacing_bottom > 41 %} mb-big{% elsif des_spacing_bottom > 30 %} mb-medium {% else %} mb-custom{% endif %}{% if des_font_size > 24 %} fs-medium {% else %} fs-custom{% endif %} {{ des_font_weight }}"
                          style="--font-size: {{ des_font_size }};--space-bottom: {{ des_spacing_bottom }}"
                          {{ block.shopify_attributes }}
                        >
                          {{ description }}
                        </div>
                      {%- endif -%}
                      <progress-bundle-bar data-minimum="{{ section.settings.minimum }}"></progress-bundle-bar>
                    </div>
                  {%- endif -%}
                 
                  <div class="bundle-items__wrapper">
                    <div class="bundle-content__items flex flex-column gap-15 ">
                      {% for item in (1..section_st.maximum) limit: section_st.maximum %}
                        <div class="horizontal-product flex align-center gap-10" data-product-bundle-variant>
                          <figure class="horizontal-product__media media relative overflow-hidden shrink-0" data-product-bundle-variant-media style="--aspect-ratio: 1/1;"></figure>
                          <div class="horizontal-product__details grow-1 flex flex-column flex-col justify-start" data-product-bundle-variant-content>
                            <span class="horizontal-product__skeleton skeleton-1"></span>
                            <span class="horizontal-product__skeleton skeleton-2"></span>
                            <span class="horizontal-product__skeleton skeleton-3"></span>
                          </div>
                        </div>
                      {% endfor %}
                    </div>
                  </div>
                  <div class="bundle-content__footer">
                    <div class="subtotal flex gap-10 justify-between align-center pt-20 pb-20 border-top">
                      <h5 class="heading-color my-0">{{ 'general.cart.subtotal' | t }}</h5>
                      <span class="subtotal-price__bundle h5 my-5">
                        {%- if theme_st.show_currency_code -%}
                          {{ 0 | money_with_currency }}
                        {%- else -%}
                          {{ 0 | money }}
                        {%- endif -%}
                      </span>
                    </div>
                    <button-submit-bundle
                      class="action block btn-primary w-full text-center no-underline viewcart disabled"
                      role="button"
                      data-minimum="{{ section.settings.minimum }}"
                      data-maximum="{{ section.settings.maximum }}"
                      data-section-id="{{ section.id }}"
                    >
                      <span>{{ 'products.product.actions.add_to_bundle.label_add_all' | t }}
                      </span>
                    </button-submit-bundle>
                    <div class="bundle-message-container"></div>
                  </div>
                </sticky-element>
              </div>
          {%- endcase -%}
        {%- endfor -%}
      </div>
      </product-bundle>
    </div>
</div>

{% schema %}
{
  "name": "t:sections.product-bundle.name",
  "tag": "section",
  "disabled_on": {
    "groups": ["header", "footer", "custom.overlay"]
  },
  "settings": [
    {
      "type": "select",
      "id": "section_width",
      "label": "t:sections.all.section_width.label",
      "default": "fluid_container",
      "options": [
        {
          "value": "container",
          "label": "t:sections.all.section_width.container.label"
        },
        {
          "value": "fluid_container",
          "label": "t:sections.all.section_width.fluid_container.label"
        },
        {
          "value": "stretch_width",
          "label": "t:sections.all.section_width.stretch_width.label"
        },
        {
          "value": "full_width",
          "label": "t:sections.all.section_width.full_width.label"
        }
      ]
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.color_scheme.label",
      "default": "scheme-1"
    },
    {
      "type": "header",
      "content": "t:sections.all.section_header.label"
    },
    {
      "type": "text",
      "id": "heading",
      "label": "t:sections.all.section_header.heading.label"
    },
    {
      "type": "richtext",
      "id": "description",
      "label": "t:sections.all.section_header.description.label"
    },
    {
      "type": "select",
      "id": "header_size",
      "label": "t:sections.all.section_header.header_size.label",
      "default": "medium",
      "options": [
        {
          "value": "small",
          "label": "t:sections.all.section_header.header_size.small.label"
        },
        {
          "value": "medium",
          "label": "t:sections.all.section_header.header_size.medium.label"
        },
        {
          "value": "large",
          "label": "t:sections.all.section_header.header_size.large.label"
        }
      ]
    },
    {
      "type": "select",
      "id": "header_alignment",
      "label": "t:sections.all.section_header.alignment.label",
      "default": "left",
      "options": [
        {
          "value": "left",
          "label": "t:sections.all.section_header.alignment.left.label"
        },
        {
          "value": "center",
          "label": "t:sections.all.section_header.alignment.center.label"
        },
        {
          "value": "right",
          "label": "t:sections.all.section_header.alignment.right.label"
        }
      ]
    },
    {
        "type": "header",
        "content": "t:sections.product-bundle.settings.discount.name",
        "info": "t:sections.product-bundle.settings.discount.info"
    },
    {
        "type": "range",
        "id": "minimum",
        "label": "t:sections.product-bundle.settings.discount.minimum",
        "default": 2,
        "min": 2,
        "max": 6,
        "step": 1
    },
    {
        "type": "range",
        "id": "maximum",
        "label": "t:sections.product-bundle.settings.discount.maximum",
        "default": 3,
        "min": 3,
        "max": 12,
        "step": 1
    },
    {
        "type": "checkbox",
        "id": "enable_quantity",
        "label": "t:sections.product-bundle.settings.discount.edit_quantity",
        "default": true
    },
    {
      "type": "header",
      "content": "t:sections.all.section_padding.label"
    },
    {
      "type": "range",
      "id": "padding_top",
      "label": "t:sections.all.section_padding.top.label",
      "default": 0,
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px"
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "label": "t:sections.all.section_padding.bottom.label",
      "default": 0,
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px"
    },
    {
      "type": "checkbox",
      "id": "reset_spacing",
      "label": "t:sections.all.section_padding.reset_spacing.label",
      "default": false
    }
  ],
  "blocks": [
    {
        "type": "product_discount",
        "name": "t:sections.product-bundle.blocks.settings.product_discount.name",
        "limit": 1,
        "settings": [
            {
                "type": "collection",
                "id": "collection",
                "label": "t:sections.collections-list-page.blocks.collection.settings.collection"
            },
            {
                "type": "product_list",
                "id": "products",
                "label": "t:sections.products-list.settings.products.header",
                "info": "t:sections.product-bundle.blocks.settings.product_discount.info"
            },
            {
              "type": "range",
              "id": "items_to_show",
              "label": "t:sections.all.items.items_to_show.label",
              "min": 2,
              "max": 12,
              "step": 1,
              "default": 6
            },
            {
              "type": "range",
              "id": "items_per_row",
              "label": "t:sections.all.items.items_per_row.label",
              "min": 1,
              "max": 4,
              "step": 1,
              "default": 3
            },
            {
              "type": "range",
              "id": "column_gap",
              "label": "t:sections.all.items.column_gap.label",
              "min": 0,
              "max": 50,
              "step": 5,
              "default": 30,
              "unit": "px"
            },
            {
              "type": "header",
              "content": "t:sections.all.mobile_options.label"
            },
            {
              "type": "checkbox",
              "id": "carousel_on_mobile",
              "label": "t:sections.all.mobile_options.carousel_on_mobile.label",
              "default": false
            },
            {
              "type": "range",
              "id": "items_per_row_mobile",
              "label": "t:sections.all.items.items_per_row.label",
              "min": 1,
              "max": 2,
              "step": 0.5,
              "default": 1
            }
        ]
    },
    {
        "type": "your_bundle",
        "name": "t:sections.product-bundle.settings.your_bundle.name",
        "limit": 1,
        "settings": [
            {
              "type": "text",
              "id": "heading",
              "label": "t:sections.all.contents.heading.label",
              "default": "Bundle Contents"
            },
            {
              "type": "richtext",
              "id": "description",
              "label": "t:sections.all.contents.description.label",
              "default": "<p>Add at <strong>least 3</strong> products and <strong>Save 30%.</strong></p>"
            },
            {
              "type": "header",
              "content": "t:sections.all.content_setting.label"
            },
            {
              "type": "header",
              "content": "t:sections.all.content_settings.typography.label",
              "info": "t:sections.all.content_setting.info"
            },
            {
              "type": "paragraph",
              "content": "t:sections.all.content_settings.heading.label"
            },
            {
              "type": "range",
              "id": "heading_font_size",
              "label": "t:sections.all.content_settings.font_size.label",
              "default": 24,
              "min": 10,
              "max": 40,
              "step": 1,
              "unit": "px"
            },
            {
              "type": "select",
              "id": "heading_font_weight",
              "label": "t:sections.all.content_settings.font_weight.label",
              "default": "heading_weight",
              "options": [
                {
                  "value": "body_weight",
                  "label": "t:sections.all.content_settings.font_weight.body_weight.label"
                },
                {
                  "value": "subheading_weight",
                  "label": "t:sections.all.content_settings.font_weight.subheading_weight.label"
                },
                {
                  "value": "heading_weight",
                  "label": "t:sections.all.content_settings.font_weight.heading_weight.label"
                }
              ]
            },
            {
              "type": "checkbox",
              "id": "heading_uppercase",
              "label": "t:sections.all.text_transform.uppercase.label"
            },
            {
              "type": "range",
              "id": "heading_spacing_bottom",
              "label": "t:sections.all.content_settings.spacing.spacing_bottom.label",
              "default": 20,
              "min": 0,
              "max": 30,
              "step": 1,
              "unit": "px"
            },
            {
              "type": "paragraph",
              "content": "t:sections.all.content_settings.description.label"
            },
            {
              "type": "range",
              "id": "des_font_size",
              "label": "t:sections.all.content_settings.font_size.label",
              "default": 15,
              "min": 10,
              "max": 30,
              "step": 1,
              "unit": "px"
            },
            {
              "type": "select",
              "id": "des_font_weight",
              "label": "t:sections.all.content_settings.font_weight.label",
              "default": "body_weight",
              "options": [
                {
                  "value": "body_weight",
                  "label": "t:sections.all.content_settings.font_weight.body_weight.label"
                },
                {
                  "value": "subheading_weight",
                  "label": "t:sections.all.content_settings.font_weight.subheading_weight.label"
                },
                {
                  "value": "heading_weight",
                  "label": "t:sections.all.content_settings.font_weight.heading_weight.label"
                }
              ]
            },
            {
              "type": "range",
              "id": "des_spacing_bottom",
              "label": "t:sections.all.content_settings.spacing.spacing_bottom.label",
              "default": 30,
              "min": 0,
              "max": 40,
              "step": 1,
              "unit": "px"
            }
          ]
    }
  ],
  "presets": [
    {
      "name": "t:sections.product-bundle.name",
      "blocks": [
        {
            "type": "product_discount"
        },
        {
            "type": "your_bundle"
        }
      ]
    }
  ]
}
{% endschema %}