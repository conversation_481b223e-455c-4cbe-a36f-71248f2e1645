.header__minicart.show_popup .overlay:after {
  display: none;
}
html:has(.header__minicart.show_popup).open-minicart {
  overflow: auto;
}
html:has(.header__minicart.show_popup).open-minicart .tingle-enabled {
  position: static;
}
.cart_bar_w {
  height: 5px;
  background-color: #ddd;
}
.cart_bar_w span {
  display: block;
  height: 100%;
  animation: 5s linear infinite progress;
  background-color: var(--color-primary);
  -webkit-background-image: linear-gradient(
    135deg,
    #ffffff26 0 25%,
    #fff0 25% 50%,
    #ffffff26 50% 75%,
    #fff0 75% 100%
  );
  background-image: linear-gradient(
    135deg,
    #ffffff26 0 25%,
    #fff0 25% 50%,
    #ffffff26 50% 75%,
    #fff0 75% 100%
  );
  background-size: 40px 40px;
}
.percent_shipping_bar svg {
  top: 50%;
  transform: translate(50%, -50%);
  right: -1.3rem;
}
.cart_shipping_free .cart_bar_w span svg {
  right: 1.5rem;
}
.cart_shipping_free .primary-color {
  --color-primary: #14854e;
}
.cart_shipping_free.cart_shipping * {
  animation: flash 0.8s linear 0.8s 2 both;
}
.mini_cart_addon_btn.show-overlay {
  padding-inline: clamp(3rem, 2vw, 5rem);
  min-height: 5rem;
  --top: 15px;
}
.cart-addons > div:not(:last-child) {
  border-right: 1px solid var(--color-border);
  line-height: 1;
}
.cart__addon-content {
  transform: translateY(100%);
  max-height: 100%;
  overflow: auto;
  transition: var(--transition-popup);
}
.cart-options .product-option:not(:first-child)::before {
  content: ' / ';
  padding: 0 2px;
}
.product-option-property .product-option {
  gap: 5px;
}
.product-option-property .product-option:not(:first-child)::before {
  display: none;
}
.minicart-body .minicart__bottom{
  transform: translateY(5px);
}
.cart__addon-content.is-open {
  transform: translateY(0);
  pointer-events: auto;
}
.minicart__header + .cart-countdown-time countdown-timer {
  border-top: 1px solid var(--color-border);
}
:is(.cart-countdown-time, .page-cart) .countdown-inner {
  color: var(--color-primary);
  font-weight: var(--heading-weight);
  --countdown-text-weight: var(--heading-weight);
}
:is(.cart-countdown-time, .page-cart)
  .countdown--container:not(:last-child):after {
  display: none;
}
.items .cart-item:last-child {
  padding-bottom: 0;
  border-bottom: 0;
  margin-bottom: 0;
}
@keyframes flash {
  50%,
  0%,
  100% {
    opacity: 1;
  }
  25%,
  75% {
    opacity: 0;
  }
}
.minicart__wrapper.loading::after {
  background: var(--color-primary);
  content: '';
  height: 3px;
  left: 0;
  opacity: 1;
  position: absolute;
  top: 0;
  width: 0;
  z-index: 3;
  animation: al-loading 1s cubic-bezier(0.43, 0.27, 0.36, 0.83) forwards;
  transition: width 1s linear, opacity 0.35s linear 0.35s;
}
 .minicart__wrapper.finish.loading ::after {
  width: 100%;
  animation: al-loading-finish 0.3s cubic-bezier(0.43, 0.27, 0.36, 0.83)
    forwards;
}
.minicart__wrapper.loading::before {
  background-color: rgb(255 255 255 / 60%);
  bottom: 0;
  content: '';
  left: 0;
  opacity: 1;
  position: absolute;
  right: 0;
  top: 0;
  transition: opacity 0.35s cubic-bezier(0.43, 0.27, 0.36, 0.83);
  z-index: 3;
}
.drawer-bottom .checkbox-group .checkmark:before {
  --grey-color: #fff;
}
.addon-message:empty {
  display: none;
}
.popup_cart.open {
  transform: translateY(0);
  opacity: 1;
  visibility: visible;
}
.cart-countdown-time countdown--item {
  color: var(--color-primary);
}
.cart-recommend .product-item__wrapper {
  --col-width: 9rem;
  --product-item__price-top: 4px;
}
.rounded-style .cart-recommend .product-item__wrapper .product-item__inner {
  --rounded-radius: 5px;
}
.cart-recommend {
  --swiper-pagination-position: static;
  --swiper-pagination-mt: 12px;
  --swiper-pagination-bullet-width: 18px;
  --tns-nav-width: 5px;
  --swiper-pagination-bullet-height: 1.5rem;
  --tns-nav-border: transparent;
}
.cart-recommend .swiper-pagination {
  font-size: 0;
}
.cart-recommend .product-list-action {
  --transform-origin-end: right;
  --transform-origin-start: left;
  width: max-content;
  margin: 1.3rem auto 0;
  position: relative;
  display: inline-block;
}
.cart-recommend .product-list-action::after {
  content: "";
  width: 100%;
  height: 1px;
  background: currentColor;
  position: absolute;
  inset-inline-start: 0;
  bottom: 0;
  transform: scaleX(1);
  transform-origin: var(--transform-origin-end);
}
.cart-recommend .product-list-action:hover::after {
  transform: scaleX(1);
  transform-origin: var(--transform-origin-start);
  animation: text-underlined .8s;
}
@keyframes text-underlined  {
    0% {
      transform: scaleX(1);
      transform-origin: var(--transform-origin-end);
  }

  50% {
      transform: scaleX(0);
      transform-origin: var(--transform-origin-end);
  }
  51% {
      transform-origin: var(--transform-origin-start);
  }
  100% {
      transform: scaleX(1);
  }

}
.cart-recommend.inside .product-item__wrapper {
  border: 1px solid var(--color-heading);
  padding: 15px;
  border-radius: var(--rounded-radius);
}
@media (min-width: 1025px) and (pointer: fine) and (prefers-reduced-motion: no-preference) {
  .cart-recommend .bls-add-cart-list:hover {
    color: var(--btn-link-hover-color);
  }
  .cart-recommend .bls-add-cart-list:hover::before {
    width: calc(100% - 3px);
    transition: var(--transition);
    animation: 1s infinite btnLinkAnimation;
  }
}
@media (min-width: 1025px) {
  .cart-recommend.beside {
    position: fixed;
    width: 23rem;
    transform: translateX(0);
    transition: transform 0.5s cubic-bezier(0.25, 0.46, 0.4, 1), all 0.5s,
      opacity 0.5s 0.2s;
    display: flex;
    flex-direction: column;
    border-right: 1px solid var(--color-border);
    padding: 0;
    border-top: 0;
    left: 0;
    top: 0;
    bottom: 0;
    z-index: -1;
  }
  .cart-recommend.beside.open {
    transform: translateX(-100%);
    opacity: 1;
    visibility: visible;
    pointer-events: auto;
  }
  .minicart__wrapper:not(.open) .cart-recommend.beside.open {
    pointer-events: none;
  }
  .cart-recommend grid-custom {
    --col-gap: 25px;
    padding: 3rem;
    flex-grow: 1;
    overflow: auto;
  }
  .cart-recommend.beside grid-custom {
    flex-grow: unset;
  }
  .cart-recommend.beside .recommend-heading {
    padding: 1.55rem 3rem;
    min-height: 57.78px;
    border-bottom: 1px solid var(--color-border);
    position: sticky;
    top: 0;
  }
  .cart-recommend.beside .minicart-heading {
    margin-bottom: 0;
  }
  .cart-recommend.beside .product-item__wrapper {
    flex-wrap: wrap;
    --col-width: 100%;
    --gap: 2rem;
    justify-content: center;
  }
  .cart-recommend.beside .product-item__information {
    text-align: center;
  }
  .cart-recommend.beside .product-item__information > * {
    justify-content: center;
  }
  .popup_cart {
    position: fixed;
    z-index: 13;
    right: calc(var(--bs-gutter-x) * 2);
    bottom: 0;
    width: 40rem;
    max-width: 90vw;
    max-height: 80vh;
    top: calc(100% + (var(--section-pb) * 1px) + 3px);
    transform: translateY(30px);
    --transition-popup: transform 0.6s cubic-bezier(0.7, 0, 0.2, 1),
      opacity 0.6s cubic-bezier(0.7, 0, 0.2, 1),
      visibility 0.6s cubic-bezier(0.7, 0, 0.2, 1);
  }
}
@media (max-width: 1024.98px) {
  .cart-recommend.beside .product-item__wrapper {
    border: 1px solid var(--color-heading);
    padding: 15px;
    border-radius: var(--rounded-radius);
  }
  .cart-recommend.beside .recommend-heading {
    padding: 2.8rem 0 0;
    border-top: 1px solid var(--color-border);
  }
  .cart-recommend.beside grid-custom {
    flex-wrap: nowrap;
    overflow: auto;
    padding: 0;
    margin-right: -3rem;
    gap: 10px;
  }
  .cart-recommend.beside grid-custom::-webkit-scrollbar {
    display: none;
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  .cart-recommend.beside .cart-upsell-item {
    flex: 0 0 80%;
  }
  .show_popup.open ~ .overlayminicart::after {
    opacity: 1;
    visibility: visible;
  }
  .popup_cart {
    z-index: 13;
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    transform: translateY(30px);
    max-height: 80vh;
    overflow: visible;
    --transition-popup: transform 0.6s cubic-bezier(0.7, 0, 0.2, 1),
      opacity 0.6s cubic-bezier(0.7, 0, 0.2, 1),
      visibility 0.6s cubic-bezier(0.7, 0, 0.2, 1);
  }
}

variant-radios-quick-edit .option-swatch-js.active .product__item-option {
  color: var(--btn-primary-hover-color);
  background-color: var(--btn-primary-hover-bg-color);
  border-color: var(--btn-primary-hover-bg-color);
}
body .dmp_discount-form button {
  width: auto !important;
  border-radius: var(--btn-radius) !important;
  color: var(--btn-color) !important;
  background-color: var(--btn-bg) !important;
}
body .dmp_discount-form input {
  border-radius: var(--btn-radius) !important;
}
.cart-item__error {
  flex: 0 0 auto;
  width: 100%;
}
.cart-item__error-text + svg {
  flex-shrink: 0;
  margin-right: 0.7rem;
}
.cart-item__error-text:empty + svg {
  display: none;
}
.cart-item__error-text {
  order: 1;
  color: #d0473e;
}
.gift_card_product.open {
  pointer-events: none;
}
.loading_cart{
   display: none;
}
.loading .loading_cart {
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  z-index: 14;
}

.loading_cart::before {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  opacity: 0.7;
  background-color: #fff;
  pointer-events: none; 
}