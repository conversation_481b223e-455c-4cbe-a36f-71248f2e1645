{% # theme-check-disable ContentForHeaderModification %}
{%- capture content_for_query_string -%}
  {{ content_for_header }}
{%- endcapture -%}
{% # theme-check-enable ContentForHeaderModification %}
{%- assign pageurl = content_for_query_string
  | split: '"pageurl":"'
  | last
  | split: '"'
  | first
  | split: request.host
  | last
  | replace: '\/', '/'
  | replace: '%20', ' '
  | replace: '\u0026', '&'
-%}
{%- assign page_query_string = pageurl | split: '?' | last -%}
{%- liquid
  assign section_st = section.settings
  assign section_width = section_st.section_width
  assign color_scheme = section_st.color_scheme
  if section_st.reset_spacing
    assign reset_spacing = ' remove_spacing'
  endif
  assign column_gap = section_st.column_gap
  assign items_per_row = section_st.items_per_row
  assign items_per_row_mobile = section_st.items_per_row_mobile
  assign heading = section_st.heading
  assign description = section_st.description
  assign header_size = ''
  if section_st.header_size == 'small'
    assign header_size = 'h3'
  elsif section_st.header_size == 'large'
    assign header_size = 'h1-size'
  endif
  assign show_arrow = section_st.show_arrow
  assign carousel_pagination = section_st.carousel_pagination
  assign infinite = section_st.infinite
  assign autoplay = section_st.autoplay
  assign autorotate_speed = section_st.autorotate_speed
  assign reveal = section_st.reveal
  assign tiktok_handle = section_st.tiktok_handle
  if section_st.items_per_row_mobile > 1 and section_st.items_per_row_mobile < 2
    assign data_free_scroll = 'true'
  endif
  assign scroll_animation = settings.scroll_animation
-%}

{%- capture style -%}
  --section-pt: {{ section_st.padding_top }}; --section-pb: {{ section_st.padding_bottom }};--col-number: {{ items_per_row_mobile }}; --col-gap: {{ column_gap }}px;--col-desktop: {{ items_per_row }};
{%- endcapture -%}
{%- capture col_style -%}
{% if column_gap < 15 %}--col-gap: {{ column_gap }}px;{% else %}--col-gap: 15px;--col-gap-desktop: {{  column_gap }}px;{% endif %}
{%- endcapture -%}
{%- style -%}
  .tiktok-video-main {
    --col: var(--col-number);
    --gap: var(--col-gap);
  }
  .tiktok-embed {
    position: absolute;
    background: transparent;
    padding: 0;
    margin-top: 0;
  }
  .tiktok-embed :is(iframe, section) {
    transform: scale(clamp(0.2, calc((var(--item-width) - 1) / 325), 1));
    transform-origin: top left;
  }
  .custom-tiktok-height:before {
    padding-top: clamp(50px, calc(100% / (0 + var(--aspect-ratio, 16 / 9))), 780px);
  }
  @media (min-width: 1025px) {
    .tiktok-video-main {
      --col: var(--col-desktop);
    }
  }
{%- endstyle -%}
<div
  class="section{% if section_st.padding_top < 30 %} pt-min{% endif %}{% if section_st.padding_bottom < 30 %} pb-min{% endif %} sec__tiktok-video color-{{ color_scheme }} gradient{{ reset_spacing }}{% if reveal %} overflow-hidden{% endif %}"
  style="{{ style | strip | strip_newlines }}"
>
  <div class="{{ section_width }}">
    {%- if heading != blank or description != blank -%}
      <div class="section__header mb-33 mb-sm-20 text-{{ section_st.header_alignment }}{% if section_st.section_width == 'full_width' %} px-20{% endif %}">
        {%- if section_st.heading != blank -%}
          <motion-element
                data-motion="fade-up-lg"
                data-motion-delay="50"
                {% if scroll_animation != 'slide_in' %}
                  hold
                {% endif %}
            class="block  {% if scroll_animation != 'none' %} scroll-trigger {{ scroll_animation }}{% endif %}"
            style="
              {%- if scroll_animation != 'none' -%}
                --animation-order: 0;
              {% endif %}
            "
          >
          <h2
            class="section__header-heading  heading-letter-spacing {{ header_size  }} mt-0{% if section_st.description != blank %} mb-10{% else %} mb-0{% endif %}"
          >
            {{ section_st.heading }}
          </h2>
        </motion-element>
        {% endif %}
        {%- if section_st.description != blank -%}
          <motion-element
            data-motion="fade-up-lg"
            data-motion-delay="150"
            {% if scroll_animation != 'slide_in' %}
              hold
            {% endif %}
            class="section__header-des block  {% if scroll_animation != 'none' %} scroll-trigger {{ scroll_animation }}{% endif %}  rich__text-m0"
            style="
              {%- if scroll_animation != 'none' -%}
                --animation-order: 1
              {% endif %}
            "
          >
            {{ section_st.description }}
          </motion-element>
        {% endif %}
      </div>
    {% endif %}
    <tiktok-video class="tiktok-video-main tiktok-loading" data-section-id="{{ section.id }}">
      {% if page_query_string contains 'ajax=1'%}
        {% if section_st.items_per_row_mobile > 1 and section_st.items_per_row_mobile < 2 %}
          <div class="free-scroll">
        {% endif %}
        <slide-section
          class="swiper{% if reveal %} reveal_on_scroll{% endif %}"
          data-section-id="{{ section.id }}"
          data-autoplay="{{ autoplay }}"
          data-effect="slide"
          data-loop="{{ infinite }}"
          data-autoplay-speed="{{ autorotate_speed }}"
          data-spacing="{{ column_gap }}"
          data-mobile="{{ items_per_row_mobile }}"
          data-desktop="{{ items_per_row }}"
          data-pagination-progressbar="{% if carousel_pagination == 'show_progress_bar' %}true{% else %}false{% endif %}"
          data-free-scroll="{{ data_free_scroll }}"
          style="{{ col_style | strip | strip_newlines }}"
        >
          {% if show_arrow %}
            {%- render 'swiper-navigation' -%}
          {% endif %}
          <div class="swiper-wrapper">
            {%- for block in section.blocks -%}
              {%- liquid
                assign block_st = block.settings
              -%}
              <div
                {{ block.shopify_attributes }}
                class="swiper-slide "
              >
                <motion-element
                  data-motion="fade-up-lg"
                  {% if scroll_animation != 'slide_in' %}
                    hold
                  {% endif %}
                  data-motion-delay="{{ forloop.index0 | times: 50 }}"
                  class="section_tiktok-image block img-ratio overflow-hidden {% if scroll_animation != 'none' %} scroll-trigger {{ scroll_animation }}{% endif %}"
                  style="
                    {%- if scroll_animation != 'none' -%}
                      --animation-order: {{  forloop.index }}
                    {% endif %}
                  "
                >
                  <div class="custom-tiktok-height" style="--aspect-ratio: 325/790">
                    {%- if block_st.tiktok_embed_url != blank -%}
                      {{ block_st.tiktok_embed_url }}
                    {%- else -%}
                      {%- render 'placeholder-render', class: 'rounded' -%}
                    {% endif %}
                  </div>
                </motion-element>
              </div>
            {%- endfor -%}
          </div>
          {%- if carousel_pagination == 'show_dots'
            or carousel_pagination == 'show_dots_on_mobile'
            or carousel_pagination == 'show_progress_bar'
          -%}
          <motion-element
            data-motion="fade-up-sm"
            {% if scroll_animation != 'slide_in' %}
              hold
            {% endif %}
            data-motion-delay="150"
            class="swiper-pagination  flex flex-wrap  {% if scroll_animation == 'slide_in' %} slide_in scroll-trigger {% endif %}   px-15 lh-1 bottom-30 {% if carousel_pagination == 'show_dots_on_mobile' %} hidden-md{% endif %} justify-content-center{% if section_st.content_below_image %} absolute-md-impo{% endif %}"
            style="--swiper-pagination-bottom: 3rem;--swiper-pagination-position: static;z-index: 2"
          >   
          </motion-element>
          {% endif %}
        </slide-section>
        {% if section_st.items_per_row_mobile > 1 and section_st.items_per_row_mobile < 2 %}
          </div>
        {% endif %}
        {%- if tiktok_handle != blank -%}
          <div class="tiktok-handle w-full text-center">
            {{ tiktok_handle }}
          </div>
        {% endif %}
      {% else %}
        {% if section_st.items_per_row_mobile > 1 and section_st.items_per_row_mobile < 2 %}
          <div class="free-scroll">
        {% endif %}
        <slide-section
          class="swiper{% if reveal %} reveal_on_scroll{% endif %}"
          data-section-id="{{ section.id }}"
          data-autoplay="{{ autoplay }}"
          data-effect="slide"
          data-loop="{{ infinite }}"
          data-autoplay-speed="{{ autorotate_speed }}"
          data-spacing="{{ column_gap }}"
          data-mobile="{{ items_per_row_mobile }}"
          data-desktop="{{ items_per_row }}"
          data-pagination-progressbar="{% if carousel_pagination == 'show_progress_bar' %}true{% else %}false{% endif %}"
          data-free-scroll="{{ data_free_scroll }}"
          style="{{ col_style | strip | strip_newlines }}"
        >
          {% if show_arrow %}
            {%- render 'swiper-navigation' -%}
          {% endif %}
          <div class="swiper-wrapper">
            {%- for block in section.blocks -%}
              {%- liquid
                assign block_st = block.settings
              -%}
              <div
                {{ block.shopify_attributes }}
                class="swiper-slide "
              >
                <motion-element
                  data-motion="fade-up-lg"
                  {% if scroll_animation != 'slide_in' %}
                    hold
                  {% endif %}
                  data-motion-delay="{{ forloop.index0 | times: 50 }}"
                  class="section_tiktok-image block img-ratio overflow-hidden {% if scroll_animation != 'none' %} scroll-trigger {{ scroll_animation }}{% endif %}"
                  style="
                    {%- if scroll_animation != 'none' -%}
                      --animation-order: {{  forloop.index }}
                    {% endif %}
                  "
                >
                  <div class="custom-tiktok-height" style="--aspect-ratio: 330/780">
                    {%- render 'placeholder-render', class: 'rounded' -%}
                  </div>
                </motion-element>
              </div>
            {%- endfor -%}
          </div>
          {%- if carousel_pagination == 'show_dots'
            or carousel_pagination == 'show_dots_on_mobile'
            or carousel_pagination == 'show_progress_bar'
          -%}
          <motion-element
            data-motion="fade-up-sm"
            {% if scroll_animation != 'slide_in' %}
              hold
            {% endif %}
            data-motion-delay="150"
            class="swiper-pagination  flex flex-wrap  {% if scroll_animation == 'slide_in' %} slide_in scroll-trigger {% endif %}   px-15 lh-1 bottom-30 {% if carousel_pagination == 'show_dots_on_mobile' %} hidden-md{% endif %} justify-content-center{% if section_st.content_below_image %} absolute-md-impo{% endif %}"
            style="--swiper-pagination-bottom: 3rem;--swiper-pagination-position: static;z-index: 2"
          >   
          </motion-element>
          {% endif %}
        </slide-section>
        {% if section_st.items_per_row_mobile > 1 and section_st.items_per_row_mobile < 2 %}
          </div>
        {% endif %}
        {%- if tiktok_handle != blank -%}
          <div class="tiktok-handle w-full text-center">
            {{ tiktok_handle }}
          </div>
        {% endif %}
      {% endif %}
    </tiktok-video>
  </div>
</div>
{% schema %}
{
  "name": "t:sections.tiktok-video.name",
  "tag": "section",
  "class": "section",
  "disabled_on": {
    "groups": ["header", "footer", "custom.overlay"]
  },
  "settings": [
    {
      "type": "select",
      "id": "section_width",
      "options": [
        {
          "value": "container",
          "label": "t:sections.all.section_width.container.label"
        },
        {
          "value": "fluid_container",
          "label": "t:sections.all.section_width.fluid_container.label"
        },
        {
          "value": "stretch_width",
          "label": "t:sections.all.section_width.stretch_width.label"
        },
        {
          "value": "full_width",
          "label": "t:sections.all.section_width.full_width.label"
        }
      ],
      "default": "stretch_width",
      "label": "t:sections.all.section_width.label"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.color_scheme.label",
      "default": "default-color-scheme"
    },
    {
      "type": "header",
      "content": "t:sections.all.section_header.label"
    },
    {
      "type": "text",
      "id": "heading",
      "default": "Tiktok",
      "label": "t:sections.all.section_header.heading.label"
    },
    {
      "type": "richtext",
      "id": "description",
      "label": "t:sections.all.section_header.description.label"
    },
    {
      "type": "select",
      "id": "header_size",
      "label": "t:sections.all.section_header.header_size.label",
      "default": "medium",
      "options": [
        {
          "value": "small",
          "label": "t:sections.all.section_header.header_size.small.label"
        },
        {
          "value": "medium",
          "label": "t:sections.all.section_header.header_size.medium.label"
        },
        {
          "value": "large",
          "label": "t:sections.all.section_header.header_size.large.label"
        }
      ]
    },
    {
      "type": "select",
      "id": "header_alignment",
      "label": "t:sections.all.section_header.alignment.label",
      "default": "center",
      "options": [
        {
          "value": "left",
          "label": "t:sections.all.content_settings.content_alignment.left.label"
        },
        {
          "value": "center",
          "label": "t:sections.all.content_settings.content_alignment.center.label"
        },
        {
          "value": "right",
          "label": "t:sections.all.content_settings.content_alignment.right.label"
        }
      ]
    },
    {
      "type": "header",
      "content": "t:sections.tiktok-video.settings.general.label"
    },
    {
      "type": "richtext",
      "id": "tiktok_handle",
      "label": "t:sections.tiktok-video.settings.tiktok_handle",
      "default": "<p>@yourTiktokHandle</p>"
    },
    {
      "type": "range",
      "id": "items_per_row",
      "label": "t:sections.all.items.items_per_row_on_desktop.label",
      "min": 2,
      "max": 5,
      "step": 1,
      "default": 4
    },
    {
      "type": "range",
      "id": "column_gap",
      "label": "t:sections.all.items.column_gap.label",
      "min": 0,
      "max": 50,
      "step": 5,
      "default": 30,
      "unit": "px"
    },
    {
      "type": "header",
      "content": "t:sections.all.carousel_settings.label"
    },
    {
      "type": "checkbox",
      "id": "show_arrow",
      "label": "t:sections.all.carousel_settings.show-next-back.label",
      "default": false
    },
    {
      "type": "select",
      "id": "carousel_pagination",
      "label": "t:sections.all.carousel_settings.pagination.label",
      "options": [
        {
          "value": "disable",
          "label": "t:sections.all.carousel_settings.pagination.disable.label"
        },
        {
          "value": "show_dots",
          "label": "t:sections.all.carousel_settings.pagination.show_dots.label"
        },
        {
          "value": "show_dots_on_mobile",
          "label": "t:sections.all.carousel_settings.pagination.show_dots_on_mobile.label"
        },
        {
          "value": "show_progress_bar",
          "label": "t:sections.all.carousel_settings.pagination.show_progress_bar.label"
        }
      ]
    },
    {
      "type": "checkbox",
      "id": "infinite",
      "label": "t:sections.all.carousel_settings.infinite.label",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "autoplay",
      "label": "t:sections.all.carousel_settings.auto_change.label",
      "default": false
    },
    {
      "type": "range",
      "id": "autorotate_speed",
      "label": "t:sections.all.carousel_settings.change_slides_every.label",
      "max": 6,
      "min": 2,
      "step": 1,
      "unit": "s",
      "default": 5
    },
    {
      "type": "checkbox",
      "id": "reveal",
      "label": "t:sections.all.carousel_settings.reveal.label",
      "info": "t:sections.all.carousel_settings.reveal.info",
      "default": false
    },
    {
      "type": "header",
      "content": "t:sections.all.mobile_options.label"
    },
    {
      "type": "range",
      "id": "items_per_row_mobile",
      "label": "t:sections.all.items.items_per_row.label",
      "min": 1,
      "max": 2,
      "step": 0.5,
      "default": 1
    },
    {
      "type": "header",
      "content": "t:sections.all.section_padding.label"
    },
    {
      "type": "range",
      "id": "padding_top",
      "label": "t:sections.all.section_padding.top.label",
      "default": 0,
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px"
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "label": "t:sections.all.section_padding.bottom.label",
      "default": 0,
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px"
    },
    {
      "type": "checkbox",
      "id": "reset_spacing",
      "label": "t:sections.all.section_padding.reset_spacing.label",
      "default": false
    }
  ],
  "blocks": [
    {
      "type": "video",
      "name": "t:sections.tiktok-video.blocks.video.name",
      "settings": [
        {
          "type": "textarea",
          "id": "tiktok_embed_url",
          "label": "t:sections.tiktok-video.blocks.video.settings.tiktok_embed_url",
          "info": "t:sections.tiktok-video.blocks.video.settings.info"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "t:sections.tiktok-video.presets.name",
      "blocks": [
        {
          "type": "video"
        },
        {
          "type": "video"
        },
        {
          "type": "video"
        },
        {
          "type": "video"
        }
      ]
    }
  ]
}
{% endschema %}
