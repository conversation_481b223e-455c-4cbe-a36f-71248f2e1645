{%- liquid
  assign section_st = section.settings
  assign section_width = section_st.section_width
  assign color_scheme = section_st.color_scheme
  if section_st.reset_spacing
    assign reset_spacing = ' remove_spacing'
  endif
  assign heading = section_st.heading
  assign description = section_st.description
  assign header_size = ''
  if section_st.header_size == 'small'
    assign header_size = 'h3'
  elsif section_st.header_size == 'large'
    assign header_size = 'h1-size'
  endif
  if section_st.items_per_row_mobile > 1 and section_st.items_per_row_mobile < 2
    assign data_free_scroll = 'true'
  endif
  assign carousel_on_mobile = section_st.carousel_on_mobile
  assign tab_header_style = section_st.tab_header_style
  assign horizontal_tab_style = section_st.horizontal_tab_style
  assign display_mode = section_st.display_mode
  assign items_to_show = section_st.items_to_show
  assign items_per_row = section_st.items_per_row
  assign column_gap = section_st.column_gap
  assign show_arrow = section_st.show_arrow
  assign carousel_pagination = section_st.carousel_pagination
  assign infinite = section_st.infinite
  assign autoplay = section_st.autoplay
  assign autorotate_speed = section_st.autorotate_speed
  assign reveal = section_st.reveal
  assign rows = section_st.rows
  assign show_load_more_button = section_st.show_load_more_button
  assign button_label = section_st.button_label
  assign items_per_row_mobile = section_st.items_per_row_mobile
  assign scroll_animation = settings.scroll_animation
-%}
{% liquid
  assign enable_rtl = settings.rtl
  assign iso_code = request.locale.iso_code
  assign lis_language_rtl = settings.language_rtl
  if lis_language_rtl != blank
    if lis_language_rtl contains iso_code
      assign enable_rtl = true
    else
      assign enable_rtl = false
    endif
  endif
%}
{%- capture style -%}
  --section-pt: {{ section_st.padding_top }}; --section-pb: {{ section_st.padding_bottom }}
{%- endcapture -%}
{%- capture col_style -%}
--col-desktop: {{ items_per_row }};--col-number:{{ items_per_row_mobile }};{% if items_per_row > 3 %}--col-tablet: 3;{% else %}--col-tablet: {{ items_per_row }};{% endif %}
{% if column_gap < 15 %}--col-gap: {{ column_gap }}px;{% else %}--col-gap: 15px;--col-gap-desktop: {{  column_gap }}px;{% endif %}--repeat: {{ items_to_show }}; --col-mobile: {{ items_per_row_mobile }}
{%- endcapture -%}
{%- style -%}
  .collection-tab__tab-content {
    transform: translateY(30px);
    -webkit-transform: translateY(30px);
  }
  .collection-tab__tab-content.active {
    opacity: 1;
    visibility: visible;
    position: relative;
    transform: translateY(0);
    pointer-events: auto;
  }
  .select-collection-tab {
    border-bottom: 2px solid var(--color-heading);
  }
{%- endstyle -%}
<div
  class="section{% if section_st.padding_top < 30 %} pt-min{% endif %}{% if section_st.padding_bottom < 30 %} pb-min{% endif %} sec__collection-tab {% if show_load_more_button == true %}sec__products-loadmore {% endif %}color-{{ color_scheme }} gradient{{ reset_spacing }}{% if reveal %} overflow-hidden{% endif %}"
  style="{{ style | strip | strip_newlines }}"
>
  <div class="{{ section_width }}">
    {% if section.blocks.size == 0 %}
      {%- if heading != blank or description != blank -%}
        <div class="section__header mb-30 mb-sm-20 text-{{ section_st.header_alignment }}{% if section_st.section_width == 'full_width' %} px-20{% endif %}{% if button_label != blank %} flex gap-15 flex-wrap justify-content-{{ section_st.header_alignment }}{% if section_st.header_alignment == 'right' %} flex-row-reverse{% endif %}{% endif %}">
          {%- if section_st.heading != blank -%}
            <motion-element
              data-motion="fade-up-lg"
              data-motion-delay="50"
              {% if scroll_animation != 'slide_in' %}
                hold
              {% endif %}
              class="block  {% if scroll_animation != 'none' %} scroll-trigger {{ scroll_animation }}{% endif %}"
              style="
                {%- if scroll_animation != 'none' -%}
                  --animation-order: 0;
                {% endif %}
              "
            >
            <h2
              class="section__header-heading heading-letter-spacing   {{ header_size  }} mt-0{% if section_st.description != blank %} mb-10{% else %} mb-0{% endif %}"
              style="{%- if scroll_animation != 'none' -%}--animation-order: 0{% endif %}"
            >
              {{ section_st.heading }}
            </h2>
          </motion-element>
          {% endif %}
          {%- if section_st.description != blank -%}
            <motion-element
              data-motion="fade-up-lg"
              data-motion-delay="150"
              {% if scroll_animation != 'slide_in' %}
                hold
              {% endif %}
              class="section__header-des block  rich__text-m0"
              style="{%- if scroll_animation != 'none' -%}--animation-order: 1{% endif %}"
            >
              {{ section_st.description }}
            </motion-element>
          {% endif %}
        </div>
      {% endif %}
    {% endif %}
    {% if section.blocks.size > 0 %}
      <tab-items data-type="{{ tab_header_style }}">
        <motion-element
          data-motion="fade-up-lg"
          {% if scroll_animation != 'slide_in' %}
            hold
          {% endif %}
           data-motion-delay="150"
          class="text-{{ section_st.header_alignment }} block {% if scroll_animation != 'none' %} scroll-trigger {{ scroll_animation }}{% endif %} section__header mb-40 mb-sm-30{% if section_st.section_width == 'full_width' %} px-20{% endif %}"
          style="{%- if scroll_animation != 'none' -%}--animation-order: 2{% endif %}"
        >
          {%- if heading != blank or description != blank -%}
            <div class="text-{{ section_st.header_alignment }}">
              {%- if section_st.heading != blank -%}
                <h2 class="section__header-heading heading-letter-spacing {{ header_size  }} mt-0{% if section_st.description != blank %} mb-10{% else %} mb-0{% endif %}">
                  {{ section_st.heading }}
                </h2>
              {% endif %}
              {%- if section_st.description != blank -%}
                <div class="section__header-des rich__text-m0">
                  {{ section_st.description }}
                </div>
              {% endif %}
            </div>
          {% endif %}
          <div class="mt-30 inline-flex align-center max-w-100 {% if tab_header_style == 'horizontal' %} collection-tab__ta overflow-auto{% else %} collection-tab__select align-center gap-10{% endif %}{% if tab_header_style != 'horizontal' %} h4 my-0{% endif %}{% if tab_header_style == 'horizontal' and horizontal_tab_style == 'underline' %} border-bottom{% endif %}{% if tab_header_style == 'horizontal' and horizontal_tab_style != 'underline' %} gap-15{% endif %}">
            {% if tab_header_style != 'horizontal' %}
              <span>{{ 'sections.collection_tab.tab_header_select' | t }} </span>
              <div class="select-collection-tab relative select-custom">
                {% for block in section.blocks %}
                  {% liquid
                    assign block_st = block.settings
                    assign collection_title = block_st.collection.title
                    assign collection_title = block_st.collection_title | default: collection_title
                  %}
                  {% if forloop.first == true %}
                    <div class="select__selected select__selected_title inline-flex align-center gap-10 pointer">
                      {{ collection_title }}
                      <svg class="icon-down transition active-rotated" width="10" height="6">
                        <use href="#icon-arrow-down"></use>
                      </svg>
                    </div>
                  {% endif %}
                {% endfor %}

                <div class="select-custom__content invisible transition-short top-100 absolute shadow list-none z-2 py-20 px-25 color-default{% if enable_rtl %} right-0{% else %} left-0{% endif %}">
                  <ul class="list-none p-0 my-0">
            {% endif %}
            {% for block in section.blocks %}
              {% liquid
                assign block_st = block.settings
                assign collection_title = block_st.collection.title
                assign collection_title = block_st.collection_title | default: collection_title
              %}

              {% if tab_header_style == 'horizontal' -%}
                <div
                  data-block-id="{{ block.id }}"
                  class="collection-tab__tab-item whitespace-nowrap pointer px-20 pb-8 transition text-center heading-style{% if horizontal_tab_style == 'underline' %} fs-16 border-animation{% else %} grey-bg btn-hover btn-rounded px-20 py-10 btn-active{% endif %}{% if forloop.first %} active{% endif %}"
                  {{ block.shopify_attributes }}
                  style="--height: 2px;"
                >
                  {% if collection_title != blank %}
                    {{ collection_title }}
                  {% else %}
                    {{- 'onboarding.collection_title' | t -}}
                  {% endif %}
                </div>
              {%- else %}
                <li class="py-5 hover-heading-color pointer collection_title_input" data-id="{{ block.id }}">
                  {{ collection_title }}
                </li>
              {% endif %}
            {% endfor %}
            {% if tab_header_style != 'horizontal' %}
              </ul>
              </div>
              </div>
            {% endif %}
          </div>
        </motion-element>
        <div class="relative tab-content">
          {% if display_mode == 'grid' %}
            {% for block in section.blocks %}
              {% liquid
                assign block_st = block.settings
                assign collection = collections[block_st.collection]
                assign all_products_to_show = 0
                if collection.products.size >= items_to_show
                  assign all_products_to_show = items_to_show
                else
                  assign all_products_to_show = collection.products.size
                endif
                assign all_product_visible = rows | times: items_per_row
                assign limit_products_paginate = 0
                if all_products_to_show >= all_product_visible
                  assign limit_products_paginate = all_product_visible
                else
                  assign limit_products_paginate = all_products_to_show
                endif
              %}
              {% if collection.products.size > 0 %}
                {% if show_load_more_button == true %}
                  {%- paginate collection.products by limit_products_paginate -%}
                    <div
                      class="collection-tab__tab-content collection-tab-{{ block.id }} absolute invisible inset-0 transition pointer-none{% if forloop.first %} active{% endif %}"
                      data-block-id="{{ block.id }}"
                    >
                      {% if section_st.items_per_row_mobile > 1
                        and section_st.items_per_row_mobile < 2
                        and carousel_on_mobile
                      %}
                        <div class="free-scroll">
                      {% endif %}
                      <grid-custom
                        class="swiper products-grid__items flex {%  if carousel_on_mobile %} flex-column flex-md-row row-gap-mb-0{% endif %} flex-cols{% if column_gap < 31 %} row-gap-20 row-gap-md-30{% endif %} {% if display_mode == 'grid' %}flex-wrap{% else %}flex-md-wrap{% endif %} product-ajax__append{% if section_st.items_per_row_mobile > 1 and section_st.items_per_row_mobile < 2 and carousel_on_mobile == blank %} grid_scroll{% endif %}"
                        style="{{ col_style | strip | strip_newlines }}"
                        data-enable="{{ carousel_on_mobile }}"
                        data-section-id="{{ section.id }}"
                        data-mobile="{{ items_per_row_mobile }}"
                        data-spacing="{{ column_gap }}"
                        data-free-scroll="{{ data_free_scroll }}"
                      >
                        {% for product in collection.products %}
                          {% render 'product-item',
                            card_product: product,
                            container: section_width,
                            colunm: items_per_row,
                            colunm_mobile: section_st.items_per_row_mobile,
                            padding: column_gap,
                            scroll_animation: scroll_animation,
                            section_id: section.id,
                            class: 'grid-custom-item',
                            template_enable_action: true,
                            template_enable_product_vendor: true,
                            template_enable_rate: true,
                            template_enable_product_short_description: true,
                            template_enable_color_swatches: true,
                            template_enable_price: true,
                            template_enable_add_cart: true,
                            indexFor: forloop.index,
                            template_enable_product_badges: true
                          %}
                        {% endfor %}
                      </grid-custom>
                      {% if button_label != blank %}
                        {% if all_products_to_show > all_product_visible %}
                          <loadmore-function
                            data-all-products="{{ all_products_to_show }}"
                            data-section-id="{{ section.id }}"
                            data-limit="{{ limit_products_paginate }}"
                            data-collection-url="{{ collection.url }}"
                            data-url="?page={{ paginate.current_page | plus: 1 }}"
                            class="block text-center mt-25 w-full col-md-w-custom"
                            style="--col-width: 100%"
                            data-block-id="{{ block.id }}"
                          >
                            <button class="btn-{% if section_st.use_outline_button_style%}outline{% else %}primary{% endif %} load-more relative">
                              <span class="hidden-on-load">{{ button_label }}</span>
                              <svg
                                width="14"
                                height="14"
                                viewBox="0 0 14 14"
                                fill="none"
                                class="spin opacity-0 icon-load"
                              >
                                <use href="#icon-load"></use>
                              </svg>
                            </button>
                          </loadmore-function>
                        {% endif %}
                      {% endif %}
                      {% if section_st.items_per_row_mobile > 1
                        and section_st.items_per_row_mobile < 2
                        and carousel_on_mobile
                      %}
                        </div>
                      {% endif %}
                    </div>
                    {%- if paginate.pages > 1 -%}
                    {% endif %}
                  {% endpaginate %}
                {% else %}
                  <div
                    class="collection-tab__tab-content absolute invisible inset-0 transition pointer-none{% if forloop.first %} active{% endif %}"
                    data-block-id="{{ block.id }}"
                  >
                    {% if section_st.items_per_row_mobile > 1
                      and section_st.items_per_row_mobile < 2
                      and carousel_on_mobile
                    %}
                      <div class="free-scroll">
                    {% endif %}
                    <grid-custom
                      class="swiper products-grid__items flex flex-cols{% if column_gap < 31 %} row-gap-20 row-gap-md-30{% endif %}{% if carousel_on_mobile %} flex-column flex-md-row row-gap-mb-0{% endif %} flex-md-wrap  product-ajax__append{% if section_st.items_per_row_mobile > 1 and section_st.items_per_row_mobile < 2 and carousel_on_mobile == blank %} grid_scroll{% endif %}"
                      style="{{ col_style | strip | strip_newlines }}"
                      data-enable="{{ carousel_on_mobile }}"
                      data-mobile="{{ items_per_row_mobile }}"
                      data-spacing="{{ column_gap }}"
                      data-free-scroll="{{ data_free_scroll }}"
                    >
                      {% for product in collection.products limit: limit_products_paginate %}
                        {% render 'product-item',
                          card_product: product,
                          section_id: section.id,
                          container: section_width,
                          colunm: items_per_row,
                          colunm_mobile: section_st.items_per_row_mobile,
                          padding: column_gap,
                          class: ' grid-custom-item',
                          template_enable_action: true,
                          template_enable_product_vendor: true,
                          template_enable_rate: true,
                          template_enable_product_short_description: true,
                          template_enable_color_swatches: true,
                          template_enable_price: true,
                          template_enable_add_cart: true,
                          template_enable_product_badges: true,
                          scroll_animation: scroll_animation,
                          indexFor: forloop.index
                        %}
                      {% endfor %}
                    </grid-custom>
                    {% if section_st.items_per_row_mobile > 1
                      and section_st.items_per_row_mobile < 2
                      and carousel_on_mobile
                    %}
                      </div>
                    {% endif %}
                  </div>
                {% endif %}
              {% else -%}
                <div
                  class="collection-tab__tab-content absolute invisible inset-0 transition pointer-none{% if forloop.first %} active{% endif %}"
                  data-block-id="{{ block.id }}"
                >
                  {% if section_st.items_per_row_mobile > 1
                    and section_st.items_per_row_mobile < 2
                    and carousel_on_mobile
                  %}
                    <div class="free-scroll">
                  {% endif %}
                  <grid-custom
                    class="swiper products-grid__items flex flex-cols{% if column_gap < 31 %} row-gap-20 row-gap-md-30{% endif %}{%  if carousel_on_mobile %} flex-column flex-md-row row-gap-mb-0 {% endif %} flex-md-wrap product-ajax__append{% if section_st.items_per_row_mobile > 1 and section_st.items_per_row_mobile < 2 and carousel_on_mobile == blank %} grid_scroll{% endif %}"
                    style="{{ col_style | strip | strip_newlines }}"
                    data-enable="{{ carousel_on_mobile }}"
                    data-mobile="{{ items_per_row_mobile }}"
                    data-spacing="{{ column_gap }}"
                    data-free-scroll="{{ data_free_scroll }}"
                  >
                    {% for i in (1..all_product_visible) %}
                      {% render 'product-item',
                        section_id: section.id,
                        class: ' grid-custom-item',
                        template_enable_action: true,
                        template_enable_product_vendor: true,
                        template_enable_rate: true,
                        template_enable_product_short_description: true,
                        template_enable_color_swatches: true,
                        template_enable_price: true,
                        template_enable_add_cart: true,
                        template_enable_product_badges: true,
                        scroll_animation: scroll_animation,
                        indexFor: forloop.index
                      %}
                    {% endfor %}
                  </grid-custom>
                  {% if section_st.items_per_row_mobile > 1
                    and section_st.items_per_row_mobile < 2
                    and carousel_on_mobile
                  %}
                    </div>
                  {% endif %}
                </div>
              {%- endif %}
            {% endfor %}
          {% else %}
            {% for block in section.blocks %}
              {% liquid
                assign block_st = block.settings
                assign all_products_to_show = 0
                if block_st.collection.products.size >= items_to_show
                  assign all_products_to_show = items_to_show
                else
                  assign all_products_to_show = block_st.collection.products.size
                endif
                assign all_product_visible = rows | times: items_per_row
              %}
              {% if block_st.collection.products.size > 0 %}
                {%- paginate block_st.collection.products by all_product_visible -%}
                  <div
                    class="collection-tab__tab-content absolute invisible inset-0 transition pointer-none{% if forloop.first %} active{% endif %}"
                    data-block-id="{{ block.id }}"
                  >
                    {% if section_st.items_per_row_mobile > 1 and section_st.items_per_row_mobile < 2 %}
                      <div class="free-scroll">
                    {% endif %}
                    <slide-section
                      class="swiper{% if reveal %} reveal_on_scroll{% endif %}"
                      data-section-id="{{ section.id }}"
                      data-block-id="{{ block.id }}"
                      data-autoplay="{{ autoplay }}"
                      data-effect="slide"
                      data-loop="{{ infinite }}"
                      data-speed="800"
                      data-autoplay-speed="{{ autorotate_speed }}"
                      data-spacing="{{ column_gap }}"
                      data-mobile="{{ items_per_row_mobile }}"
                      data-tablet="3"
                      data-desktop="{{ items_per_row }}"
                      data-free-scroll="{{ data_free_scroll }}"
                      data-pagination-progressbar="{% if carousel_pagination == 'show_progress_bar' %}true{% else %}false{% endif %}"
                      style="{{ col_style | strip | strip_newlines }}"
                      data-arrow-centerimage="1"
                    >
                      {% if show_arrow %}
                        {%- render 'swiper-navigation' -%}
                      {% endif %}
                      <div class="swiper-wrapper">
                        {% for product in block_st.collection.products %}
                          {% render 'product-item',
                            card_product: product,
                            section_id: section.id,
                            class: ' swiper-slide',
                            template_enable_action: true,
                            template_enable_product_vendor: true,
                            template_enable_rate: true,
                            template_enable_product_short_description: true,
                            template_enable_color_swatches: true,
                            template_enable_price: true,
                            template_enable_add_cart: true,
                            template_enable_product_badges: true,
                            scroll_animation: scroll_animation,
                            indexFor: forloop.index
                          %}
                        {% endfor %}
                      </div>
                      {%- if carousel_pagination == 'show_dots'
                        or carousel_pagination == 'show_dots_on_mobile'
                        or carousel_pagination == 'show_progress_bar'
                      -%}
                      <motion-element
                        data-motion="fade-up-sm"
                        {% if scroll_animation != 'slide_in' %}
                          hold
                        {% endif %}
                        data-motion-delay="150"
                        class="swiper-pagination  flex flex-wrap  {% if scroll_animation == 'slide_in' %} slide_in scroll-trigger {% endif %}   px-15 lh-1 bottom-30 {% if carousel_pagination == 'show_dots_on_mobile' %} hidden-md{% endif %} justify-content-center{% if section_st.content_below_image %} absolute-md-impo{% endif %}"
                        style="--swiper-pagination-bottom: 3rem;--swiper-pagination-position: static;z-index: 2"
                      >   
                      </motion-element>
                      {% endif %}
                    </slide-section>
                    {% if section_st.items_per_row_mobile > 1 and section_st.items_per_row_mobile < 2 %}
                      </div>
                    {% endif %}
                  </div>
                {% endpaginate %}
              {% else -%}
                <div
                  class="collection-tab__tab-content absolute invisible inset-0 transition pointer-none{% if forloop.first %} active{% endif %}"
                  data-block-id="{{ block.id }}"
                >
                  {% if section_st.items_per_row_mobile > 1
                    and section_st.items_per_row_mobile < 2
                    and carousel_on_mobile
                  %}
                    <div class="free-scroll">
                  {% endif %}
                  <slide-section
                    class="swiper{% if reveal %} reveal_on_scroll{% endif %}"
                    data-section-id="{{ section.id }}"
                    data-autoplay="{{ autoplay }}"
                    data-effect="slide"
                    data-loop="{{ infinite }}"
                    data-speed="800"
                    data-autoplay-speed="{{ autorotate_speed }}"
                    data-spacing="{{ column_gap }}"
                    data-mobile="{{ items_per_row_mobile }}"
                    data-tablet="3"
                    data-desktop="{{ items_per_row }}"
                    data-pagination-progressbar="{% if carousel_pagination == 'show_progress_bar' %}true{% else %}false{% endif %}"
                    style="{{ col_style | strip | strip_newlines }}"
                  >
                    {% if show_arrow %}
                      {%- render 'swiper-navigation' -%}
                    {% endif %}
                    <div class="swiper-wrapper">
                      {% for i in (1..items_to_show) %}
                        {% render 'product-item',
                          section_id: section.id,
                          class: ' swiper-slide',
                          indexFor: i,
                          template_enable_action: true,
                          template_enable_product_vendor: true,
                          template_enable_rate: true,
                          template_enable_product_short_description: true,
                          template_enable_color_swatches: true,
                          template_enable_price: true,
                          template_enable_add_cart: true,
                          template_enable_product_badges: true,
                          scroll_animation: scroll_animation
                        %}
                      {% endfor %}
                    </div>
                    {%- if carousel_pagination == 'show_dots'
                      or carousel_pagination == 'show_dots_on_mobile'
                      or carousel_pagination == 'show_progress_bar'
                    -%}
                    <motion-element
                      data-motion="fade-up-sm"
                      {% if scroll_animation != 'slide_in' %}
                        hold
                      {% endif %}
                      data-motion-delay="150"
                      class="swiper-pagination  flex flex-wrap  {% if scroll_animation == 'slide_in' %} slide_in scroll-trigger {% endif %}   px-15 lh-1 bottom-30 {% if carousel_pagination == 'show_dots_on_mobile' %} hidden-md{% endif %} justify-content-center{% if section_st.content_below_image %} absolute-md-impo{% endif %}"
                      style="--swiper-pagination-bottom: 3rem;--swiper-pagination-position: static;z-index: 2"
                    >   
                    </motion-element>
                    {% endif %}
                  </slide-section>
                  {% if section_st.items_per_row_mobile > 1
                    and section_st.items_per_row_mobile < 2
                    and carousel_on_mobile
                  %}
                    </div>
                  {% endif %}
                </div>
              {%- endif %}
            {% endfor %}
          {% endif %}
        </div>
      </tab-items>
    {% endif %}
  </div>
</div>
{% schema %}
{
  "name": "t:sections.collection-tab.name",
  "tag": "section",
  "disabled_on": {
    "groups": ["header", "footer", "custom.overlay"]
  },
  "settings": [
    {
      "type": "select",
      "id": "section_width",
      "label": "t:sections.all.section_width.label",
      "default": "fluid_container",
      "options": [
        {
          "value": "container",
          "label": "t:sections.all.section_width.container.label"
        },
        {
          "value": "fluid_container",
          "label": "t:sections.all.section_width.fluid_container.label"
        },
        {
          "value": "stretch_width",
          "label": "t:sections.all.section_width.stretch_width.label"
        },
        {
          "value": "full_width",
          "label": "t:sections.all.section_width.full_width.label"
        }
      ]
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.color_scheme.label",
      "default": "scheme-1"
    },
    {
      "type": "header",
      "content": "t:sections.all.section_header.label"
    },
    {
      "type": "text",
      "id": "heading",
      "label": "t:sections.all.section_header.heading.label",
      "default": "Trending this week"
    },
    {
      "type": "richtext",
      "id": "description",
      "label": "t:sections.all.section_header.description.label"
    },
    {
      "type": "select",
      "id": "header_size",
      "label": "t:sections.all.section_header.header_size.label",
      "default": "medium",
      "options": [
        {
          "value": "small",
          "label": "t:sections.all.section_header.header_size.small.label"
        },
        {
          "value": "medium",
          "label": "t:sections.all.section_header.header_size.medium.label"
        },
        {
          "value": "large",
          "label": "t:sections.all.section_header.header_size.large.label"
        }
      ]
    },
    {
      "type": "select",
      "id": "header_alignment",
      "label": "t:sections.all.section_header.alignment.label",
      "default": "center",
      "options": [
        {
          "value": "left",
          "label": "t:sections.all.section_header.alignment.left.label"
        },
        {
          "value": "center",
          "label": "t:sections.all.section_header.alignment.center.label"
        },
        {
          "value": "right",
          "label": "t:sections.all.section_header.alignment.right.label"
        }
      ]
    },
    {
      "type": "header",
      "content": "t:sections.collection-tab.settings.collection.header"
    },
    {
      "type": "select",
      "id": "tab_header_style",
      "label": "t:sections.collection-tab.settings.collection.tab_header_style.label",
      "options": [
        {
          "value": "horizontal",
          "label": "t:sections.collection-tab.settings.collection.tab_header_style.horizontal"
        },
        {
          "value": "select",
          "label": "t:sections.collection-tab.settings.collection.tab_header_style.select"
        }
      ],
      "default": "horizontal"
    },
    {
      "type": "select",
      "id": "horizontal_tab_style",
      "label": "t:sections.collection-tab.settings.collection.horizontal_tab_style.label",
      "options": [
        {
          "value": "underline",
          "label": "t:sections.collection-tab.settings.collection.horizontal_tab_style.underline"
        },
        {
          "value": "fill",
          "label": "t:sections.collection-tab.settings.collection.horizontal_tab_style.fill"
        }
      ],
      "default": "fill"
    },
    {
      "type": "select",
      "id": "display_mode",
      "label": "t:sections.all.display_mode.label",
      "options": [
        {
          "value": "grid",
          "label": "t:sections.all.display_mode.grid"
        },
        {
          "value": "carousel",
          "label": "t:sections.all.display_mode.carousel"
        }
      ],
      "default": "grid"
    },
    {
      "type": "range",
      "id": "items_to_show",
      "label": "t:sections.all.items.items_to_show.label",
      "min": 1,
      "max": 24,
      "step": 1,
      "default": 8
    },
    {
      "type": "range",
      "id": "items_per_row",
      "label": "t:sections.all.items.items_per_row.label",
      "min": 2,
      "max": 5,
      "step": 1,
      "default": 4
    },
    {
      "type": "range",
      "id": "column_gap",
      "label": "t:sections.all.items.column_gap.label",
      "min": 0,
      "max": 50,
      "step": 5,
      "default": 30,
      "unit": "px"
    },
    {
      "type": "header",
      "content": "t:sections.all.carousel_settings.label"
    },
    {
      "type": "checkbox",
      "id": "show_arrow",
      "label": "t:sections.all.carousel_settings.show-next-back.label",
      "default": false
    },
    {
      "type": "select",
      "id": "carousel_pagination",
      "label": "t:sections.all.carousel_settings.pagination.label",
      "options": [
        {
          "value": "disable",
          "label": "t:sections.all.carousel_settings.pagination.disable.label"
        },
        {
          "value": "show_dots",
          "label": "t:sections.all.carousel_settings.pagination.show_dots.label"
        },
        {
          "value": "show_dots_on_mobile",
          "label": "t:sections.all.carousel_settings.pagination.show_dots_on_mobile.label"
        },
        {
          "value": "show_progress_bar",
          "label": "t:sections.all.carousel_settings.pagination.show_progress_bar.label"
        }
      ]
    },
    {
      "type": "checkbox",
      "id": "infinite",
      "label": "t:sections.all.carousel_settings.infinite.label",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "autoplay",
      "label": "t:sections.all.carousel_settings.auto_change.label",
      "default": false
    },
    {
      "type": "range",
      "id": "autorotate_speed",
      "label": "t:sections.all.carousel_settings.change_slides_every.label",
      "max": 6,
      "min": 2,
      "step": 1,
      "unit": "s",
      "default": 5
    },
    {
      "type": "checkbox",
      "id": "reveal",
      "label": "t:sections.all.carousel_settings.reveal.label",
      "info": "t:sections.all.carousel_settings.reveal.info",
      "default": false
    },
    {
      "type": "header",
      "content": "t:sections.collection-tab.settings.grid_settings.header"
    },
    {
      "type": "range",
      "id": "rows",
      "label": "t:sections.collection-tab.settings.grid_settings.rows",
      "max": 3,
      "min": 1,
      "step": 1,
      "default": 2
    },
    {
      "type": "checkbox",
      "id": "show_load_more_button",
      "label": "t:sections.collection-tab.settings.grid_settings.show_load_more_button",
      "default": false
    },
    {
      "type": "text",
      "id": "button_label",
      "label": "t:sections.collection-tab.settings.grid_settings.button_label",
      "default": "Load more"
    },
    {
      "type": "header",
      "content": "t:sections.all.mobile_options.label"
    },
    {
      "type": "checkbox",
      "id": "carousel_on_mobile",
      "label": "t:sections.all.mobile_options.carousel_on_mobile.label",
      "default": false
    },
    {
      "type": "range",
      "id": "items_per_row_mobile",
      "label": "t:sections.all.items.items_per_row.label",
      "info": "t:sections.all.mobile_options.enable_grid_scroll_on_mobile.info",
      "min": 1,
      "max": 2,
      "step": 0.5,
      "default": 1
    },
    {
      "type": "header",
      "content": "t:sections.all.section_padding.label"
    },
    {
      "type": "range",
      "id": "padding_top",
      "label": "t:sections.all.section_padding.top.label",
      "default": 0,
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px"
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "label": "t:sections.all.section_padding.bottom.label",
      "default": 0,
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px"
    },
    {
      "type": "checkbox",
      "id": "reset_spacing",
      "label": "t:sections.all.section_padding.reset_spacing.label",
      "default": false
    }
  ],
  "blocks": [
    {
      "type": "collection",
      "limit": 5,
      "name": "t:sections.collection-tab.blocks.name",
      "settings": [
        {
          "type": "collection",
          "id": "collection",
          "label": "t:sections.collection-tab.blocks.collection"
        },
        {
          "type": "text",
          "id": "collection_title",
          "label": "t:sections.collection-tab.blocks.collection_title.label",
          "info": "t:sections.collection-tab.blocks.collection_title.info"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "t:sections.collection-tab.name",
      "blocks": [
        {
          "type": "collection"
        },
        {
          "type": "collection"
        }
      ]
    }
  ]
}
{% endschema %}
