{%- liquid
  assign theme_st = settings
  assign cart_note = theme_st.enable_cart_note
  assign enable_gift_wrap = theme_st.enable_cart_note
  assign show_currency_code = theme_st.show_currency_code
  assign enable_discount_code = theme_st.enable_discount_code
  assign gift_wrap_pr = all_products[theme_st.choose_gift_wrap_product]
  assign gift_pr_id = gift_wrap_pr.id
  assign gift_wrap = cart.items | where: 'product_id', gift_pr_id
  if show_currency_code
    assign gift_pr_price = gift_wrap_pr.selected_or_first_available_variant.price | money_with_currency
  else
    assign gift_pr_price = gift_wrap_pr.selected_or_first_available_variant.price | money
  endif
  assign show_estimate_shipping_rates = theme_st.show_estimate_shipping_rates
  assign show_delivery_day = theme_st.show_delivery_day
-%}
{% liquid
  assign discount_codes = cart.cart_level_discount_applications | where: 'type', 'discount_code' | map: 'title'
  for item in cart.items
    for allocation in item.line_level_discount_allocations
      if allocation.discount_application.type == 'discount_code'
        assign discount_codes = item.line_level_discount_allocations | slice: forloop.index0 | map: 'discount_application' | map: 'title' | concat: discount_codes
      endif
    endfor
  endfor
  assign discount_codes = discount_codes | uniq
%}
{% if enable_discount_code %}
  <script src="{{ 'cart-discount.js' | asset_url }}" defer="defer"></script>
{% endif %}
<div class="cart-addons flex justify-center border-top">
  {%- if cart_note -%}
    <div data-id="not" class="mini_cart_note">
      <button
        type="button"
        data-open="note"
        class="btn-reset pointer mini_cart_addon_btn show-overlay inline-flex align-center note tooltip relative"
        aria-label=" {{- 'general.cart.note.title' | t -}}"
      >
        <svg width="15" height="16" viewBox="0 0 15 16" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M10.8642 1.18519V0.691358C10.8642 0.296296 10.5679 0 10.1728 0C9.77778 0 9.48148 0.395062 9.48148 0.691358V1.08642H5.03704V0.691358C5.03704 0.395062 4.74074 0 4.34568 0C3.95062 0 3.65432 0.395062 3.65432 0.691358V1.18519C1.28395 1.38272 0 2.96296 0 5.4321V11.6543C0 14.4198 1.58025 16 4.34568 16H10.1728C12.9383 16 14.5185 14.4198 14.5185 11.6543V5.4321C14.5185 2.96296 13.2346 1.38272 10.8642 1.18519ZM4.34568 3.55556C4.64198 3.55556 5.03704 3.25926 5.03704 2.8642V2.46914H9.48148V2.8642C9.48148 3.25926 9.87654 3.55556 10.1728 3.55556C10.4691 3.55556 10.8642 3.25926 10.8642 2.8642V2.46914C12.4444 2.66667 13.1358 3.65432 13.1358 5.4321V11.6543C13.1358 13.7284 12.1481 14.6173 10.1728 14.6173H4.34568C2.27161 14.6173 1.38272 13.6296 1.38272 11.6543V5.33333C1.38272 3.55556 2.07407 2.5679 3.65432 2.37037V2.76543C3.65432 3.25926 3.95062 3.55556 4.34568 3.55556Z" fill="#111111"/>
          <path d="M4.34565 8.00019H10.1728C10.4691 8.00019 10.8642 7.7039 10.8642 7.30884C10.8642 6.91377 10.4691 6.61748 10.1728 6.61748H4.34565C3.95059 6.61748 3.6543 6.91377 3.6543 7.30884C3.6543 7.7039 4.04936 8.00019 4.34565 8.00019Z" fill="#111111"/>
          <path d="M7.30862 10.2715H4.34565C3.95059 10.2715 3.6543 10.5678 3.6543 10.9629C3.6543 11.3579 4.04936 11.6542 4.34565 11.6542H7.30862C7.60491 11.6542 7.99998 11.3579 7.99998 10.9629C7.99998 10.5678 7.60491 10.2715 7.30862 10.2715Z" fill="#111111"/>
        </svg>
        <span class="tooltip-content invisible rounded-3 absolute pointer-none">
          {{- 'general.cart.note.title' | t -}}
        </span>
      </button>
    </div>
  {%- endif -%}
  {%- if enable_gift_wrap and gift_wrap_pr.variants.size == 1 -%}
    <div data-id="gift" class="mini_cart_gift{% if gift_wrap != blank %} hidden{% endif %}">
      <button
        type="button"
        data-open="gift"
        class="btn-reset pointer mini_cart_addon_btn show-overlay inline-flex align-center gift tooltip relative"
        aria-label=" {{- 'general.cart.gift.title' | t -}}"
      >
        <svg width="17" height="16" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M9.14355 10.375C9.14355 10.0298 8.86374 9.75 8.51855 9.75C8.17337 9.75 7.89355 10.0298 7.89355 10.375V12.875C7.89355 13.2202 8.17337 13.5 8.51855 13.5C8.86374 13.5 9.14355 13.2202 9.14355 12.875V10.375Z" fill="#111111"/>
          <path d="M2.26855 9.75C1.92337 9.75 1.64355 10.0298 1.64355 10.375V12.875C1.64355 14.5981 3.04543 16 4.76855 16H12.2686C13.9917 16 15.3936 14.5981 15.3936 12.875V10.375C15.3936 10.0298 15.1137 9.75 14.7686 9.75C14.4234 9.75 14.1436 10.0298 14.1436 10.375V12.875C14.1436 13.9089 13.3024 14.75 12.2686 14.75H4.76855C3.73468 14.75 2.89355 13.9089 2.89355 12.875V10.375C2.89355 10.0298 2.61374 9.75 2.26855 9.75Z" fill="#111111"/>
          <path d="M2.39355 8.75H14.6436C15.6774 8.75 16.5186 7.90887 16.5186 6.875V5.625C16.5186 4.59113 15.6774 3.75 14.6436 3.75H12.5572C12.7707 3.38197 12.8936 2.95519 12.8936 2.5C12.8936 1.1215 11.7721 0 10.3936 0C9.6474 0 8.97702 0.329 8.51855 0.849031C8.06009 0.329 7.38971 0 6.64355 0C5.26505 0 4.14355 1.1215 4.14355 2.5C4.14355 2.95519 4.26643 3.38197 4.4799 3.75H2.39355C1.35968 3.75 0.518555 4.59113 0.518555 5.625V6.875C0.518555 7.90887 1.35968 8.75 2.39355 8.75ZM10.3936 1.25C11.0828 1.25 11.6436 1.81075 11.6436 2.5C11.6436 3.18925 11.0828 3.75 10.3936 3.75H9.14355V2.5C9.14355 1.81075 9.7043 1.25 10.3936 1.25ZM5.39355 2.5C5.39355 1.81075 5.9543 1.25 6.64355 1.25C7.3328 1.25 7.89355 1.81075 7.89355 2.5V3.75H6.64355C5.9543 3.75 5.39355 3.18925 5.39355 2.5ZM1.76855 5.625C1.76855 5.28038 2.04893 5 2.39355 5H7.89355V5.625C7.89355 5.97019 8.17337 6.25 8.51855 6.25C8.86374 6.25 9.14355 5.97019 9.14355 5.625V5H14.6436C14.9882 5 15.2686 5.28038 15.2686 5.625V6.875C15.2686 7.21962 14.9882 7.5 14.6436 7.5H2.39355C2.04893 7.5 1.76855 7.21962 1.76855 6.875V5.625Z" fill="#111111"/>
        </svg>
        <span class="tooltip-content invisible rounded-3 absolute pointer-none">
          {{- 'general.cart.gift.title' | t -}}
        </span>
      </button>
    </div>
  {%- endif -%}
  {%- if show_estimate_shipping_rates -%}
    <div data-id="shipping" class="mini_cart_shipping tooltip lh-1">
      <button
        type="button"
        data-open="shipping"
        class="btn-reset pointer mini_cart_addon_btn show-overlay inline-flex align-center shipping tooltip relative"
        aria-label="{{- 'general.cart.shipping.title' | t -}}"
      >
        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M13.7633 2.65861L9.8962 0.435045C8.83275 -0.145015 7.28593 -0.145015 6.31916 0.435045L2.45209 2.65861C1.38865 3.23867 0.518555 4.68882 0.518555 5.94562V10.1027C0.518555 11.3595 1.38865 12.713 2.45209 13.3897L6.31916 15.6133C6.80254 15.9033 7.47928 16 8.15602 16C8.83275 16 9.41282 15.9033 9.99288 15.6133L13.8599 13.3897C14.9234 12.8097 15.7935 11.3595 15.7935 10.1027V5.84894C15.6968 4.68882 14.8267 3.23867 13.7633 2.65861ZM6.80254 1.49849C7.09257 1.30514 7.57596 1.20846 8.05934 1.20846C8.54272 1.20846 8.92943 1.30514 9.31614 1.49849L13.1832 3.72205C13.4732 3.81873 13.6666 4.10876 13.8599 4.39879L11.7331 5.65559L5.7391 2.07855L6.80254 1.49849ZM2.93547 3.62538L4.57898 2.65861L10.5729 6.13897L8.05934 7.6858L2.16206 4.30212C2.35541 4.01208 2.64544 3.81873 2.93547 3.62538ZM2.93547 12.2296C2.25874 11.8429 1.582 10.7795 1.582 10.006V5.84894C1.582 5.65559 1.582 5.46224 1.67868 5.26888L7.47928 8.65257V14.6465C7.18925 14.6465 6.9959 14.5498 6.80254 14.4532L2.93547 12.2296ZM14.5367 10.006C14.5367 10.7795 13.9566 11.8429 13.1832 12.2296L9.31614 14.3565C9.12279 14.4532 8.92943 14.5498 8.6394 14.5498V8.65257L11.153 7.20242V8.84592C11.153 9.13595 11.443 9.42598 11.7331 9.42598C12.0231 9.42598 12.3131 9.13595 12.3131 8.84592V6.52568L14.44 5.26888C14.44 5.55891 14.5367 5.75227 14.5367 5.84894V10.006Z" fill="#111111"/>
        </svg>
        <span class="tooltip-content invisible rounded-3 absolute pointer-none">
          {{- 'general.cart.shipping.title' | t -}}
        </span>
      </button>
    </div>
  {%- endif -%}
  {% if enable_discount_code %}
    <div data-id="coupon" class="mini_cart_coupon tooltip lh-1">
      <button
        aria-label="{{- 'general.cart.coupon.title' | t -}}"
        type="button"
        data-open="coupon"
        class="btn-reset pointer mini_cart_addon_btn show-overlay inline-flex align-center gift tooltip relative"
      >
        <svg width="14" height="17" fill="none" class="mr-5">
          <path d="M7.29346 16C7.00706 15.9883 6.72673 15.9091 6.47284 15.7681C6.21895 15.6272 5.99788 15.428 5.82572 15.1852L5.12679 14.1481C4.98766 13.9584 4.78953 13.8272 4.56765 13.7778C4.45827 13.772 4.34932 13.7962 4.25136 13.8481C4.1534 13.9 4.06975 13.9778 4.00851 14.0741C3.03002 15.1852 2.2612 15.1111 1.84184 14.963C1.42249 14.8148 0.793457 14.2963 0.793457 12.6667V4.2963C0.793457 1.03704 1.70206 0 4.63754 0H9.94937C11.487 0 12.3257 0.222222 12.9547 0.888889C13.5838 1.55556 13.7935 2.51852 13.7935 4.2963V12.6667C13.7935 14.2963 13.1644 14.8148 12.7451 14.963C12.3257 15.1111 11.6268 15.1852 10.5784 14.0741C10.5172 13.9778 10.4335 13.9 10.3356 13.8481C10.2376 13.7962 10.1286 13.772 10.0193 13.7778C9.9045 13.786 9.79343 13.8241 9.69589 13.8887C9.59836 13.9533 9.51737 14.0424 9.46012 14.1481L8.7612 15.1111C8.60326 15.3747 8.38702 15.5934 8.13124 15.7483C7.87547 15.9032 7.58788 15.9896 7.29346 16ZM4.56765 12.6667H4.63754C4.90409 12.6807 5.16362 12.762 5.39459 12.9037C5.62555 13.0454 5.82127 13.2435 5.9655 13.4815L6.66442 14.5185C6.72711 14.6257 6.81457 14.7142 6.91854 14.7755C7.0225 14.8369 7.13952 14.869 7.25851 14.869C7.3775 14.869 7.49452 14.8369 7.59848 14.7755C7.70245 14.7142 7.78992 14.6257 7.8526 14.5185L8.55152 13.5556C8.88599 13.1219 9.35808 12.8322 9.87948 12.7407C10.1438 12.7402 10.4052 12.7993 10.6462 12.9143C10.8873 13.0292 11.1024 13.1974 11.2773 13.4074C11.8365 14 12.1859 14.0741 12.2558 14C12.3257 13.9259 12.6053 13.4815 12.6053 12.7407V4.2963C12.6053 2.88889 12.4655 2.07407 12.0461 1.62963C11.6268 1.18519 10.9978 1.11111 9.80959 1.11111H4.63754C2.33109 1.11111 1.84184 1.62963 1.84184 4.2963V12.6667C1.84184 13.4074 2.05152 13.8519 2.19131 13.9259C2.33109 14 2.68055 13.9259 3.1698 13.3333C3.34923 13.1292 3.56528 12.9651 3.80531 12.8507C4.04534 12.7362 4.30452 12.6736 4.56765 12.6667Z" fill="#111111"/>
          <path d="M5.19654 9.33342C5.06689 9.33089 4.9426 9.2782 4.84707 9.18527C4.7455 9.07701 4.68848 8.93054 4.68848 8.77786C4.68848 8.62518 4.7455 8.47871 4.84707 8.37045L9.04062 3.92601C9.08956 3.86574 9.14971 3.81682 9.2173 3.78232C9.2849 3.74782 9.35848 3.72848 9.43346 3.7255C9.50843 3.72253 9.58318 3.73599 9.65302 3.76504C9.72286 3.79409 9.78629 3.8381 9.83934 3.89432C9.89239 3.95055 9.93392 4.01777 9.96133 4.09179C9.98874 4.16581 10.0014 4.24503 9.99863 4.32449C9.99582 4.40395 9.97757 4.48193 9.94502 4.55357C9.91246 4.62521 9.86631 4.68896 9.80944 4.74082L5.61589 9.18527C5.48839 9.26743 5.34505 9.31807 5.19654 9.33342Z" fill="#111111"/>
          <path d="M9.39033 9.48148C9.20496 9.48148 9.02719 9.40344 8.89612 9.26452C8.76504 9.12561 8.69141 8.9372 8.69141 8.74074C8.69141 8.54428 8.76504 8.35587 8.89612 8.21696C9.02719 8.07804 9.20496 8 9.39033 8C9.5757 8 9.75347 8.07804 9.88455 8.21696C10.0156 8.35587 10.0893 8.54428 10.0893 8.74074C10.0893 8.9372 10.0156 9.12561 9.88455 9.26452C9.75347 9.40344 9.5757 9.48148 9.39033 9.48148Z" fill="#111111"/>
          <path d="M5.19645 5.40732C5.10307 5.41386 5.00944 5.39919 4.92185 5.36428C4.83425 5.32937 4.7547 5.27503 4.68852 5.20489C4.62235 5.13476 4.57107 5.05045 4.53813 4.95761C4.50519 4.86477 4.49135 4.76555 4.49753 4.66658C4.49135 4.56761 4.50519 4.46838 4.53813 4.37554C4.57107 4.2827 4.62235 4.19839 4.68852 4.12826C4.7547 4.05812 4.83425 4.00378 4.92185 3.96887C5.00944 3.93396 5.10307 3.91929 5.19645 3.92584C5.28983 3.91929 5.38346 3.93396 5.47106 3.96887C5.55866 4.00378 5.63821 4.05812 5.70438 4.12826C5.77056 4.19839 5.82183 4.2827 5.85477 4.37554C5.88771 4.46838 5.90155 4.56761 5.89538 4.66658C5.90155 4.76555 5.88771 4.86477 5.85477 4.95761C5.82183 5.05045 5.77056 5.13476 5.70438 5.20489C5.63821 5.27503 5.55866 5.32937 5.47106 5.36428C5.38346 5.39919 5.28983 5.41386 5.19645 5.40732Z" fill="#111111"/>
        </svg>
        <span class="tooltip-content invisible rounded-3 absolute pointer-none">
          <span>{{ 'general.cart.coupon.title' | t }}</span>
        </span>
      </button>
    </div>
  {% endif %}
</div>
{%- if cart_note -%}
  <div
    class="cart-footeraddon grey-bg absolute transition w-full z-10 p-30 left-0 bottom-0 border-top shadow pointer-none cart__addon-content"
    id="note"
  >
    <div class="addon-title flex align-center gap-10 mb-15 lh-normal">
      <svg width="17" height="19" fill="none">
        <path fill="#111" d="M12.721 1.407V.821a.796.796 0 0 0-.81-.821c-.462 0-.809.47-.809.821v.47H5.898V.82c0-.352-.347-.821-.81-.821-.462 0-.81.47-.81.821v.586C1.504 1.642 0 3.52 0 6.451v7.388C0 17.123 1.85 19 5.088 19h6.824C15.15 19 17 17.123 17 13.84V6.45c0-2.931-1.503-4.808-4.279-5.043ZM5.088 4.222c.347 0 .81-.352.81-.82v-.47h5.204v.47c0 .468.463.82.81.82.346 0 .81-.352.81-.82v-.47c1.85.235 2.659 1.408 2.659 3.519v7.388c0 2.463-1.156 3.519-3.47 3.519H5.089c-2.428 0-3.469-1.173-3.469-3.519V6.333c0-2.11.81-3.284 2.66-3.518v.469c0 .586.347.938.81.938Z"/>
        <path fill="#111" d="M5.089 9.5h6.823c.347 0 .81-.352.81-.82 0-.47-.463-.822-.81-.822H5.089a.796.796 0 0 0-.81.821c0 .47.463.821.81.821ZM8.558 12.197h-3.47a.796.796 0 0 0-.809.822c0 .469.463.82.81.82h3.47c.346 0 .809-.351.809-.82 0-.47-.463-.822-.81-.822Z"/>
      </svg>
      <span class="heading-style">{{ 'general.cart.note.title' | t }}</span>
    </div>
    <div class="form-floating textarea">
      <textarea
        class="w-full bg-white mb-20 cart-note form-control"
        placeholder="{{ 'general.cart.note.placeholder' | t }}"
      >{{ cart.note }}</textarea>
      <label class="mb-5" for="textarea">{{ 'general.cart.note.placeholder' | t }}</label>
    </div>
    <div class="addon-actions">
      <button
        type="button"
        class="btn-save w-full btn-primary mb-10"
        name="{{ 'general.cart.save' | t }}"
        data-action="note"
      >
        {{ 'general.cart.save' | t }}
      </button>
      <button type="button" class="btn-cancel w-full btn-outline" name="{{ 'general.cart.cancel' | t }}">
        {{ 'general.cart.cancel' | t }}
      </button>
    </div>
  </div>
{%- endif -%}
{%- if enable_gift_wrap and gift_wrap_pr.variants.size == 1 -%}
  <div
    class="cart-footeraddon grey-bg absolute transition w-full z-10 p-30 left-0 bottom-0 border-top shadow pointer-none cart__addon-content{% if gift_wrap != blank %} hidden{% endif %}"
    id="gift"
  >
    <div class="mini_cart-tool text-center">
      <svg width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M16.1719 19.4531C16.1719 18.8059 15.6472 18.2812 15 18.2812C14.3528 18.2812 13.8281 18.8059 13.8281 19.4531V24.1406C13.8281 24.7879 14.3528 25.3125 15 25.3125C15.6472 25.3125 16.1719 24.7879 16.1719 24.1406V19.4531Z" fill="#111111"/>
        <path d="M3.28125 18.2812C2.63402 18.2812 2.10938 18.8059 2.10938 19.4531V24.1406C2.10938 27.3715 4.73789 30 7.96875 30H22.0312C25.2622 30 27.8906 27.3715 27.8906 24.1406V19.4531C27.8906 18.8059 27.366 18.2812 26.7188 18.2812C26.0715 18.2812 25.5469 18.8059 25.5469 19.4531V24.1406C25.5469 26.0791 23.9698 27.6562 22.0312 27.6562H7.96875C6.03023 27.6562 4.45312 26.0791 4.45312 24.1406V19.4531C4.45312 18.8059 3.92848 18.2812 3.28125 18.2812Z" fill="#111111"/>
        <path d="M3.51562 16.4062H26.4844C28.4229 16.4062 30 14.8291 30 12.8906V10.5469C30 8.60836 28.4229 7.03125 26.4844 7.03125H22.5725C22.9727 6.34119 23.2031 5.54098 23.2031 4.6875C23.2031 2.10281 21.1003 0 18.5156 0C17.1166 0 15.8596 0.616875 15 1.59193C14.1404 0.616875 12.8834 0 11.4844 0C8.89969 0 6.79688 2.10281 6.79688 4.6875C6.79688 5.54098 7.02727 6.34119 7.42752 7.03125H3.51562C1.57711 7.03125 0 8.60836 0 10.5469V12.8906C0 14.8291 1.57711 16.4062 3.51562 16.4062ZM18.5156 2.34375C19.808 2.34375 20.8594 3.39516 20.8594 4.6875C20.8594 5.97984 19.808 7.03125 18.5156 7.03125H16.1719V4.6875C16.1719 3.39516 17.2233 2.34375 18.5156 2.34375ZM9.14062 4.6875C9.14062 3.39516 10.192 2.34375 11.4844 2.34375C12.7767 2.34375 13.8281 3.39516 13.8281 4.6875V7.03125H11.4844C10.192 7.03125 9.14062 5.97984 9.14062 4.6875ZM2.34375 10.5469C2.34375 9.9007 2.86945 9.375 3.51562 9.375H13.8281V10.5469C13.8281 11.1941 14.3528 11.7188 15 11.7188C15.6472 11.7188 16.1719 11.1941 16.1719 10.5469V9.375H26.4844C27.1305 9.375 27.6562 9.9007 27.6562 10.5469V12.8906C27.6562 13.5368 27.1305 14.0625 26.4844 14.0625H3.51562C2.86945 14.0625 2.34375 13.5368 2.34375 12.8906V10.5469Z" fill="#111111"/>
      </svg>
      <div class="field mt-10 max-w-custom mx-auto" style="--max-width: 85%">
        {{ 'general.cart.gift.gift_wrap_html' | t: price: gift_pr_price }}
      </div>
      <div class="addon-actions mt-25">
        <a
          href="{{ gift_wrap_pr.url }}"
          data-variant-id="{{ gift_wrap_pr.selected_or_first_available_variant.id }}"
          class="block add-giftwrap w-full btn-primary mb-10 no-underline relative"
          arial-label="{{ 'general.cart.gift.button_text' | t }}"
        >
          <span class="hidden-on-load">{{ 'general.cart.gift.button_text' | t }}</span>
          <svg width="14" height="14" viewBox="0 0 14 14" fill="none" class="spin opacity-0 icon-load">
            <use href="#icon-load"></use>
          </svg>
        </a>
        <button type="button" class="btn-cancel w-full btn-outline" name="{{ 'general.cart.cancel' | t }}">
          {{ 'general.cart.cancel' | t }}
        </button>
      </div>
    </div>
  </div>
{%- endif -%}
{%- if show_estimate_shipping_rates -%}
  <div
    class="cart-footeraddon grey-bg absolute transition w-full z-10 p-30 left-0 bottom-0 border-top shadow pointer-none cart__addon-content"
    id="shipping"
  >
    <div class="addon-title mb-20 flex align-center gap-10 lh-normal">
      <svg width="18" height="19" viewBox="0 0 18 19" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M15.6076 3.1571L11.0506 0.516616C9.79747 -0.172205 7.97468 -0.172205 6.83544 0.516616L2.27848 3.1571C1.02532 3.84592 0 5.56798 0 7.06042V11.997C0 13.4894 1.02532 15.0967 2.27848 15.9003L6.83544 18.5408C7.40506 18.8852 8.20253 19 9 19C9.79747 19 10.481 18.8852 11.1646 18.5408L15.7215 15.9003C16.9747 15.2115 18 13.4894 18 11.997V6.94562C17.8861 5.56798 16.8608 3.84592 15.6076 3.1571ZM7.40506 1.77946C7.74684 1.54985 8.31646 1.43505 8.88608 1.43505C9.4557 1.43505 9.91139 1.54985 10.3671 1.77946L14.9241 4.41994C15.2658 4.53474 15.4937 4.87915 15.7215 5.22357L13.2152 6.71601L6.1519 2.46828L7.40506 1.77946ZM2.8481 4.30514L4.78481 3.1571L11.8481 7.29003L8.88608 9.12689L1.93671 5.10876C2.16456 4.76435 2.50633 4.53474 2.8481 4.30514ZM2.8481 14.5227C2.05063 14.0634 1.25316 12.8006 1.25316 11.8822V6.94562C1.25316 6.71601 1.25316 6.48641 1.36709 6.2568L8.20253 10.2749V17.3927C7.86076 17.3927 7.63291 17.2779 7.40506 17.1631L2.8481 14.5227ZM16.519 11.8822C16.519 12.8006 15.8354 14.0634 14.9241 14.5227L10.3671 17.0483C10.1392 17.1631 9.91139 17.2779 9.56962 17.2779V10.2749L12.5316 8.55287V10.5045C12.5316 10.8489 12.8734 11.1934 13.2152 11.1934C13.557 11.1934 13.8987 10.8489 13.8987 10.5045V7.74925L16.4051 6.2568C16.4051 6.60121 16.519 6.83082 16.519 6.94562V11.8822Z" fill="#111111"/>
      </svg>
      <span class="heading-style">{{ 'general.cart.shipping.estimate_shipping_title' | t }}</span>
    </div>
    <div class="addon-content mb-30">
      <div data-address="root">
        <div class="form-group mb-15 flex flex-column">
          <label class="mb-5" for="address_country">{{ 'customer.addresses.country' | t }}</label>
          <select
            id="address_country"
            class="form-control"
            name="address[country]"
            data-default="United States"
          >
            {{ country_option_tags }}
          </select>
        </div>
        <div id="address_province_container" class="address_province_container form-group mb-15 flex flex-column">
          <label class="mb-5" for="address_province">{{ 'customer.addresses.province' | t }}</label>
          <select
            id="address_province"
            class="form-control"
            name="address[province]"
            data-default="{{ form.province }}"
          ></select>
        </div>
        <div class="form-group flex flex-column">
          <label class="mb-5" for="AddressZip">{{ 'customer.addresses.zip' | t }}</label>
          <div class="form-floating">
            <input
              type="text"
              class="form-control w-full"
              id="AddressZip"
              name="address[zip]"
              value="{{ form.zip }}"
              autocapitalize="characters"
              placeholder="{{ 'customer.addresses.zip' | t }}"
            >
            <label class="mb-5" for="AddressZip">{{ 'customer.addresses.zip' | t }}</label>
          </div>
        </div>
      </div>
    </div>
    <div class="addon-actions">
      <button type="button" class="btn-primary btn-save w-full mb-10" data-action="shipping">
        {{ 'general.cart.shipping.estimate_shipping_button' | t }}
      </button>
      <button type="button" class="btn-cancel btn-outline w-full">{{ 'general.cart.cancel' | t }}</button>
    </div>
    <div
      class="addon-message mt-15 rich__text-m0"
      data-show-delivery-days="{{ show_delivery_day }}"
      data-delivery-day-one="{{ 'general.cart.delivery_days.one' | t }}"
      data-delivery-days-other="{{ 'general.cart.delivery_days.other' | t }}"
    ></div>
  </div>
{%- endif -%}
{% if enable_discount_code %}
  <minicart-discount
    class="cart-footeraddon grey-bg absolute transition w-full z-10 p-30 left-0 bottom-0 border-top shadow pointer-none cart__addon-content"
    id="coupon"
  >
    <div class="addon-title flex align-center gap-10 mb-10 lh-normal">
      <svg width="17" height="19" viewBox="0 0 17 19" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M12.7211 1.40741V0.820988C12.7211 0.351852 12.3741 0 11.9116 0C11.449 0 11.102 0.469136 11.102 0.820988V1.29012H5.89796V0.820988C5.89796 0.469136 5.55102 0 5.08844 0C4.62585 0 4.27891 0.469136 4.27891 0.820988V1.40741C1.5034 1.64198 0 3.51852 0 6.45062V13.8395C0 17.1235 1.85034 19 5.08844 19H11.9116C15.1497 19 17 17.1235 17 13.8395V6.45062C17 3.51852 15.4966 1.64198 12.7211 1.40741ZM5.08844 4.22222C5.43537 4.22222 5.89796 3.87037 5.89796 3.40123V2.9321H11.102V3.40123C11.102 3.87037 11.5646 4.22222 11.9116 4.22222C12.2585 4.22222 12.7211 3.87037 12.7211 3.40123V2.9321C14.5714 3.16667 15.381 4.33951 15.381 6.45062V13.8395C15.381 16.3025 14.2245 17.358 11.9116 17.358H5.08844C2.65986 17.358 1.61905 16.1852 1.61905 13.8395V6.33333C1.61905 4.22222 2.42857 3.04938 4.27891 2.81481V3.28395C4.27891 3.87037 4.62585 4.22222 5.08844 4.22222Z" fill="#111111"/>
        <path d="M5.08833 9.50024H11.9115C12.2584 9.50024 12.721 9.14838 12.721 8.67925C12.721 8.21011 12.2584 7.85826 11.9115 7.85826H5.08833C4.62575 7.85826 4.27881 8.21011 4.27881 8.67925C4.27881 9.14838 4.74139 9.50024 5.08833 9.50024Z" fill="#111111"/>
        <path d="M8.55772 12.1974H5.08833C4.62575 12.1974 4.27881 12.5493 4.27881 13.0184C4.27881 13.4875 4.74139 13.8394 5.08833 13.8394H8.55772C8.90466 13.8394 9.36724 13.4875 9.36724 13.0184C9.36724 12.5493 8.90466 12.1974 8.55772 12.1974Z" fill="#111111"/>
      </svg>
      <span class="bold">{{ 'general.cart.coupon.title' | t }}</span>
    </div>
    <form
      on:submit="/applyDiscount"
      onsubmit="return false;"
    >
      <div id="coupon-messages" class="mb-4">
        <p className="my-4"></p>
      </div>
      <input
        placeholder="{{ 'general.cart.coupon.enter_discount_code' | t }}"
        type="text"
        required
        name="discount"
        class="bls__discount_code discount_code w-full bg-white {% if discount_codes.size == 0 %}mb-20{% endif %} cart-coupon"
      >
      <ul class="cart-discount__codes list-unstyled flex gap-10 {% if discount_codes.size > 0 %}mt-10 mb-10{% endif %} fs-14 heading-color">
        {% for discount_code in discount_codes %}
          <li
            class="cart-discount__item inline-flex align-center gap-5"
            data-discount-code="{{ discount_code }}"
          >
            <p class="cart-discount-code m-0 inline-flex align-center gap-5">
              <svg
                aria-hidden="true"
                focusable="false"
                class="icon icon-discount w-custom"
                style="--custom-width: 12px;"
                viewBox="0 0 12 12"
              >
                  <path fill-rule="evenodd" clip-rule="evenodd" d="M7 0h3a2 2 0 012 2v3a1 1 0 01-.3.7l-6 6a1 1 0 01-1.4 0l-4-4a1 1 0 010-1.4l6-6A1 1 0 017 0zm2 2a1 1 0 102 0 1 1 0 00-2 0z" fill="currentColor">
                </path>
              </svg>
              {{ discount_code }}
            </p>
            <button
              type="button"
              on:click="/removeDiscount"
              class="cart-discount-remove btn p-0 inline-flex align-center relative"
            >
              <svg width="16" height="16" class="heading-color remove-icon hidden-on-load">
                <use href="#icon-trash" />
              </svg>
              <div class="icon-rotator"></div>
            </button>
          </li>
        {% endfor %}
      </ul>
      <button
        type="submit"
        class="btn-save w-full btn-primary mb-10 relative"
        name="{{ 'collections.sidebar.apply' | t }}"
      >
        <span class="hidden-on-load">
          {{ 'collections.sidebar.apply' | t }}
        </span>
        <svg width="14" height="14" viewBox="0 0 14 14" fill="none" class="spin opacity-0 icon-load">
          <use href="#icon-load"></use>
        </svg>
      </button>
    </form>
    <div class="addon-actions">
      <button type="button" class="btn-cancel w-full btn-outline" name="{{ 'general.cart.cancel' | t }}">
        {{ 'general.cart.cancel' | t }}
      </button>
    </div>
  </minicart-discount>
{% endif %}