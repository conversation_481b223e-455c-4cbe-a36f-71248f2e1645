/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "banner": {
      "type": "main-collection-heading",
      "blocks": {
        "breadcrumb_MJHLQb": {
          "type": "breadcrumb",
          "settings": {}
        },
        "heading_fntt9G": {
          "type": "heading",
          "settings": {
            "page_title": "",
            "header_size": "default"
          }
        },
        "description_FAmcR8": {
          "type": "description",
          "settings": {}
        }
      },
      "block_order": [
        "breadcrumb_MJHLQb",
        "heading_fntt9G",
        "description_FAmcR8"
      ],
      "settings": {
        "section_width": "stretch_width",
        "color_scheme": "",
        "alignment": "center",
        "show_collection_image": false,
        "overlay_opacity": 0,
        "height": "adapt",
        "padding_top": 80,
        "padding_bottom": 0
      }
    },
    "subcollection": {
      "type": "subcollection-list",
      "settings": {
        "section_width": "fluid_container",
        "color_scheme": "",
        "heading": "",
        "description": "",
        "header_size": "medium",
        "header_alignment": "left",
        "image_ratio": "adapt",
        "custom_ratio": "",
        "items_per_row": 6,
        "column_gap": 30,
        "collection_information_position": "below_image",
        "design": "default",
        "text_alignment": "left",
        "show_product_count": false,
        "font_size": 14,
        "font_weight": "heading_weight",
        "information_spacing": 12,
        "rounded_image": false,
        "image_custom_width": false,
        "image_width": 200,
        "show_arrow": true,
        "carousel_pagination": "show_dots_on_mobile",
        "infinite": false,
        "autoplay": false,
        "autorotate_speed": 5,
        "reveal": false,
        "items_per_row_mobile": 2,
        "padding_top": 0,
        "padding_bottom": 0
      }
    },
    "main": {
      "type": "main-collection-product",
      "blocks": {
        "categories_LYDr6p": {
          "type": "categories",
          "settings": {
            "title": "Products Category",
            "menu": "products-category"
          }
        },
        "filter_giBtHK": {
          "type": "filter",
          "settings": {}
        },
        "popular_product_Xwjqcf": {
          "type": "popular_product",
          "settings": {
            "title": "Featured product",
            "collection": "shorts",
            "items_to_show": 3
          }
        },
        "image_banner_UUJxtT": {
          "type": "image_banner",
          "settings": {
            "image": "shopify://shop_images/sidebar_banner.jpg",
            "url": "shopify://collections",
            "open_link": true
          }
        }
      },
      "block_order": [
        "categories_LYDr6p",
        "filter_giBtHK",
        "popular_product_Xwjqcf",
        "image_banner_UUJxtT"
      ],
      "settings": {
        "section_width": "fluid_container",
        "enable_filtering": true,
        "show_filter_counts": true,
        "default_filter": "open_all",
        "filter_layout": "vertical",
        "vertical_posion": "left",
        "number_products_per_page": true,
        "sorting": true,
        "grid_list": true,
        "items_to_show": 11,
        "items_per_row": 3,
        "column_gap": 30,
        "pagination": "default",
        "button_show_more": "Load more",
        "promotion_image": "shopify://shop_images/bannrx-category.jpg",
        "promotion_link": "shopify://collections/best-seller",
        "promotion_postion": 6,
        "items_per_row_mobile": "2",
        "padding_top": 0,
        "padding_bottom": 0
      }
    }
  },
  "order": [
    "banner",
    "subcollection",
    "main"
  ]
}
