{{ 'banner.css' | asset_url | stylesheet_tag }}
{%- liquid
  assign section_st = section.settings
  assign section_width = ''

  assign heading = section_st.heading
  assign description = section_st.description
  assign header_size = ''
  if section_st.header_size == 'small'
    assign header_size = 'h3'
  elsif section_st.header_size == 'large'
    assign header_size = 'h1-size'
  endif
  if section_st.section_width == 'full_width overflow-hidden'
    assign reset_spacing = ' remove_spacing'
  else
    assign section_width = section_st.section_width
  endif
  assign column_gap = section_st.column_gap
  assign content_background_color = section_st.content_background_color
  assign items_per_row_mobile = section_st.items_per_row_mobile
  assign carousel_on_mobile = section_st.carousel_on_mobile
  if section_st.reset_spacing
    assign reset_spacing = ' remove_spacing'
  endif
  if section_st.items_per_row_mobile > 1 and section_st.items_per_row_mobile < 2
    assign data_free_scroll = 'true'
  endif
  if section_st.items_per_row_mobile == 1
    assign w_mobile = 'w-full'
  elsif section_st.items_per_row_mobile == 2
    assign w_mobile = 'w-50-percent'
  endif
  assign scroll_animation = settings.scroll_animation
-%}
{%- capture style -%}
  --section-pt: {{ section_st.padding_top }}; --section-pb: {{ section_st.padding_bottom }};{% if column_gap < 31 %}--col-gap: {{ column_gap }}px;{% else %}--col-gap: 15px;--col-gap-desktop: {{  column_gap }}px;{% endif %}
{%- endcapture -%}
<div
  class="section{% if section_st.padding_top < 30 %} pt-min{% endif %}{% if section_st.padding_bottom < 30 %} pb-min{% endif %} sec__multi-content gradient {{ reset_spacing }}"
  style="{{ style | strip | strip_newlines }}"
>
  <div class="{{ section_width }}">
    {%- if heading != blank or description != blank -%}
      <div class="section__header mb-33 mb-sm-20 text-{{ section_st.header_alignment }}{% if section_st.section_width == 'full_width' %} px-20{% endif %}">
        {%- if section_st.heading != blank -%}
          <motion-element
            data-motion="fade-up-lg"
            data-motion-delay="50"
            {% if scroll_animation != 'slide_in' %}
              hold
            {% endif %}
            class="block  {% if scroll_animation != 'none' %} scroll-trigger {{ scroll_animation }}{% endif %}"
            style="
              {%- if scroll_animation != 'none' -%}
                --animation-order: 0;
              {% endif %}
            "
          >
            <h2
              class="section__header-heading heading-letter-spacing {{ header_size  }} mt-0{% if section_st.description != blank %} mb-10{% else %} mb-0{% endif %}"
            >
              {{ section_st.heading }}
            </h2>
          </motion-element>
        {% endif %}
        {%- if section_st.description != blank -%}
          <motion-element
            data-motion="fade-up-lg"
            data-motion-delay="150"
            {% if scroll_animation != 'slide_in' %}
              hold
            {% endif %}
            class="section__header-des block {% if scroll_animation != 'none' %} scroll-trigger {{ scroll_animation }}{% endif %} rich__text-m0"
            style="
              {%- if scroll_animation != 'none' -%}
                --animation-order: 1
              {% endif %}
            "
          >
            {{ section_st.description }}
          </motion-element>
        {% endif %}
      </div>
    {% endif %}
    {%- if section.blocks.size > 0 -%}
      <multi-content
        data-enable="{{ carousel_on_mobile }}"
        data-mobile="{{ section_st.items_per_row_mobile }}"
        data-spacing="{{ section_st.column_gap }}"
        class="{% if section_st.items_per_row_mobile > 1 and section_st.items_per_row_mobile < 2 %} grid_scroll {% endif %}multi-content-js relative flex flex-col-custom flex-wrap{% if column_gap > 30 %} row-gap-30{% else %} row-gap{% endif %}{% if section.blocks.size == 2 and column_gap == 0 %}{% if  carousel_on_mobile == false %} rounded {% else %} rounded-md{% endif %}{% endif %}"
        style="--col-number: {{ section_st.items_per_row_mobile }};--swiper-pagination-mt: 2rem;--content_bg_color: {{ content_background_color }};"
        {% if section_st.items_per_row_mobile > 1 and section_st.items_per_row_mobile < 2 %}
          data-free-scroll="{{ data_free_scroll }}"
        {% endif %}
      >
        {%- for block in section.blocks -%}
          {%- liquid
            assign block_st = block.settings
            assign color_scheme = block_st.color_scheme
            assign colunm = 1
            assign padding = 0
            assign content_width = block_st.content_width | remove: '%' | times: 1
            if content_width < 100
              assign colunm = 100 | divided_by: content_width
              assign padding = block_st.content_padding_block
            endif
          -%}
          <motion-element
            data-motion="fade-up-lg"
            {% if scroll_animation != 'slide_in' or block.type == 'text' %}
              hold
            {% endif %}
            data-motion-delay="{{ forloop.index0 | times: 50 }}"
            class="{% if block.type == 'text' and block_st.collection.products.size > 0 %}background-block {% endif %}section__block {{ settings.hover_effect }}{% if scroll_animation != 'none' and block.type != 'text' and block_st.content_width != "100%" %} scroll-trigger {{ scroll_animation }}{% endif %} {{ w_mobile }} col-md-w-custom {% if section_st.equal_height_adjustment != true %}align-self-{{ block_st.vertical_align }}{% endif %}{% if block_st.content_width == '20%' or block_st.content_width == '25%' or block_st.content_width == '66.6666%' or block_st.content_width == '75%' or block_st.content_width == '80%' %} col-md-w-custom-50{% endif %}"
            style="
              --col-width: {{ block_st.content_width }};
              {%- if scroll_animation != 'none' and block_st.content_width != "100%" -%}
                --animation-order: {{ forloop.index }}
              {% endif %}
            "
            {{ block.shopify_attributes }}
          >
            {%- case block.type -%}
              {%- when 'image_with_text_overlay' -%}
                <div
                  class="section__block-inner gradient{% if content_width < 75 %} hover-effect{% endif %} color-{{ color_scheme }} flex relative{% if block_st.content_below_image %} flex-column flex-md-row text-below-mb{% endif %}{% if section_st.equal_height_adjustment %} h-full{% endif %}"
                  style="{%- if block_st.image != blank -%}--aspect-ratio: {{ block_st.image.aspect_ratio }};{%- else -%}--aspect-ratio: 5/2;{% endif %}{%- if block_st.mobile_image -%}--aspect-ratio-mb: {{ block_st.mobile_image.aspect_ratio }};{% endif %}"
                >
                  <div
                    class="banner__media w-full overlay-bg{% if section.blocks.size == 2 and column_gap == 0 %} remove-rounded{% endif %} rounded{% if block_st.content_below_image %} absolute-md mb-30 mb-md-0{% else %} absolute{% endif %} inset-0"
                    style="{%- if block_st.image != blank -%}--aspect-ratio: {{ block_st.image.aspect_ratio }};{%- else -%}--aspect-ratio: 5/2; {% endif %}--overlay-opacity: {{ block_st.image_overlay_opacity }}%; {%- if block_st.mobile_image -%} --aspect-ratio-mb: {{ block_st.mobile_image.aspect_ratio }};{% endif %}"
                  >
                    {% liquid
                      assign image = block_st.image
                      assign mobile_image = block_st.mobile_image | default: image
                    %}
                    {%- if image != blank or mobile_image != blank -%}
                      {%- assign image_alt = image.alt | escape | default: 'Images' -%}
                      {% render 'responsive-image',
                        type: 'banner',
                        container: section_width,
                        colunm: colunm,
                        padding: padding,
                        colunm_mobile: items_per_row_mobile,
                        image: image,
                        image_mobile: mobile_image,
                        image_alt: image_alt
                      %}
                    {%- else -%}
                      {%- render 'placeholder-render' -%}
                    {% endif %}
                    {% if block_st.image_link != blank %}
                      <a
                        class="absolute inset-0 z-2 block hidden-md"
                        aria-label="{{ block_st.heading }}"
                        href="{{ block_st.image_link }}"
                      ></a>
                    {% endif %}
                  </div>
                  <div
                    class="sec__content w-full flex {{ block_st.content_position }} text-{{ block_st.content_alignment }}{% if section_st.equal_height_adjustment %} align-self-{{ block_st.vertical_align }}{% endif %}"
                    style="--padding-inline: {{ block_st.content_padding_inline }};--padding-block: {{ block_st.content_padding_block }};"
                  >
                    <div class="sec__content-inner py-custom px-custom relative{% if block_st.content_padding_inline < 35 %} x-min-value{% endif %}{% if block_st.content_padding_block < 35 %} y-min-value{% endif %}{% if block_st.content_below_image %} w-full{% endif %}">
                      {%- if block_st.custom_svg != blank -%}
                        <div
                          class="sec__content-custom-svg {% if block_st.subheading_spacing_bottom > 41 %} mb-big{% elsif block_st.subheading_spacing_bottom > 30 %} mb-medium {% else %} mb-custom{% endif %}"
                          style="--space-bottom: {{ block_st.custom_svg_spacing_bottom }}"
                        >
                          {{ block_st.custom_svg }}
                        </div>
                      {% endif %}
                      {%- if block_st.subheading != blank -%}
                        <div
                          class="sec__content-subheading heading-color heading fs-custom {{ block_st.subheading_font_weight }}{% if block_st.subheading_spacing_bottom > 41 %} mb-big{% elsif block_st.subheading_spacing_bottom > 30 %} mb-medium {% else %} mb-custom{% endif %}"
                          style="--font-size: {{ block_st.subheading_font_size }};--space-bottom: {{ block_st.subheading_spacing_bottom }}"
                        >
                          {{ block_st.subheading | escape }}
                        </div>
                      {% endif %}
                      {%- if block_st.heading != blank -%}
                        <h2
                          class="sec__content-heading heading-letter-spacing mt-0{% if block_st.heading_spacing_bottom > 41 %} mb-big{% elsif block_st.heading_spacing_bottom > 30 %} mb-medium {% else %} mb-custom{% endif %}{% if block_st.heading_uppercase %} uppercase{% endif %}{% if block_st.heading_font_size > 41 %} fs-big{% elsif block_st.heading_font_size > 24 %} fs-medium {% else %} fs-custom{% endif %} {{ block_st.heading_font_weight }}"
                          style="--font-size: {{ block_st.heading_font_size }};--space-bottom: {{ block_st.heading_spacing_bottom }}"
                          {{ block.shopify_attributes }}
                        >
                          {{ block_st.heading }}
                        </h2>
                      {% endif %}
                      {%- if block_st.description != blank -%}
                        <div
                          class="sec__content-des rich__text-m0 {% if block_st.des_spacing_bottom > 41 %} mb-big{% elsif block_st.des_spacing_bottom > 30 %} mb-medium {% else %} mb-custom{% endif %}{% if block_st.des_font_size > 24 %} fs-medium {% else %} fs-custom{% endif %} {{ block_st.des_font_weight }}"
                          style="--font-size: {{ block_st.des_font_size }};--space-bottom: {{ block_st.des_spacing_bottom }}"
                          {{ block.shopify_attributes }}
                        >
                          {{ block_st.description }}
                        </div>
                      {% endif %}
                      {% if block_st.first_button_label != blank or block_st.second_button_label != blank %}
                        <div class="sec__content-btn align-center flex flex-wrap gap-15 justify-content-{{ block_st.content_alignment }}">
                          {% if block_st.first_button_label != blank %}
                            <a
                              {% if block_st.first_button_link == blank %}
                                role="link" aria-disabled="true"
                              {% else %}
                                href="{{ block_st.first_button_link | default: "#" }}"
                              {% endif %}
                              aria-label="{{ block_st.first_button_label }}"
                              class="relative z-3 inline-flex no-underline btn-{{ block_st.first_button_type }} px-md-20 py-md-10 p-lg-content-btn"
                            >
                              {{ block_st.first_button_label }}
                            </a>
                          {% endif %}
                          {% if block_st.second_button_label != blank %}
                            <a
                              {% if block_st.second_button_link == blank %}
                                role="link" aria-disabled="true"
                              {% else %}
                                href="{{ block_st.second_button_link | default: "#" }}"
                              {% endif %}
                              aria-label="{{ block_st.second_button_label }}"
                              class="relative z-3 inline-flex no-underline btn-{{ block_st.second_button_type }} px-md-20 py-md-10 p-lg-content-btn"
                            >
                              {{ block_st.second_button_label }}
                            </a>
                          {% endif %}
                        </div>
                      {% endif %}
                    </div>
                    {% if block_st.image_link != blank %}
                      <a
                        class="absolute inset-0 z-2 hidden block-md"
                        href="{{ block_st.image_link }}"
                        aria-label="{{ block_st.heading }}"
                      ></a>
                    {% endif %}
                  </div>
                </div>
              {%- when 'image' -%}
                <div class="{% if section.blocks.size == 2 and column_gap == 0 %} remove-rounded {% endif %}rounded {{ settings.hover_effect }}{% if section_st.equal_height_adjustment %} h-full{% endif %}">
                  <a
                    class="block banner__media{% if section.blocks.size == 2 and column_gap == 0 %} remove-rounded{% endif %} rounded{% if content_width < 50 %} hover-effect{% endif %}{% if section_st.equal_height_adjustment %} h-full{% endif %}"
                    {% if block_st.image_link != blank %}
                      href="{{ block_st.image_link }}"
                      aria-label="{{ block_st.heading }}"
                    {% else %}
                      role="link"
                    {% endif %}
                    style=" --aspect-ratio: {{ block_st.image.aspect_ratio }}{%- if block_st.image == blank -%} 16/9 {% endif %}"
                  >
                    {%- if block_st.image != blank -%}
                      {% liquid
                        assign image_alt = block_st.image.alt | escape | default: 'Images'
                      %}
                      {% render 'responsive-image',
                        type: 'banner',
                        container: section_width,
                        colunm: colunm,
                        padding: padding,
                        colunm_mobile: items_per_row_mobile,
                        image: block_st.image,
                        image_alt: image_alt
                      %}
                    {%- else -%}
                      {%- render 'placeholder-render' -%}
                    {% endif %}
                  </a>
                </div>
              {%- when 'text' -%}
                <div class="p-20 py-30 section__block-inner{% if section.blocks.size == 2 and column_gap == 0 %} remove-rounded{% endif %} gradient color-{{ color_scheme }}{% if section_st.equal_height_adjustment %} h-full flex align-{{ block_st.vertical_align}} rounded{% endif %} {% if block_st.image_link != blank %} relative{% endif %}">
                  <div
                    class="sec__content w-full text-{{ block_st.content_alignment }}{% if section_st.equal_height_adjustment %} p-30{% endif %}"
                    style="--padding-inline: {{ block_st.content_padding_inline }};--padding-block: {{ block_st.content_padding_block }};"
                  >
                    <div class="sec__content-inner py-custom px-custom">
                      {%- if block_st.subheading != blank -%}
                        <motion-element
                          data-motion="fade-up-lg"
                          {% if scroll_animation != 'slide_in' %}
                            hold
                          {% endif %}
                          data-motion-delay="{{  forloop.index0  | times: 50 }}"
                          class="sec__content-subheading block heading-color  {% if scroll_animation == 'slide_in' %} scroll-trigger {{ scroll_animation }}{% endif %} {% if block_st.subheading_spacing_bottom > 41 %} mb-big{% elsif block_st.subheading_spacing_bottom > 30 %} mb-medium {% else %} mb-custom{% endif %} fs-custom {{ block_st.subheading_font_weight }}"
                          style="--font-size: {{ block_st.subheading_font_size }};--space-bottom: {{ block_st.subheading_spacing_bottom }}"
                        >
                          {{ block_st.subheading | escape }}
                        </motion-element>
                      {% endif %}
                      {%- if block_st.heading != blank -%}
                        <motion-element
                          data-motion="fade-up-lg"
                          {% if scroll_animation != 'slide_in' %}
                            hold
                          {% endif %}
                          data-motion-delay="{{  forloop.index0  | times: 150 }}"
                          class="block {% if scroll_animation == 'slide_in' %} scroll-trigger {{ scroll_animation }}{% endif %}"
                        >
                          <h2
                            class="sec__content-heading heading-letter-spacing mt-0{% if block_st.heading_spacing_bottom > 41 %} mb-big{% elsif block_st.heading_spacing_bottom > 30 %} mb-medium {% else %} mb-custom{% endif %}{% if block_st.heading_uppercase %} uppercase{% endif %}{% if block_st.heading_font_size > 41 %} fs-big{% elsif block_st.heading_font_size > 24 %} fs-medium {% else %} fs-custom{% endif %} {{ block_st.heading_font_weight }}"
                            style="--font-size: {{ block_st.heading_font_size }};--space-bottom: {{ block_st.heading_spacing_bottom }}"
                            {{ block.shopify_attributes }}
                          >
                            {{ block_st.heading }}
                          </h2>
                        </motion-element>
                      {% endif %}
                      {%- if block_st.description != blank -%}
                        <motion-element
                          data-motion="fade-up-lg"
                          {% if scroll_animation != 'slide_in' %}
                            hold
                          {% endif %}
                          data-motion-delay="{{  forloop.index0  | times: 200 }}"
                          class="sec__content-des block  {% if scroll_animation == 'slide_in' %} scroll-trigger {{ scroll_animation }}{% endif %} rich__text-m0 {% if block_st.des_spacing_bottom > 41 %} mb-big{% elsif block_st.des_spacing_bottom > 30 %} mb-medium {% else %} mb-custom{% endif %}{% if block_st.des_font_size > 24 %} fs-medium {% else %} fs-custom{% endif %} {{ block_st.des_font_weight }}"
                          style="--font-size: {{ block_st.des_font_size }};--space-bottom: {{ block_st.des_spacing_bottom }}"
                          {{ block.shopify_attributes }}
                        >
                          {{ block_st.description }}
                        </motion-element>
                      {% endif %}
                      {% if block_st.collection.products.size > 0 %}
                        <slide-section
                          class="swiper"
                          data-section-id="{{ block.id }}"
                          data-loop="true"
                          data-mobile="1"
                          data-desktop="1"
                          data-pagination-progressbar="false"
                          data-arrow-centerimage="1"
                        >
                          <div class="swiper-wrapper">
                            {% for product in block_st.collection.products %}
                              <div class="swiper-slide block-text-product__item">
                                <div class="block-text-product__infor flex align-center gap-10 text-left">
                                  <div
                                    class="block-text-product__infor-image global-media-settings w-custom fs-0 relative"
                                    style="--custom-width: 6.5rem;"
                                  >
                                    <a
                                      href="{{- product.url -}}"
                                      class="rounded-5 fs-0 "
                                      style=" --aspect-ratio: 1/1;"
                                      aria-label="{{ product.title | escape }}"
                                    >
                                      {%- assign image_alt = product.featured_media.alt
                                        | default: product.title
                                        | escape
                                      -%}
                                      {% render 'responsive-image',
                                        type: 'product',
                                        image: product.featured_media,
                                        image_alt: image_alt,
                                        class: 'rounded-5',
                                        custom_widths: '550, 480, 360, 160, 90',
                                        no_animate: true
                                      %}
                                    </a>
                                  </div>
                                  <h3 class="block-text-product__infor--name my-0 text-size flex-1">
                                    <a
                                      class="no-underline heading-color"
                                      href="{{- product.url -}}"
                                      aria-label="{{ product.title | escape }}"
                                    >
                                      {{- product.title | escape -}}
                                    </a>
                                  </h3>
                                  <div class="block-text-product__price">
                                    {%- render 'price', scope: 'item', product: product, show_badges: false -%}
                                  </div>
                                </div>
                              </div>
                            {% endfor %}
                          </div>
                          <div
                            class="swiper-pagination  flex flex-wrap px-15 lh-1 bottom-30 mb-20 justify-content-center"
                            style="--swiper-pagination-bottom: 3rem;--swiper-pagination-position: static;z-index: 2"
                          ></div>
                        </slide-section>
                      {% endif %}
                      {{ 'countdown.css' | asset_url | stylesheet_tag }}
                      {% if block_st.timer != blank %}
                        {%- liquid
                          assign fs_custom = ''
                          if block_st.font_size > 41
                            assign fs_custom = 'fs-big'
                          elsif block_st.font_size > 24
                            assign fs_custom = 'fs-medium'
                          else
                            assign fs_custom = 'fs-custom'
                          endif
                          assign mb_custom = ''
                          if block_st.spacing_bottom > 41
                            assign mb_custom = 'mb-big'
                          elsif block_st.spacing_bottom > 30
                            assign mb_custom = 'mb-medium'
                          else
                            assign mb_custom = 'mb-custom'
                          endif
                        -%}
                        <motion-element
                          data-motion="fade-up-lg"
                          {% if scroll_animation != 'slide_in' %}
                            hold
                          {% endif %}
                          data-motion-delay="{{  forloop.index0  | times: 250 }}"
                          class="timer block {{ block_st.style }} {% if scroll_animation != 'none' %} scroll-trigger {{ scroll_animation }} {% endif %} {{ mb_custom }} {{ fs_custom }}"
                          style="
                            --font-size: {{ block_st.font_size }};--space-bottom: {{ block_st.spacing_bottom }};{%- if scroll_animation != 'none' -%}
                              --animation-order: {{  forloop.index }}
                            {% endif %}
                          "
                        >
                          <countdown-timer
                            class="hidden inline-flex flex-wrap  heading-style justify-content-md-{{ section_st.content_alignment }}"
                            data-endtime="{{ block_st.timer }}"
                            data-timeout-message="{{ block_st.expired_message }}"
                            data-format="dd:hh:mm:ss"
                            data-days="{{ 'sections.times_bn.days' | t }}"
                            data-hours="{{ 'sections.times_bn.hour' | t }}"
                            data-mins="{{ 'sections.times_bn.mins' | t }}"
                            data-secs="{{ 'sections.times_bn.secs' | t }}"
                          >
                          </countdown-timer>
                        </motion-element>
                      {% endif %}
                      {% if block_st.first_button_label != blank or block_st.second_button_label != blank %}
                        <motion-element
                          data-motion="fade-up-lg"
                          {% if scroll_animation != 'slide_in' %}
                            hold
                          {% endif %}
                          data-motion-delay="{{  forloop.index0  | times: 300 }}"
                          class="sec__content-btn {% if scroll_animation == 'slide_in' %} scroll-trigger {{ scroll_animation }}{% endif %} align-center flex flex-wrap gap-15 justify-content-{{ block_st.content_alignment }} relative z-3"
                        >
                          {% if block_st.first_button_label != blank %}
                            <a
                              {% if block_st.first_button_link == blank %}
                                role="link" aria-disabled="true"
                              {% else %}
                                href="{{ block_st.first_button_link | default: "#" }}"
                              {% endif %}
                              aria-label="{{ block_st.first_button_label }}"
                              class="inline-flex no-underline btn-{{ block_st.first_button_type }} px-md-20 py-md-10 p-lg-content-btn"
                            >
                              {{ block_st.first_button_label }}
                            </a>
                          {% endif %}
                          {% if block_st.second_button_label != blank %}
                            <a
                              {% if block_st.second_button_link == blank %}
                                role="link" aria-disabled="true"
                              {% else %}
                                href="{{ block_st.second_button_link | default: "#" }}"
                              {% endif %}
                              aria-label="{{ block_st.second_button_label }}"
                              class="inline-flex no-underline btn-{{ block_st.second_button_type }} px-md-20 py-md-10 p-lg-content-btn"
                            >
                              {{ block_st.second_button_label }}
                            </a>
                          {% endif %}
                        </motion-element>
                      {% endif %}
                    </div>
                  </div>
                  {% if block_st.image_link != blank %}
                    <a
                      class="absolute inset-0 z-2"
                      aria-label="{{ block_st.heading }}"
                      href="{{ block_st.image_link }}"
                    ></a>
                  {% endif %}
                </div>
              {%- when 'video' -%}
                {% liquid
                  assign custom_ratio = block_st.custom_ratio
                  if custom_ratio != empty
                    assign ratio = custom_ratio | replace: ':', '/'
                  else
                    assign ratio = '3/2'
                  endif
                  assign video_local = block_st.video_local
                  assign video_id = block_st.video_url.id | default: block_st.video_url.id
                  assign poster = block_st.video_poster | default: block_st.video_local.preview_image | image_url: width: 1100
                  assign autoplay = block_st.autoplay
                %}
                <video-section
                  class="block relative{% if section.blocks.size == 2 and column_gap == 0 %} remove-rounded{% endif %} rounded overflow-hidden{% if section_st.equal_height_adjustment %} h-full{% endif %}"
                  data-media-id="{{ video_id }}"
                  {% if block_st.aspect_ratio == 'adapt' %}
                    style="--aspect-ratio: {{ block_st.video_local.aspect_ratio }};"
                  {% elsif block_st.aspect_ratio == 'custom' %}
                    style="--aspect-ratio: {{ ratio }}"
                  {% else %}
                    style="--aspect-ratio: {{ block_st.aspect_ratio }}"
                  {% endif %}
                >
                  {%- if block_st.video_local == null and block_st.video_url != null -%}
                    {%- liquid
                      assign loop = '&loop=1&playlist=' | append: video_id
                    -%}
                    {%- if block_st.video_url.type == 'youtube' -%}
                      <iframe
                        src="https://www.youtube.com/embed/{{ video_id }}?enablejsapi=1&controls=0&autoplay={% if autoplay %}1&mute=1{% else %}0&mute=0{% endif %}&playsinline=1&loop=1"
                        class="js-youtube"
                        allow="autoplay; encrypted-media"
                        allowfullscreen
                        title="{{ block_st.description | escape }}"
                      ></iframe>
                    {%- else -%}
                      <iframe
                        src="https://player.vimeo.com/video/{{ video_id }}?autoplay={% if autoplay %}1&mute=1{% else %}0&mute=0{% endif %}{{ loop }}"
                        class="js-vimeo"
                        allow="autoplay; encrypted-media"
                        allowfullscreen
                        title="{{ block_st.description | escape }}"
                      ></iframe>
                    {% endif %}
                  {%- else -%}
                    {{
                      video_local
                      | video_tag:
                        poster: poster,
                        loop: 'loop',
                        muted: autoplay,
                        playsinline: true,
                        autoplay: autoplay,
                        controls: false
                    }}
                  {% endif %}
                </video-section>
              {%- when 'video_with_text_overlay' -%}
                <div
                  class="section__block-inner {{ block_st.content_below_image }}  gradient color-{{ color_scheme }} flex relative{% if block_st.content_below_image %} flex-column text-below-video{% endif %}{% if section_st.equal_height_adjustment %} h-full{% endif %}"
                  {% liquid
                    assign video_local = block_st.video_local
                    assign poster = block_st.video_poster | default: block_st.video_local.preview_image | image_url: width: 1100
                  %}
                  {% if block_st.aspect_ratio == 'adapt' and block_st.video_local != null %}
                    style="--aspect-ratio: {{ block_st.video_local.aspect_ratio }};"
                  {% elsif block_st.aspect_ratio == 'adapt' and block_st.video_local == null and poster != null %}
                    style="--aspect-ratio: {{ block_st.video_poster.aspect_ratio }};"
                  {% elsif block_st.aspect_ratio == 'adapt' and block_st.video_local == null and poster == null %}
                    style="--aspect-ratio: 3/2;"
                  {% elsif block_st.aspect_ratio == 'custom' %}
                    {% liquid
                      assign custom_ratio = block_st.custom_ratio
                      if custom_ratio != empty
                        assign ratio = custom_ratio | replace: ':', '/'
                      else
                        assign ratio = '3/2'
                      endif
                    %}
                    style="--aspect-ratio: {{ ratio }}"
                  {% else %}
                    style="--aspect-ratio: {{ block_st.aspect_ratio }}"
                  {% endif %}
                >
                  <div
                    class="banner__media{% if section.blocks.size == 2 and column_gap == 0 %} remove-rounded{% endif %} w-full overlay-bg{% if content_width < 50 %} hover-effect{% endif %} rounded{% if block_st.content_below_image %} mb-30 mb-md-0{% else %} absolute{% endif %} inset-0{% if section_st.equal_height_adjustment %} h-full{% endif %}"
                    {% if block_st.aspect_ratio == 'adapt' and block_st.video_local != null %}
                      style="--aspect-ratio: {{ block_st.video_local.aspect_ratio }};--overlay-opacity: {{ block_st.image_overlay_opacity }}%;"
                    {% elsif block_st.aspect_ratio == 'adapt' and block_st.video_local == null and poster != null %}
                      style="--aspect-ratio: {{ block_st.video_poster.aspect_ratio }};--overlay-opacity: {{ block_st.image_overlay_opacity }}%;"
                    {% elsif block_st.aspect_ratio == 'adapt' and block_st.video_local == null and poster == null %}
                      style="--aspect-ratio: 3/2;--overlay-opacity: {{ block_st.image_overlay_opacity }}%;"
                    {% elsif block_st.aspect_ratio == 'custom' %}
                      {% liquid
                        assign custom_ratio = block_st.custom_ratio
                        if custom_ratio != empty
                          assign ratio = custom_ratio | replace: ':', '/'
                        else
                          assign ratio = '3/2'
                        endif
                      %}
                      style="--aspect-ratio: {{ ratio }};--overlay-opacity: {{ block_st.image_overlay_opacity }}%;"
                    {% else %}
                      style="--aspect-ratio: {{ block_st.aspect_ratio }};--overlay-opacity: {{ block_st.image_overlay_opacity }}%;"
                    {% endif %}
                    style=""
                  >
                    {% liquid
                      assign video_local = block_st.video_local
                      assign poster = block_st.video_poster | default: block_st.video_local.preview_image | image_url: width: 1100

                      assign autoplay = block_st.autoplay
                    %}
                    <video-section
                      class="block h-full{% if section.blocks.size == 2 and column_gap == 0 %} remove-rounded{% endif %} rounded overflow-hidden{% if section_st.equal_height_adjustment %} h-full object-fit-cover object-position-center{% endif %}"
                      data-media-id="{{ video_id }}"
                    >
                      {% if block_st.video_local != null %}
                        {{
                          video_local
                          | video_tag:
                            poster: poster,
                            loop: 'loop',
                            muted: true,
                            playsinline: true,
                            autoplay: autoplay,
                            controls: false
                        }}
                      {% else %}
                        {%- render 'placeholder-render', class: 'rounded w-full h-full object-fit-cover' -%}
                      {% endif %}
                    </video-section>
                  </div>
                  <div
                    class="sec__content w-full relative z-1 flex {{ block_st.content_position }} text-{{ block_st.content_alignment }}"
                    style="--padding-inline: {{ block_st.content_padding_inline }};--padding-block: {{ block_st.content_padding_block }};"
                  >
                    <div class="sec__content-inner py-custom px-custom{% if block_st.content_padding_inline < 35 %} x-min-value{% endif %}{% if block_st.content_padding_block < 35 %} y-min-value{% endif %}{% if block_st.content_below_image %} w-full{% endif %}">
                      {%- if block_st.subheading != blank -%}
                        <div
                          class="sec__content-subheading heading-color heading fs-custom {{ block_st.subheading_font_weight }}{% if block_st.subheading_spacing_bottom > 41 %} mb-big{% elsif block_st.subheading_spacing_bottom > 30 %} mb-medium {% else %} mb-custom{% endif %}"
                          style="--font-size: {{ block_st.subheading_font_size }};--space-bottom: {{ block_st.subheading_spacing_bottom }}"
                        >
                          {{ block_st.subheading | escape }}
                        </div>
                      {% endif %}
                      {%- if block_st.heading != blank -%}
                        <h2
                          class="sec__content-heading heading-letter-spacing mt-0{% if block_st.heading_spacing_bottom > 41 %} mb-big{% elsif block_st.heading_spacing_bottom > 30 %} mb-medium {% else %} mb-custom{% endif %}{% if block_st.heading_uppercase %} uppercase{% endif %}{% if block_st.heading_font_size > 41 %} fs-big{% elsif block_st.heading_font_size > 24 %} fs-medium {% else %} fs-custom{% endif %} {{ block_st.heading_font_weight }}"
                          style="--font-size: {{ block_st.heading_font_size }};--space-bottom: {{ block_st.heading_spacing_bottom }}"
                          {{ block.shopify_attributes }}
                        >
                          {{ block_st.heading }}
                        </h2>
                      {% endif %}
                      {%- if block_st.description != blank -%}
                        <div
                          class="sec__content-des rich__text-m0 {% if block_st.des_spacing_bottom > 41 %} mb-big{% elsif block_st.des_spacing_bottom > 30 %} mb-medium {% else %} mb-custom{% endif %}{% if block_st.des_font_size > 24 %} fs-medium {% else %} fs-custom{% endif %} {{ block_st.des_font_weight }}"
                          style="--font-size: {{ block_st.des_font_size }};--space-bottom: {{ block_st.des_spacing_bottom }}"
                          {{ block.shopify_attributes }}
                        >
                          {{ block_st.description }}
                        </div>
                      {% endif %}
                      {% if block_st.first_button_label != blank or block_st.second_button_label != blank %}
                        <div class="sec__content-btn align-center flex flex-wrap gap-15 justify-content-{{ block_st.content_alignment }}">
                          {% if block_st.first_button_label != blank %}
                            <a
                              {% if block_st.first_button_link == blank %}
                                role="link" aria-disabled="true"
                              {% else %}
                                href="{{ block_st.first_button_link | default: "#" }}"
                              {% endif %}
                              aria-label="{{ block_st.first_button_label }}"
                              class="inline-flex no-underline btn-{{ block_st.first_button_type }} px-md-20 py-md-10 p-lg-content-btn"
                            >
                              {{ block_st.first_button_label }}
                            </a>
                          {% endif %}
                          {% if block_st.second_button_label != blank %}
                            <a
                              {% if block_st.second_button_link == blank %}
                                role="link" aria-disabled="true"
                              {% else %}
                                href="{{ block_st.second_button_link | default: "#" }}"
                              {% endif %}
                              aria-label="{{ block_st.second_button_label }}"
                              class="inline-flex no-underline btn-{{ block_st.second_button_type }} px-md-20 py-md-10 p-lg-content-btn"
                            >
                              {{ block_st.second_button_label }}
                            </a>
                          {% endif %}
                        </div>
                      {% endif %}
                    </div>
                  </div>
                </div>
              {%- when 'image_with_text_below' -%}
                <div class="section__block-inner gradient color-{{ color_scheme }}{% if section_st.equal_height_adjustment %} h-full flex flex-column justify-{{ block_st.vertical_align}} align-{{ block_st.vertical_align}}{% endif %}">
                  <div
                    class="banner__media w-full{% if section.blocks.size == 2 and column_gap == 0 %} remove-rounded{% endif %} rounded{% if content_width < 50 %} hover-effect{% endif %}"
                    style="{%- if block_st.image != blank -%}--aspect-ratio: {{ block_st.image.aspect_ratio }};{%- else -%}--aspect-ratio: 5/2; {% endif %}{%- if block_st.mobile_image -%} --aspect-ratio-mb: {{ block_st.mobile_image.aspect_ratio }};{% endif %}"
                  >
                    {%- if block_st.image != blank or block_st.mobile_image != blank -%}
                      {%- if block_st.image.alt == blank -%}
                        {%- capture alt -%}{{ block_st.heading }}{%- endcapture -%}
                      {%- else -%}
                        {%- assign alt = block_st.image.alt -%}
                      {% endif %}
                      {% render 'responsive-image',
                        type: 'banner',
                        container: section_width,
                        colunm: colunm,
                        padding: padding,
                        colunm_mobile: items_per_row_mobile,
                        image: block_st.image,
                        image_mobile: block_st.mobile_image,
                        image_alt: alt
                      %}
                    {%- else -%}
                      {%- render 'placeholder-render' -%}
                    {% endif %}
                    {% if block_st.image_link != blank %}
                      <a class="absolute inset-0 z-2" href="{{ block_st.image_link }}"></a>
                    {% endif %}
                  </div>
                  <div
                    class="sec__content w-full text-{{ block_st.content_alignment }}"
                    style="--padding-inline: {{ block_st.content_padding_inline }};--space-top: {{ block_st.spacing_top }}px;--space-bottom: {{ block_st.spacing_bottom }};"
                  >
                    <div class="sec__content-inner mt-custom pb-custom px-custom relative{% if block_st.content_padding_inline < 35 %} x-min-value{% endif %}{% if block_st.content_padding_block < 35 %} y-min-value{% endif %}">
                      {%- if block_st.subheading != blank -%}
                        <div
                          class="sec__content-subheading heading-color heading {% if block_st.subheading_spacing_bottom > 41 %} mb-big{% elsif block_st.subheading_spacing_bottom > 30 %} mb-medium {% else %} mb-custom{% endif %} fs-custom {{ block_st.subheading_font_weight }}"
                          style="--font-size: {{ block_st.subheading_font_size }};--space-bottom: {{ block_st.subheading_spacing_bottom }}"
                        >
                          {{ block_st.subheading | escape }}
                        </div>
                      {% endif %}
                      {%- if block_st.heading != blank -%}
                        <h2
                          class="sec__content-heading heading-letter-spacing mt-0 {% if block_st.heading_spacing_bottom > 41 %} mb-big{% elsif block_st.heading_spacing_bottom > 30 %} mb-medium {% else %} mb-custom{% endif %}{% if block_st.heading_uppercase %} uppercase{% endif %}{% if block_st.heading_font_size > 41 %} fs-big{% elsif block_st.heading_font_size > 24 %} fs-medium {% else %} fs-custom{% endif %} {{ block_st.heading_font_weight }}"
                          style="--font-size: {{ block_st.heading_font_size }};--space-bottom: {{ block_st.heading_spacing_bottom }}"
                          {{ block.shopify_attributes }}
                        >
                          {% if block_st.image_link != blank %}
                            <a class="no-underline heading-style" href="{{ block_st.image_link }}">
                              {{ block_st.heading -}}
                            </a>
                          {% else %}
                            {{ block_st.heading }}
                          {% endif %}
                        </h2>
                      {% endif %}
                      {%- if block_st.description != blank -%}
                        <div
                          class="sec__content-des rich__text-m0 {% if block_st.des_spacing_bottom > 41 %} mb-big{% elsif block_st.des_spacing_bottom > 30 %} mb-medium {% else %} mb-custom{% endif %}{% if block_st.des_font_size > 24 %} fs-medium {% else %} fs-custom{% endif %} {{ block_st.des_font_weight }}"
                          style="--font-size: {{ block_st.des_font_size }};--space-bottom: {{ block_st.des_spacing_bottom }}"
                          {{ block.shopify_attributes }}
                        >
                          {{ block_st.description }}
                        </div>
                      {% endif %}
                      {% if block_st.first_button_label != blank or block_st.second_button_label != blank %}
                        <div class="sec__content-btn align-center flex flex-wrap gap-15 justify-content-{{ block_st.content_alignment }}">
                          {% if block_st.first_button_label != blank %}
                            <a
                              {% if block_st.first_button_link == blank %}
                                role="link" aria-disabled="true"
                              {% else %}
                                href="{{ block_st.first_button_link | default: "#" }}"
                              {% endif %}
                              aria-label="{{ block_st.first_button_label }}"
                              class="inline-flex no-underline btn-{{ block_st.first_button_type }} px-md-20 py-md-10 p-lg-content-btn"
                            >
                              {{ block_st.first_button_label }}
                            </a>
                          {% endif %}
                          {% if block_st.second_button_label != blank %}
                            <a
                              {% if block_st.second_button_link == blank %}
                                role="link" aria-disabled="true"
                              {% else %}
                                href="{{ block_st.second_button_link | default: "#" }}"
                              {% endif %}
                              aria-label="{{ block_st.second_button_label }}"
                              class="inline-flex no-underline btn-{{ block_st.second_button_type }} px-md-20 py-md-10 p-lg-content-btn"
                            >
                              {{ block_st.second_button_label }}
                            </a>
                          {% endif %}
                        </div>
                      {% endif %}
                    </div>
                  </div>
                </div>
              {%- when 'icon_box' -%}
                {% assign image = block_st.icon_image %}
                {% assign image_2 = block_st.icon_image2 %}
                <motion-element
                  data-motion="fade-up-lg"
                  {% if scroll_animation != 'slide_in' %}
                    hold
                  {% endif %}
                  data-motion-delay="{{ forloop.index0 | times: 50 }}"
                  class="sec__icon-switch-slide block py-20 {% if scroll_animation != 'none' %} scroll-trigger {{ scroll_animation }} {% endif %}"
                  style="
                    {%- if scroll_animation != 'none' -%}
                      --animation-order: {{  forloop.index }}
                    {% endif %}
                  "
                >
                  <div
                    class="sec__icon-box-item flex flex-column align-center"
                  >
                    {%- if image != blank -%}
                      <div
                        class="sec__icon-box-content-icon lh-1{% if image != blank %} w-custom max-w-100 flex-auto mb-20 {% endif %}"
                        style="--custom-width: {{ block_st.icon_size }}px"
                      >
                        {%- if image != blank -%}
                          {%- assign sizes = block_st.icon_size | append: 'px' -%}
                          {%- assign image_alt = image.alt | default: 'images' -%}
                          {% render 'responsive-image',
                            type: 'other',
                            image: image,
                            image_alt: image_alt,
                            sizes: sizes,
                            custom_widths: '185, 120, 60, 30',
                            no_animate: true
                          %}
                        {% endif %}
                      </div>
                    {% endif %}
                    {% if block_st.heading != blank or block_st.description != blank %}
                      <div class="sec__icon-box-content text-center word-break">
                        {% if block_st.heading != blank %}
                          <h3
                            class="sec__icon-box-content-heading mt-0 mb-10 fs-18"
                          >
                            {{ block_st.heading }}
                          </h3>
                        {% endif %}
                        {% if block_st.description != blank %}
                          <div
                            class="sec__icon-box-content-des rich__text-m0 fs-custom"
                            style="--font-size: 15;"
                          >
                            {{ block_st.description }}
                          </div>
                        {% endif %}
                      </div>
                    {% endif %}
                  </div>

                  <div
                    class="sec__icon-switch-slide py-20 flex flex-column gap-custom align-center justify-center "
                  >
                    <div
                      class="sec__icon-box-item flex flex-column align-center"
                    >
                      {%- if image_2 != blank -%}
                        <div
                          class="sec__icon-box-content-icon lh-1{% if image != blank %} w-custom max-w-100 flex-auto mb-20 {% endif %}"
                          style="--custom-width: {{ block_st.icon_size2 }}px"
                        >
                          {%- if image_2 != blank -%}
                            {%- assign sizes = block_st.icon_size2 | append: 'px' -%}
                            {%- assign image_alt = image_2.alt | default: 'images' -%}
                            {% render 'responsive-image',
                              type: 'other',
                              image: image_2,
                              image_alt: image_alt,
                              sizes: sizes,
                              custom_widths: '185, 120, 60, 30',
                              no_animate: true
                            %}
                          {% endif %}
                        </div>
                      {% endif %}
                      {% if block_st.heading2 != blank or block_st.description2 != blank %}
                        <div class="sec__icon-box-content text-center word-break">
                          {% if block_st.heading2 != blank %}
                            <h3
                              class="sec__icon-box-content-heading mt-0 mb-10 fs-18"
                            >
                              {{ block_st.heading2 }}
                            </h3>
                          {% endif %}
                          {% if block_st.description2 != blank %}
                            <div
                              class="sec__icon-box-content-des rich__text-m0 fs-custom"
                              style="--font-size: 15;"
                            >
                              {{ block_st.description2 }}
                            </div>
                          {% endif %}
                        </div>
                      {% endif %}
                    </div>
                  </div>
                </motion-element>
            {%- endcase -%}
          </motion-element>
        {%- endfor -%}
      </multi-content>
    {% endif %}
  </div>
</div>
{% schema %}
{
  "name": "t:sections.multi_content.name",
  "tag": "section",
  "disabled_on": {
    "groups": ["header", "footer", "custom.overlay"]
  },
  "settings": [
    {
      "type": "select",
      "id": "section_width",
      "label": "t:sections.all.section_width.label",
      "default": "fluid_container",
      "options": [
        {
          "value": "container",
          "label": "t:sections.all.section_width.container.label"
        },
        {
          "value": "fluid_container",
          "label": "t:sections.all.section_width.fluid_container.label"
        },
        {
          "value": "stretch_width",
          "label": "t:sections.all.section_width.stretch_width.label"
        },
        {
          "value": "full_width",
          "label": "t:sections.all.section_width.full_width.label"
        }
      ]
    },
    {
      "type": "header",
      "content": "t:sections.all.section_header.label"
    },
    {
      "type": "text",
      "id": "heading",
      "label": "t:sections.all.section_header.heading.label"
    },
    {
      "type": "richtext",
      "id": "description",
      "label": "t:sections.all.section_header.description.label"
    },
    {
      "type": "select",
      "id": "header_size",
      "label": "t:sections.all.section_header.header_size.label",
      "default": "medium",
      "options": [
        {
          "value": "small",
          "label": "t:sections.all.section_header.header_size.small.label"
        },
        {
          "value": "medium",
          "label": "t:sections.all.section_header.header_size.medium.label"
        },
        {
          "value": "large",
          "label": "t:sections.all.section_header.header_size.large.label"
        }
      ]
    },
    {
      "type": "select",
      "id": "header_alignment",
      "label": "t:sections.all.section_header.alignment.label",
      "default": "center",
      "options": [
        {
          "value": "left",
          "label": "t:sections.all.content_settings.content_alignment.left.label"
        },
        {
          "value": "center",
          "label": "t:sections.all.content_settings.content_alignment.center.label"
        },
        {
          "value": "right",
          "label": "t:sections.all.content_settings.content_alignment.right.label"
        }
      ]
    },
    {
      "type": "range",
      "id": "column_gap",
      "label": "t:sections.all.items.column_gap.label",
      "default": 30,
      "min": 0,
      "max": 50,
      "step": 5,
      "unit": "px"
    },
    {
      "type": "color",
      "id": "content_background_color",
      "label": "t:sections.multi_content.content_background_color.label"
    },
    {
      "type": "header",
      "content": "t:sections.multi_content.block_settings.label"
    },
    {
      "type": "checkbox",
      "id": "equal_height_adjustment",
      "label": "t:sections.multi_content.block_settings.equal_height_adjustment.label"
    },
    {
      "type": "header",
      "content": "t:sections.all.mobile_options.label"
    },
    {
      "type": "checkbox",
      "id": "carousel_on_mobile",
      "label": "t:sections.all.mobile_options.carousel_on_mobile.label",
      "default": false
    },
    {
      "type": "range",
      "id": "items_per_row_mobile",
      "label": "t:sections.all.items.items_per_row.label",
      "min": 1,
      "max": 2,
      "step": 0.5,
      "default": 1
    },
    {
      "type": "header",
      "content": "t:sections.all.section_padding.label"
    },
    {
      "type": "range",
      "id": "padding_top",
      "label": "t:sections.all.section_padding.top.label",
      "default": 0,
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px"
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "label": "t:sections.all.section_padding.bottom.label",
      "default": 0,
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px"
    },
    {
      "type": "checkbox",
      "id": "reset_spacing",
      "label": "t:sections.all.section_padding.reset_spacing.label",
      "default": false
    }
  ],
  "blocks": [
    {
      "type": "image_with_text_overlay",
      "name": "t:sections.image_with_text_overlay.name",
      "limit": 4,
      "settings": [
        {
          "type": "color_scheme",
          "id": "color_scheme",
          "label": "t:sections.all.color_scheme.label",
          "default": "scheme-1"
        },
        {
          "type": "select",
          "id": "content_width",
          "label": "t:sections.all.content_setting.width.label",
          "default": "50%",
          "options": [
            {
              "value": "20%",
              "label": "20%"
            },
            {
              "value": "25%",
              "label": "25%"
            },
            {
              "value": "33.3333%",
              "label": "33%"
            },
            {
              "value": "50%",
              "label": "50%"
            },
            {
              "value": "66.6666%",
              "label": "66%"
            },
            {
              "value": "75%",
              "label": "75%"
            },
            {
              "value": "80%",
              "label": "80%"
            },
            {
              "value": "100%",
              "label": "100%"
            }
          ]
        },
        {
          "type": "select",
          "id": "vertical_align",
          "label": "t:sections.multi_content.block_settings.vertical_align.label",
          "default": "start",
          "options": [
            {
              "value": "start",
              "label": "t:sections.multi_content.block_settings.vertical_align.top.label"
            },
            {
              "value": "center",
              "label": "t:sections.multi_content.block_settings.vertical_align.middle.label"
            },
            {
              "value": "end",
              "label": "t:sections.multi_content.block_settings.vertical_align.bottom.label"
            }
          ]
        },
        {
          "type": "header",
          "content": "t:sections.all.image.label"
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "t:sections.all.image.label"
        },
        {
          "type": "image_picker",
          "id": "mobile_image",
          "label": "t:sections.all.image.mobile_image.label"
        },
        {
          "type": "url",
          "id": "image_link",
          "label": "t:sections.all.image.link"
        },
        {
          "type": "range",
          "id": "image_overlay_opacity",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "%",
          "label": "t:sections.all.image.overlay_opacity.label",
          "default": 0
        },
        {
          "type": "header",
          "content": "t:sections.all.contents.label"
        },
        {
          "type": "textarea",
          "id": "custom_svg",
          "label": "t:sections.all.contents.custom_svg.label"
        },
        {
          "type": "text",
          "id": "subheading",
          "default": "Subheading",
          "label": "t:sections.all.contents.subheading.label"
        },
        {
          "type": "textarea",
          "id": "heading",
          "label": "t:sections.all.contents.heading.label",
          "default": "Text overlay"
        },
        {
          "type": "richtext",
          "id": "description",
          "label": "t:sections.all.contents.description.label",
          "default": "<p>Description</p>"
        },
        {
          "type": "text",
          "id": "first_button_label",
          "default": "Button label",
          "label": "t:sections.all.contents.button.first_button_label.label",
          "info": "t:sections.all.contents.button.first_button_label.info"
        },
        {
          "type": "url",
          "id": "first_button_link",
          "label": "t:sections.all.contents.button.first_button_link.label"
        },
        {
          "type": "select",
          "id": "first_button_type",
          "label": "t:sections.all.contents.button.button_type.label",
          "default": "primary",
          "options": [
            {
              "value": "primary",
              "label": "t:sections.all.contents.button.button_type.primary.label"
            },
            {
              "value": "outline",
              "label": "t:sections.all.contents.button.button_type.outline.label"
            },
            {
              "value": "link",
              "label": "t:sections.all.contents.button.button_type.link.label"
            }
          ]
        },
        {
          "type": "text",
          "id": "second_button_label",
          "label": "t:sections.all.contents.button.secondary_button_label.label",
          "info": "t:sections.all.contents.button.secondary_button_label.info"
        },
        {
          "type": "url",
          "id": "second_button_link",
          "label": "t:sections.all.contents.button.secondary_button_link.label"
        },
        {
          "type": "select",
          "id": "second_button_type",
          "label": "t:sections.all.contents.button.button_type.label",
          "default": "primary",
          "options": [
            {
              "value": "primary",
              "label": "t:sections.all.contents.button.button_type.primary.label"
            },
            {
              "value": "outline",
              "label": "t:sections.all.contents.button.button_type.outline.label"
            },
            {
              "value": "link",
              "label": "t:sections.all.contents.button.button_type.link.label"
            }
          ]
        },
        {
          "type": "header",
          "content": "t:sections.all.content_setting.label"
        },
        {
          "type": "select",
          "id": "content_alignment",
          "label": "t:sections.all.content_settings.content_alignment.label",
          "default": "left",
          "options": [
            {
              "value": "left",
              "label": "t:sections.all.content_settings.content_alignment.left.label"
            },
            {
              "value": "center",
              "label": "t:sections.all.content_settings.content_alignment.center.label"
            },
            {
              "value": "right",
              "label": "t:sections.all.content_settings.content_alignment.right.label"
            }
          ]
        },
        {
          "type": "select",
          "id": "content_position",
          "default": "top-left",
          "label": "t:sections.all.content_settings.content_position.label",
          "options": [
            {
              "value": "top-left",
              "label": "t:sections.all.content_settings.content_position.top_left.label"
            },
            {
              "value": "top-center",
              "label": "t:sections.all.content_settings.content_position.top_center.label"
            },
            {
              "value": "top-right",
              "label": "t:sections.all.content_settings.content_position.top_right.label"
            },
            {
              "value": "middle-left",
              "label": "t:sections.all.content_settings.content_position.middle_left.label"
            },
            {
              "value": "middle-center",
              "label": "t:sections.all.content_settings.content_position.middle_center.label"
            },
            {
              "value": "middle-right",
              "label": "t:sections.all.content_settings.content_position.middle_right.label"
            },
            {
              "value": "bottom-left",
              "label": "t:sections.all.content_settings.content_position.bottom_left.label"
            },
            {
              "value": "bottom-center",
              "label": "t:sections.all.content_settings.content_position.bottom_center.label"
            },
            {
              "value": "bottom-right",
              "label": "t:sections.all.content_settings.content_position.bottom_right.label"
            }
          ]
        },
        {
          "type": "range",
          "id": "content_padding_block",
          "min": 10,
          "max": 60,
          "step": 1,
          "unit": "px",
          "default": 30,
          "label": "t:sections.all.content_settings.content_padding_block.label"
        },
        {
          "type": "range",
          "id": "content_padding_inline",
          "min": 10,
          "max": 60,
          "step": 1,
          "unit": "px",
          "default": 30,
          "label": "t:sections.all.content_settings.content_padding_inline.label"
        },
        {
          "type": "header",
          "content": "t:sections.all.mobile_options.label"
        },
        {
          "type": "checkbox",
          "id": "content_below_image",
          "label": "t:sections.all.mobile_options.content_below_image.label"
        },
        {
          "type": "header",
          "content": "t:sections.all.content_settings.typography.label",
          "info": "t:sections.all.content_setting.info"
        },
        {
          "type": "paragraph",
          "content": "t:sections.all.content_settings.custom_svg.label"
        },
        {
          "type": "range",
          "id": "custom_svg_spacing_bottom",
          "label": "t:sections.all.content_settings.spacing.spacing_bottom.label",
          "default": 10,
          "min": 0,
          "max": 30,
          "step": 1,
          "unit": "px"
        },
        {
          "type": "paragraph",
          "content": "t:sections.all.content_settings.subheading.label"
        },
        {
          "type": "range",
          "id": "subheading_font_size",
          "label": "t:sections.all.content_settings.font_size.label",
          "default": 12,
          "min": 10,
          "max": 30,
          "step": 1,
          "unit": "px"
        },
        {
          "type": "select",
          "id": "subheading_font_weight",
          "label": "t:sections.all.content_settings.font_weight.label",
          "default": "subheading_weight",
          "options": [
            {
              "value": "body_weight",
              "label": "t:sections.all.content_settings.font_weight.body_weight.label"
            },
            {
              "value": "subheading_weight",
              "label": "t:sections.all.content_settings.font_weight.subheading_weight.label"
            },
            {
              "value": "heading_weight",
              "label": "t:sections.all.content_settings.font_weight.heading_weight.label"
            }
          ]
        },
        {
          "type": "range",
          "id": "subheading_spacing_bottom",
          "label": "t:sections.all.content_settings.spacing.spacing_bottom.label",
          "default": 10,
          "min": 0,
          "max": 30,
          "step": 1,
          "unit": "px"
        },
        {
          "type": "paragraph",
          "content": "t:sections.all.content_settings.heading.label"
        },
        {
          "type": "range",
          "id": "heading_font_size",
          "label": "t:sections.all.content_settings.font_size.label",
          "default": 36,
          "min": 10,
          "max": 90,
          "step": 1,
          "unit": "px"
        },
        {
          "type": "select",
          "id": "heading_font_weight",
          "label": "t:sections.all.content_settings.font_weight.label",
          "default": "heading_weight",
          "options": [
            {
              "value": "body_weight",
              "label": "t:sections.all.content_settings.font_weight.body_weight.label"
            },
            {
              "value": "subheading_weight",
              "label": "t:sections.all.content_settings.font_weight.subheading_weight.label"
            },
            {
              "value": "heading_weight",
              "label": "t:sections.all.content_settings.font_weight.heading_weight.label"
            }
          ]
        },
        {
          "type": "checkbox",
          "id": "heading_uppercase",
          "label": "t:sections.all.text_transform.uppercase.label"
        },
        {
          "type": "range",
          "id": "heading_spacing_bottom",
          "label": "t:sections.all.content_settings.spacing.spacing_bottom.label",
          "default": 10,
          "min": 0,
          "max": 30,
          "step": 1,
          "unit": "px"
        },

        {
          "type": "paragraph",
          "content": "t:sections.all.content_settings.description.label"
        },
        {
          "type": "range",
          "id": "des_font_size",
          "label": "t:sections.all.content_settings.font_size.label",
          "default": 14,
          "min": 10,
          "max": 30,
          "step": 1,
          "unit": "px"
        },
        {
          "type": "select",
          "id": "des_font_weight",
          "label": "t:sections.all.content_settings.font_weight.label",
          "default": "body_weight",
          "options": [
            {
              "value": "body_weight",
              "label": "t:sections.all.content_settings.font_weight.body_weight.label"
            },
            {
              "value": "subheading_weight",
              "label": "t:sections.all.content_settings.font_weight.subheading_weight.label"
            },
            {
              "value": "heading_weight",
              "label": "t:sections.all.content_settings.font_weight.heading_weight.label"
            }
          ]
        },
        {
          "type": "range",
          "id": "des_spacing_bottom",
          "label": "t:sections.all.content_settings.spacing.spacing_bottom.label",
          "default": 10,
          "min": 0,
          "max": 40,
          "step": 1,
          "unit": "px"
        }
      ]
    },
    {
      "type": "image_with_text_below",
      "name": "t:sections.multi_content.block_settings.image_with_text_below.label",
      "limit": 4,
      "settings": [
        {
          "type": "color_scheme",
          "id": "color_scheme",
          "label": "t:sections.all.color_scheme.label",
          "default": "scheme-1"
        },
        {
          "type": "select",
          "id": "content_width",
          "label": "t:sections.all.content_setting.width.label",
          "default": "33.3333%",
          "options": [
            {
              "value": "20%",
              "label": "20%"
            },
            {
              "value": "25%",
              "label": "25%"
            },
            {
              "value": "33.3333%",
              "label": "33%"
            },
            {
              "value": "50%",
              "label": "50%"
            },
            {
              "value": "66.6666%",
              "label": "66%"
            },
            {
              "value": "75%",
              "label": "75%"
            },
            {
              "value": "80%",
              "label": "80%"
            },
            {
              "value": "100%",
              "label": "100%"
            }
          ]
        },
        {
          "type": "select",
          "id": "vertical_align",
          "options": [
            {
              "value": "start",
              "label": "t:sections.multi_content.block_settings.vertical_align.top.label"
            },
            {
              "value": "center",
              "label": "t:sections.multi_content.block_settings.vertical_align.middle.label"
            },
            {
              "value": "end",
              "label": "t:sections.multi_content.block_settings.vertical_align.bottom.label"
            }
          ],
          "default": "start",
          "label": "t:sections.multi_content.block_settings.vertical_align.label"
        },
        {
          "type": "header",
          "content": "t:sections.all.image.label"
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "t:sections.all.image.label"
        },
        {
          "type": "image_picker",
          "id": "mobile_image",
          "label": "t:sections.all.image.mobile_image.label"
        },
        {
          "type": "url",
          "id": "image_link",
          "label": "t:sections.all.image.link"
        },
        {
          "type": "header",
          "content": "t:sections.all.contents.label"
        },
        {
          "type": "text",
          "id": "subheading",
          "label": "t:sections.all.contents.subheading.label"
        },
        {
          "type": "text",
          "id": "heading",
          "label": "t:sections.all.contents.heading.label",
          "default": "Text below"
        },
        {
          "type": "richtext",
          "id": "description",
          "label": "t:sections.all.contents.description.label",
          "default": "<p>Share information about your brand with your customers. Describe a product, make announcements, or welcome customers to your store.</p>"
        },
        {
          "type": "text",
          "id": "first_button_label",
          "default": "Button label",
          "label": "t:sections.all.contents.button.first_button_label.label",
          "info": "t:sections.all.contents.button.first_button_label.info"
        },
        {
          "type": "url",
          "id": "first_button_link",
          "label": "t:sections.all.contents.button.first_button_link.label"
        },
        {
          "type": "select",
          "id": "first_button_type",
          "label": "t:sections.all.contents.button.button_type.label",
          "default": "link",
          "options": [
            {
              "value": "primary",
              "label": "t:sections.all.contents.button.button_type.primary.label"
            },
            {
              "value": "outline",
              "label": "t:sections.all.contents.button.button_type.outline.label"
            },
            {
              "value": "link",
              "label": "t:sections.all.contents.button.button_type.link.label"
            }
          ]
        },
        {
          "type": "text",
          "id": "second_button_label",
          "label": "t:sections.all.contents.button.secondary_button_label.label",
          "info": "t:sections.all.contents.button.secondary_button_label.info"
        },
        {
          "type": "url",
          "id": "second_button_link",
          "label": "t:sections.all.contents.button.secondary_button_link.label"
        },
        {
          "type": "select",
          "id": "second_button_type",
          "label": "t:sections.all.contents.button.button_type.label",
          "default": "primary",
          "options": [
            {
              "value": "primary",
              "label": "t:sections.all.contents.button.button_type.primary.label"
            },
            {
              "value": "outline",
              "label": "t:sections.all.contents.button.button_type.outline.label"
            },
            {
              "value": "link",
              "label": "t:sections.all.contents.button.button_type.link.label"
            }
          ]
        },
        {
          "type": "header",
          "content": "t:sections.all.content_setting.label"
        },
        {
          "type": "select",
          "id": "content_alignment",
          "label": "t:sections.all.content_settings.content_alignment.label",
          "default": "left",
          "options": [
            {
              "value": "left",
              "label": "t:sections.all.content_settings.content_alignment.left.label"
            },
            {
              "value": "center",
              "label": "t:sections.all.content_settings.content_alignment.center.label"
            },
            {
              "value": "right",
              "label": "t:sections.all.content_settings.content_alignment.right.label"
            }
          ]
        },
        {
          "type": "range",
          "id": "spacing_top",
          "min": 0,
          "max": 50,
          "step": 1,
          "label": "t:sections.all.content_settings.spacing.spacing_top.label",
          "default": 25
        },
        {
          "type": "range",
          "id": "spacing_bottom",
          "min": 0,
          "max": 50,
          "step": 1,
          "label": "t:sections.all.content_settings.spacing.spacing_bottom.label",
          "default": 0
        },
        {
          "type": "range",
          "id": "content_padding_inline",
          "min": 0,
          "max": 60,
          "step": 1,
          "unit": "px",
          "default": 0,
          "label": "t:sections.all.content_settings.content_padding_inline.label"
        },
        {
          "type": "header",
          "content": "t:sections.all.content_settings.typography.label",
          "info": "t:sections.all.content_setting.info"
        },
        {
          "type": "paragraph",
          "content": "t:sections.all.content_settings.subheading.label"
        },
        {
          "type": "range",
          "id": "subheading_font_size",
          "label": "t:sections.all.content_settings.font_size.label",
          "default": 12,
          "min": 10,
          "max": 30,
          "step": 1,
          "unit": "px"
        },
        {
          "type": "select",
          "id": "subheading_font_weight",
          "label": "t:sections.all.content_settings.font_weight.label",
          "default": "subheading_weight",
          "options": [
            {
              "value": "body_weight",
              "label": "t:sections.all.content_settings.font_weight.body_weight.label"
            },
            {
              "value": "subheading_weight",
              "label": "t:sections.all.content_settings.font_weight.subheading_weight.label"
            },
            {
              "value": "heading_weight",
              "label": "t:sections.all.content_settings.font_weight.heading_weight.label"
            }
          ]
        },
        {
          "type": "range",
          "id": "subheading_spacing_bottom",
          "label": "t:sections.all.content_settings.spacing.spacing_bottom.label",
          "default": 15,
          "min": 0,
          "max": 30,
          "step": 1,
          "unit": "px"
        },
        {
          "type": "paragraph",
          "content": "t:sections.all.content_settings.heading.label"
        },
        {
          "type": "range",
          "id": "heading_font_size",
          "label": "t:sections.all.content_settings.font_size.label",
          "default": 18,
          "min": 10,
          "max": 40,
          "step": 1,
          "unit": "px"
        },
        {
          "type": "select",
          "id": "heading_font_weight",
          "label": "t:sections.all.content_settings.font_weight.label",
          "default": "heading_weight",
          "options": [
            {
              "value": "body_weight",
              "label": "t:sections.all.content_settings.font_weight.body_weight.label"
            },
            {
              "value": "subheading_weight",
              "label": "t:sections.all.content_settings.font_weight.subheading_weight.label"
            },
            {
              "value": "heading_weight",
              "label": "t:sections.all.content_settings.font_weight.heading_weight.label"
            }
          ]
        },
        {
          "type": "checkbox",
          "id": "heading_uppercase",
          "label": "t:sections.all.text_transform.uppercase.label"
        },
        {
          "type": "range",
          "id": "heading_spacing_bottom",
          "label": "t:sections.all.content_settings.spacing.spacing_bottom.label",
          "default": 15,
          "min": 0,
          "max": 50,
          "step": 1,
          "unit": "px"
        },
        {
          "type": "paragraph",
          "content": "t:sections.all.content_settings.description.label"
        },
        {
          "type": "range",
          "id": "des_font_size",
          "label": "t:sections.all.content_settings.font_size.label",
          "default": 14,
          "min": 10,
          "max": 30,
          "step": 1,
          "unit": "px"
        },
        {
          "type": "select",
          "id": "des_font_weight",
          "label": "t:sections.all.content_settings.font_weight.label",
          "default": "body_weight",
          "options": [
            {
              "value": "body_weight",
              "label": "t:sections.all.content_settings.font_weight.body_weight.label"
            },
            {
              "value": "subheading_weight",
              "label": "t:sections.all.content_settings.font_weight.subheading_weight.label"
            },
            {
              "value": "heading_weight",
              "label": "t:sections.all.content_settings.font_weight.heading_weight.label"
            }
          ]
        },
        {
          "type": "range",
          "id": "des_spacing_bottom",
          "label": "t:sections.all.content_settings.spacing.spacing_bottom.label",
          "default": 20,
          "min": 0,
          "max": 40,
          "step": 1,
          "unit": "px"
        }
      ]
    },
    {
      "type": "image",
      "name": "Image",
      "limit": 4,
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "t:sections.all.image.label"
        },
        {
          "type": "url",
          "id": "image_link",
          "label": "t:sections.all.image.link"
        },
        {
          "type": "select",
          "id": "content_width",
          "label": "t:sections.all.content_setting.width.label",
          "default": "50%",
          "options": [
            {
              "value": "20%",
              "label": "20%"
            },
            {
              "value": "25%",
              "label": "25%"
            },
            {
              "value": "33.333%",
              "label": "33%"
            },
            {
              "value": "50%",
              "label": "50%"
            },
            {
              "value": "66.666%",
              "label": "66%"
            },
            {
              "value": "75%",
              "label": "75%"
            },
            {
              "value": "80%",
              "label": "80%"
            },
            {
              "value": "100%",
              "label": "100%"
            }
          ]
        },
        {
          "type": "select",
          "id": "vertical_align",
          "label": "t:sections.multi_content.block_settings.vertical_align.label",
          "default": "start",
          "options": [
            {
              "value": "start",
              "label": "t:sections.multi_content.block_settings.vertical_align.top.label"
            },
            {
              "value": "center",
              "label": "t:sections.multi_content.block_settings.vertical_align.middle.label"
            },
            {
              "value": "end",
              "label": "t:sections.multi_content.block_settings.vertical_align.bottom.label"
            }
          ]
        }
      ]
    },
    {
      "type": "text",
      "name": "t:sections.multi_content.block_settings.text.label",
      "limit": 4,
      "settings": [
        {
          "type": "color_scheme",
          "id": "color_scheme",
          "label": "t:sections.all.color_scheme.label",
          "default": "scheme-1"
        },
        {
          "type": "select",
          "id": "content_width",
          "label": "t:sections.all.content_setting.width.label",
          "default": "50%",
          "options": [
            {
              "value": "20%",
              "label": "20%"
            },
            {
              "value": "25%",
              "label": "25%"
            },
            {
              "value": "33.3333%",
              "label": "33%"
            },
            {
              "value": "50%",
              "label": "50%"
            },
            {
              "value": "66.6666%",
              "label": "66%"
            },
            {
              "value": "75%",
              "label": "75%"
            },
            {
              "value": "80%",
              "label": "80%"
            },
            {
              "value": "100%",
              "label": "100%"
            }
          ]
        },
        {
          "type": "select",
          "id": "vertical_align",
          "label": "t:sections.multi_content.block_settings.vertical_align.label",
          "default": "start",
          "options": [
            {
              "value": "start",
              "label": "t:sections.multi_content.block_settings.vertical_align.top.label"
            },
            {
              "value": "center",
              "label": "t:sections.multi_content.block_settings.vertical_align.middle.label"
            },
            {
              "value": "end",
              "label": "t:sections.multi_content.block_settings.vertical_align.bottom.label"
            }
          ]
        },
        {
          "type": "url",
          "id": "image_link",
          "label": "t:sections.all.image.link"
        },
        {
          "type": "header",
          "content": "t:sections.all.contents.label"
        },
        {
          "type": "text",
          "id": "subheading",
          "default": "Subheading",
          "label": "t:sections.all.contents.subheading.label"
        },
        {
          "type": "textarea",
          "id": "heading",
          "label": "t:sections.all.contents.heading.label",
          "default": "Text block"
        },
        {
          "type": "richtext",
          "id": "description",
          "label": "t:sections.all.contents.description.label"
        },
        {
          "type": "collection",
          "id": "collection",
          "label": "t:sections.products-list.blocks.collection"
        },
        {
          "type": "text",
          "id": "timer",
          "label": "t:sections.image_with_text_overlay.blocks.timer.settings.end_time.label",
          "info": "t:sections.image_with_text_overlay.blocks.timer.settings.end_time.info"
        },
        {
          "type": "text",
          "id": "expired_message",
          "default": "Time expired",
          "label": "t:sections.image_with_text_overlay.blocks.timer.settings.expired_message.label",
          "info": "t:sections.image_with_text_overlay.blocks.timer.settings.expired_message.info"
        },
        {
          "type": "select",
          "id": "style",
          "label": "t:sections.image_with_text_overlay.blocks.timer.settings.style.label",
          "default": "default",
          "options": [
            {
              "value": "default",
              "label": "t:sections.image_with_text_overlay.blocks.timer.settings.style.normal"
            },
            {
              "value": "highlight",
              "label": "t:sections.image_with_text_overlay.blocks.timer.settings.style.highlight"
            }
          ]
        },
        {
          "type": "range",
          "id": "font_size",
          "label": "t:sections.all.content_settings.font_size.label",
          "default": 36,
          "min": 24,
          "max": 60,
          "step": 1,
          "unit": "px"
        },
        {
          "type": "range",
          "id": "spacing_bottom",
          "label": "t:sections.all.content_settings.spacing.spacing_bottom.label",
          "default": 30,
          "min": 0,
          "max": 50,
          "step": 1,
          "unit": "px"
        },
        {
          "type": "text",
          "id": "first_button_label",
          "default": "Button label",
          "label": "t:sections.all.contents.button.first_button_label.label",
          "info": "t:sections.all.contents.button.first_button_label.info"
        },
        {
          "type": "url",
          "id": "first_button_link",
          "label": "t:sections.all.contents.button.first_button_link.label"
        },
        {
          "type": "select",
          "id": "first_button_type",
          "label": "t:sections.all.contents.button.button_type.label",
          "default": "primary",
          "options": [
            {
              "value": "primary",
              "label": "t:sections.all.contents.button.button_type.primary.label"
            },
            {
              "value": "outline",
              "label": "t:sections.all.contents.button.button_type.outline.label"
            },
            {
              "value": "link",
              "label": "t:sections.all.contents.button.button_type.link.label"
            }
          ]
        },
        {
          "type": "text",
          "id": "second_button_label",
          "label": "t:sections.all.contents.button.secondary_button_label.label",
          "info": "t:sections.all.contents.button.secondary_button_label.info"
        },
        {
          "type": "url",
          "id": "second_button_link",
          "label": "t:sections.all.contents.button.secondary_button_link.label"
        },
        {
          "type": "select",
          "id": "second_button_type",
          "label": "t:sections.all.contents.button.button_type.label",
          "default": "primary",
          "options": [
            {
              "value": "primary",
              "label": "t:sections.all.contents.button.button_type.primary.label"
            },
            {
              "value": "outline",
              "label": "t:sections.all.contents.button.button_type.outline.label"
            },
            {
              "value": "link",
              "label": "t:sections.all.contents.button.button_type.link.label"
            }
          ]
        },
        {
          "type": "header",
          "content": "t:sections.all.content_setting.label"
        },
        {
          "type": "select",
          "id": "content_alignment",
          "label": "t:sections.all.content_settings.content_alignment.label",
          "default": "left",
          "options": [
            {
              "value": "left",
              "label": "t:sections.all.content_settings.content_alignment.left.label"
            },
            {
              "value": "center",
              "label": "t:sections.all.content_settings.content_alignment.center.label"
            },
            {
              "value": "right",
              "label": "t:sections.all.content_settings.content_alignment.right.label"
            }
          ]
        },
        {
          "type": "header",
          "content": "t:sections.all.content_settings.typography.label",
          "info": "t:sections.all.content_setting.info"
        },
        {
          "type": "paragraph",
          "content": "t:sections.all.content_settings.subheading.label"
        },
        {
          "type": "range",
          "id": "subheading_font_size",
          "label": "t:sections.all.content_settings.font_size.label",
          "default": 12,
          "min": 10,
          "max": 30,
          "step": 1,
          "unit": "px"
        },
        {
          "type": "select",
          "id": "subheading_font_weight",
          "label": "t:sections.all.content_settings.font_weight.label",
          "default": "subheading_weight",
          "options": [
            {
              "value": "body_weight",
              "label": "t:sections.all.content_settings.font_weight.body_weight.label"
            },
            {
              "value": "subheading_weight",
              "label": "t:sections.all.content_settings.font_weight.subheading_weight.label"
            },
            {
              "value": "heading_weight",
              "label": "t:sections.all.content_settings.font_weight.heading_weight.label"
            }
          ]
        },
        {
          "type": "range",
          "id": "subheading_spacing_bottom",
          "label": "t:sections.all.content_settings.spacing.spacing_bottom.label",
          "default": 15,
          "min": 0,
          "max": 30,
          "step": 1,
          "unit": "px"
        },
        {
          "type": "paragraph",
          "content": "t:sections.all.content_settings.heading.label"
        },
        {
          "type": "range",
          "id": "heading_font_size",
          "label": "t:sections.all.content_settings.font_size.label",
          "default": 36,
          "min": 10,
          "max": 90,
          "step": 1,
          "unit": "px"
        },
        {
          "type": "select",
          "id": "heading_font_weight",
          "label": "t:sections.all.content_settings.font_weight.label",
          "default": "heading_weight",
          "options": [
            {
              "value": "body_weight",
              "label": "t:sections.all.content_settings.font_weight.body_weight.label"
            },
            {
              "value": "subheading_weight",
              "label": "t:sections.all.content_settings.font_weight.subheading_weight.label"
            },
            {
              "value": "heading_weight",
              "label": "t:sections.all.content_settings.font_weight.heading_weight.label"
            }
          ]
        },
        {
          "type": "checkbox",
          "id": "heading_uppercase",
          "label": "t:sections.all.text_transform.uppercase.label"
        },
        {
          "type": "range",
          "id": "heading_spacing_bottom",
          "label": "t:sections.all.content_settings.spacing.spacing_bottom.label",
          "default": 20,
          "min": 0,
          "max": 30,
          "step": 1,
          "unit": "px"
        },
        {
          "type": "paragraph",
          "content": "t:sections.all.content_settings.description.label"
        },
        {
          "type": "range",
          "id": "des_font_size",
          "label": "t:sections.all.content_settings.font_size.label",
          "default": 14,
          "min": 10,
          "max": 30,
          "step": 1,
          "unit": "px"
        },
        {
          "type": "select",
          "id": "des_font_weight",
          "label": "t:sections.all.content_settings.font_weight.label",
          "default": "body_weight",
          "options": [
            {
              "value": "body_weight",
              "label": "t:sections.all.content_settings.font_weight.body_weight.label"
            },
            {
              "value": "subheading_weight",
              "label": "t:sections.all.content_settings.font_weight.subheading_weight.label"
            },
            {
              "value": "heading_weight",
              "label": "t:sections.all.content_settings.font_weight.heading_weight.label"
            }
          ]
        },
        {
          "type": "range",
          "id": "des_spacing_bottom",
          "label": "t:sections.all.content_settings.spacing.spacing_bottom.label",
          "default": 30,
          "min": 0,
          "max": 40,
          "step": 1,
          "unit": "px"
        }
      ]
    },
    {
      "type": "video",
      "name": "Video",
      "limit": 4,
      "settings": [
        {
          "type": "video_url",
          "id": "video_url",
          "accept": ["youtube", "vimeo"],
          "label": "t:sections.all.video.video_url.label",
          "default": "https://www.youtube.com/watch?v=_9VUPq3SxOc",
          "info": "t:sections.all.video.video_url.info"
        },
        {
          "type": "video",
          "id": "video_local",
          "label": "t:sections.all.video.video_local.label",
          "info": "t:sections.all.video.video_local.info"
        },
        {
          "type": "image_picker",
          "id": "video_poster",
          "label": "t:sections.all.video.video_poster.label",
          "info": "t:sections.all.video.video_poster.info"
        },
        {
          "type": "checkbox",
          "id": "autoplay",
          "label": "t:sections.all.video.autoplay.label",
          "info": "t:sections.all.video.autoplay.info",
          "default": true
        },
        {
          "type": "select",
          "id": "aspect_ratio",
          "default": "16/9",
          "label": "t:sections.all.image.image_ratio.label",
          "options": [
            {
              "value": "16/9",
              "label": "16/9"
            },
            {
              "value": "21/9",
              "label": "21/9"
            },
            {
              "value": "adapt",
              "label": "t:sections.all.video.adapt_to_video.label"
            },
            {
              "value": "custom",
              "label": "t:sections.all.image.image_ratio.custom.label"
            }
          ]
        },
        {
          "type": "text",
          "id": "custom_ratio",
          "label": "t:sections.all.image.image_ratio.custom_ratio.label"
        },
        {
          "type": "select",
          "id": "vertical_align",
          "label": "t:sections.multi_content.block_settings.vertical_align.label",
          "default": "start",
          "options": [
            {
              "value": "start",
              "label": "t:sections.multi_content.block_settings.vertical_align.top.label"
            },
            {
              "value": "center",
              "label": "t:sections.multi_content.block_settings.vertical_align.middle.label"
            },
            {
              "value": "end",
              "label": "t:sections.multi_content.block_settings.vertical_align.bottom.label"
            }
          ]
        },
        {
          "type": "select",
          "id": "content_width",
          "label": "t:sections.all.content_setting.width.label",
          "default": "50%",
          "options": [
            {
              "value": "16.666%",
              "label": "16%"
            },
            {
              "value": "20%",
              "label": "20%"
            },
            {
              "value": "25%",
              "label": "25%"
            },
            {
              "value": "33.333%",
              "label": "33%"
            },
            {
              "value": "50%",
              "label": "50%"
            },
            {
              "value": "66.666%",
              "label": "66%"
            },
            {
              "value": "75%",
              "label": "75%"
            },
            {
              "value": "80%",
              "label": "80%"
            },
            {
              "value": "100%",
              "label": "100%"
            }
          ]
        }
      ]
    },
    {
      "type": "video_with_text_overlay",
      "name": "t:sections.video_with_text_overlay.name",
      "limit": 4,
      "settings": [
        {
          "type": "color_scheme",
          "id": "color_scheme",
          "label": "t:sections.all.color_scheme.label",
          "default": "scheme-1"
        },
        {
          "type": "video",
          "id": "video_local",
          "label": "t:sections.all.video.video_local.label",
          "info": "t:sections.all.video.video_local.info"
        },
        {
          "type": "image_picker",
          "id": "video_poster",
          "label": "t:sections.all.video.video_poster.label",
          "info": "t:sections.all.video.video_poster.info"
        },
        {
          "type": "checkbox",
          "id": "autoplay",
          "label": "t:sections.all.video.autoplay.label",
          "info": "t:sections.all.video.autoplay.info",
          "default": true
        },
        {
          "type": "select",
          "id": "aspect_ratio",
          "default": "16/9",
          "label": "t:sections.all.image.image_ratio.label",
          "options": [
            {
              "value": "16/9",
              "label": "16/9"
            },
            {
              "value": "21/9",
              "label": "21/9"
            },
            {
              "value": "adapt",
              "label": "t:sections.all.video.adapt_to_video.label"
            },
            {
              "value": "custom",
              "label": "t:sections.all.image.image_ratio.custom.label"
            }
          ]
        },
        {
          "type": "text",
          "id": "custom_ratio",
          "label": "t:sections.all.image.image_ratio.custom_ratio.label"
        },
        {
          "type": "select",
          "id": "content_width",
          "label": "t:sections.all.content_setting.width.label",
          "default": "50%",
          "options": [
            {
              "value": "20%",
              "label": "20%"
            },
            {
              "value": "25%",
              "label": "25%"
            },
            {
              "value": "33.3333%",
              "label": "33%"
            },
            {
              "value": "50%",
              "label": "50%"
            },
            {
              "value": "66.6666%",
              "label": "66%"
            },
            {
              "value": "75%",
              "label": "75%"
            },
            {
              "value": "80%",
              "label": "80%"
            },
            {
              "value": "100%",
              "label": "100%"
            }
          ]
        },
        {
          "type": "select",
          "id": "vertical_align",
          "label": "t:sections.multi_content.block_settings.vertical_align.label",
          "default": "start",
          "options": [
            {
              "value": "start",
              "label": "t:sections.multi_content.block_settings.vertical_align.top.label"
            },
            {
              "value": "center",
              "label": "t:sections.multi_content.block_settings.vertical_align.middle.label"
            },
            {
              "value": "end",
              "label": "t:sections.multi_content.block_settings.vertical_align.bottom.label"
            }
          ]
        },
        {
          "type": "header",
          "content": "t:sections.all.image.label"
        },
        {
          "type": "range",
          "id": "image_overlay_opacity",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "%",
          "label": "t:sections.all.image.overlay_opacity.label",
          "default": 0
        },
        {
          "type": "header",
          "content": "t:sections.all.contents.label"
        },
        {
          "type": "text",
          "id": "subheading",
          "default": "Subheading",
          "label": "t:sections.all.contents.subheading.label"
        },
        {
          "type": "textarea",
          "id": "heading",
          "label": "t:sections.all.contents.heading.label",
          "default": "Video text overlay"
        },
        {
          "type": "richtext",
          "id": "description",
          "label": "t:sections.all.contents.description.label",
          "default": "<p>Description</p>"
        },
        {
          "type": "text",
          "id": "first_button_label",
          "default": "Button label",
          "label": "t:sections.all.contents.button.first_button_label.label",
          "info": "t:sections.all.contents.button.first_button_label.info"
        },
        {
          "type": "url",
          "id": "first_button_link",
          "label": "t:sections.all.contents.button.first_button_link.label"
        },
        {
          "type": "select",
          "id": "first_button_type",
          "label": "t:sections.all.contents.button.button_type.label",
          "default": "primary",
          "options": [
            {
              "value": "primary",
              "label": "t:sections.all.contents.button.button_type.primary.label"
            },
            {
              "value": "outline",
              "label": "t:sections.all.contents.button.button_type.outline.label"
            },
            {
              "value": "link",
              "label": "t:sections.all.contents.button.button_type.link.label"
            }
          ]
        },
        {
          "type": "text",
          "id": "second_button_label",
          "label": "t:sections.all.contents.button.secondary_button_label.label",
          "info": "t:sections.all.contents.button.secondary_button_label.info"
        },
        {
          "type": "url",
          "id": "second_button_link",
          "label": "t:sections.all.contents.button.secondary_button_link.label"
        },
        {
          "type": "select",
          "id": "second_button_type",
          "label": "t:sections.all.contents.button.button_type.label",
          "default": "primary",
          "options": [
            {
              "value": "primary",
              "label": "t:sections.all.contents.button.button_type.primary.label"
            },
            {
              "value": "outline",
              "label": "t:sections.all.contents.button.button_type.outline.label"
            },
            {
              "value": "link",
              "label": "t:sections.all.contents.button.button_type.link.label"
            }
          ]
        },
        {
          "type": "header",
          "content": "t:sections.all.content_setting.label"
        },
        {
          "type": "select",
          "id": "content_alignment",
          "label": "t:sections.all.content_settings.content_alignment.label",
          "default": "left",
          "options": [
            {
              "value": "left",
              "label": "t:sections.all.content_settings.content_alignment.left.label"
            },
            {
              "value": "center",
              "label": "t:sections.all.content_settings.content_alignment.center.label"
            },
            {
              "value": "right",
              "label": "t:sections.all.content_settings.content_alignment.right.label"
            }
          ]
        },
        {
          "type": "select",
          "id": "content_position",
          "default": "top-left",
          "label": "t:sections.all.content_settings.content_position.label",
          "options": [
            {
              "value": "top-left",
              "label": "t:sections.all.content_settings.content_position.top_left.label"
            },
            {
              "value": "top-center",
              "label": "t:sections.all.content_settings.content_position.top_center.label"
            },
            {
              "value": "top-right",
              "label": "t:sections.all.content_settings.content_position.top_right.label"
            },
            {
              "value": "middle-left",
              "label": "t:sections.all.content_settings.content_position.middle_left.label"
            },
            {
              "value": "middle-center",
              "label": "t:sections.all.content_settings.content_position.middle_center.label"
            },
            {
              "value": "middle-right",
              "label": "t:sections.all.content_settings.content_position.middle_right.label"
            },
            {
              "value": "bottom-left",
              "label": "t:sections.all.content_settings.content_position.bottom_left.label"
            },
            {
              "value": "bottom-center",
              "label": "t:sections.all.content_settings.content_position.bottom_center.label"
            },
            {
              "value": "bottom-right",
              "label": "t:sections.all.content_settings.content_position.bottom_right.label"
            }
          ]
        },
        {
          "type": "range",
          "id": "content_padding_block",
          "min": 10,
          "max": 60,
          "step": 1,
          "unit": "px",
          "default": 30,
          "label": "t:sections.all.content_settings.content_padding_block.label"
        },
        {
          "type": "range",
          "id": "content_padding_inline",
          "min": 10,
          "max": 60,
          "step": 5,
          "unit": "px",
          "default": 30,
          "label": "t:sections.all.content_settings.content_padding_inline.label"
        },
        {
          "type": "checkbox",
          "id": "content_below_image",
          "label": "t:sections.multi_content.block_settings.show_below_content_video.label"
        },
        {
          "type": "header",
          "content": "t:sections.all.content_settings.typography.label",
          "info": "t:sections.all.content_setting.info"
        },
        {
          "type": "paragraph",
          "content": "t:sections.all.content_settings.subheading.label"
        },
        {
          "type": "range",
          "id": "subheading_font_size",
          "label": "t:sections.all.content_settings.font_size.label",
          "default": 12,
          "min": 10,
          "max": 30,
          "step": 1,
          "unit": "px"
        },
        {
          "type": "select",
          "id": "subheading_font_weight",
          "label": "t:sections.all.content_settings.font_weight.label",
          "default": "subheading_weight",
          "options": [
            {
              "value": "body_weight",
              "label": "t:sections.all.content_settings.font_weight.body_weight.label"
            },
            {
              "value": "subheading_weight",
              "label": "t:sections.all.content_settings.font_weight.subheading_weight.label"
            },
            {
              "value": "heading_weight",
              "label": "t:sections.all.content_settings.font_weight.heading_weight.label"
            }
          ]
        },
        {
          "type": "range",
          "id": "subheading_spacing_bottom",
          "label": "t:sections.all.content_settings.spacing.spacing_bottom.label",
          "default": 10,
          "min": 0,
          "max": 30,
          "step": 1,
          "unit": "px"
        },
        {
          "type": "paragraph",
          "content": "t:sections.all.content_settings.heading.label"
        },
        {
          "type": "range",
          "id": "heading_font_size",
          "label": "t:sections.all.content_settings.font_size.label",
          "default": 36,
          "min": 10,
          "max": 90,
          "step": 1,
          "unit": "px"
        },
        {
          "type": "select",
          "id": "heading_font_weight",
          "label": "t:sections.all.content_settings.font_weight.label",
          "default": "heading_weight",
          "options": [
            {
              "value": "body_weight",
              "label": "t:sections.all.content_settings.font_weight.body_weight.label"
            },
            {
              "value": "subheading_weight",
              "label": "t:sections.all.content_settings.font_weight.subheading_weight.label"
            },
            {
              "value": "heading_weight",
              "label": "t:sections.all.content_settings.font_weight.heading_weight.label"
            }
          ]
        },
        {
          "type": "checkbox",
          "id": "heading_uppercase",
          "label": "t:sections.all.text_transform.uppercase.label"
        },
        {
          "type": "range",
          "id": "heading_spacing_bottom",
          "label": "t:sections.all.content_settings.spacing.spacing_bottom.label",
          "default": 10,
          "min": 0,
          "max": 30,
          "step": 1,
          "unit": "px"
        },

        {
          "type": "paragraph",
          "content": "t:sections.all.content_settings.description.label"
        },
        {
          "type": "range",
          "id": "des_font_size",
          "label": "t:sections.all.content_settings.font_size.label",
          "default": 14,
          "min": 10,
          "max": 30,
          "step": 1,
          "unit": "px"
        },
        {
          "type": "select",
          "id": "des_font_weight",
          "label": "t:sections.all.content_settings.font_weight.label",
          "default": "body_weight",
          "options": [
            {
              "value": "body_weight",
              "label": "t:sections.all.content_settings.font_weight.body_weight.label"
            },
            {
              "value": "subheading_weight",
              "label": "t:sections.all.content_settings.font_weight.subheading_weight.label"
            },
            {
              "value": "heading_weight",
              "label": "t:sections.all.content_settings.font_weight.heading_weight.label"
            }
          ]
        },
        {
          "type": "range",
          "id": "des_spacing_bottom",
          "label": "t:sections.all.content_settings.spacing.spacing_bottom.label",
          "default": 10,
          "min": 0,
          "max": 40,
          "step": 1,
          "unit": "px"
        }
      ]
    },
    {
      "type": "icon_box",
      "name": "t:sections.icon_box.name",
      "limit": 2,
      "settings": [
        {
          "type": "select",
          "id": "content_width",
          "label": "t:sections.all.content_setting.width.label",
          "default": "50%",
          "options": [
            {
              "value": "20%",
              "label": "20%"
            },
            {
              "value": "25%",
              "label": "25%"
            },
            {
              "value": "33.3333%",
              "label": "33%"
            },
            {
              "value": "50%",
              "label": "50%"
            },
            {
              "value": "66.6666%",
              "label": "66%"
            },
            {
              "value": "75%",
              "label": "75%"
            },
            {
              "value": "80%",
              "label": "80%"
            },
            {
              "value": "100%",
              "label": "100%"
            }
          ]
        },
        {
          "type": "header",
          "content": "t:sections.multi_content.block_settings.header.Content_1"
        },
        {
          "type": "image_picker",
          "id": "icon_image",
          "label": "t:sections.all.image.label"
        },
        {
          "type": "range",
          "id": "icon_size",
          "label": "t:sections.icon_box.settings.icon_size.label",
          "default": 32,
          "min": 10,
          "max": 60,
          "step": 1,
          "unit": "px"
        },
        {
          "type": "text",
          "id": "heading",
          "label": "t:sections.all.contents.heading.label",
          "default": "This is heading text"
        },
        {
          "type": "richtext",
          "id": "description",
          "label": "t:sections.all.contents.description.label",
          "default": "<p>Tell about your service.</p>"
        },
        {
          "type": "header",
          "content": "t:sections.multi_content.block_settings.header.Content_2"
        },
        {
          "type": "image_picker",
          "id": "icon_image2",
          "label": "t:sections.all.image.label"
        },
        {
          "type": "range",
          "id": "icon_size2",
          "label": "t:sections.icon_box.settings.icon_size.label",
          "default": 32,
          "min": 10,
          "max": 60,
          "step": 1,
          "unit": "px"
        },
        {
          "type": "text",
          "id": "heading2",
          "label": "t:sections.all.contents.heading.label",
          "default": "This is heading text"
        },
        {
          "type": "richtext",
          "id": "description2",
          "label": "t:sections.all.contents.description.label",
          "default": "<p>Tell about your service.</p>"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "t:sections.multi_content.name",
      "blocks": [
        {
          "type": "image_with_text_overlay",
          "settings": {
            "heading": "Text overlay",
            "description": "<p>Description</p>",
            "first_button_label": "Button label"
          }
        },
        {
          "type": "image_with_text_overlay",
          "settings": {
            "description": "<p>Description</p>",
            "first_button_label": "Button label"
          }
        }
      ]
    }
  ]
}
{% endschema %}
