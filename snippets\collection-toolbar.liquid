{%- liquid
  assign enable_filtering = enable_filtering
  assign default_filter = default_filter
  assign filter_layout = filter_layout
  assign number_products_per_page = section_st.number_products_per_page
  assign sorting = section_st.sorting
  assign grid_list = section_st.grid_list
  assign products_column = products_column
-%}
{% liquid
  assign enable_rtl = settings.rtl
  assign iso_code = request.locale.iso_code
  assign lis_language_rtl = settings.language_rtl
  if lis_language_rtl != blank
    if lis_language_rtl contains iso_code
      assign enable_rtl = true
    else
      assign enable_rtl = false
    endif
  endif
%}
<div class="toolbar toolbar-products flex gap-15 gap-md-30 align-center mb-20{% if filter_layout == 'horizontal' %} mt-1025-60{% endif %}">
  {%- if enable_filtering or number_products_per_page -%}
    {% if number_products_per_page %}
      <div class="toolbar-left flex gap-15 gap-md-30 align-center flex-1">
        {%- if enable_filtering -%}
          <btn-filter class="btn-filter btn-primary inline-flex content-center gap-5 subheading_weight rounded-3 px-10 py-5 {{ filter_layout }} overlay{% if enable_filtering and filter_layout != 'drawer' %} inline-flex hidden-1025{% endif %}">
            <svg width="14" height="13" fill="none">
              <path fill="#fff" d="M11.58 1.5H2.42l3.855 4.553a.474.474 0 0 1 .096.191.482.482 0 0 1 .041.192v3.459l1.176.574V6.436c0-.073.009-.142.027-.206a.72.72 0 0 1 .11-.177L11.58 1.5ZM12.838.338c.064 0 .128.014.191.04.064.02.123.051.178.097.128.1.196.232.205.396a.533.533 0 0 1-.137.424L8.75 6.655v4.757a.695.695 0 0 1-.014.137.587.587 0 0 1-.383.424.598.598 0 0 1-.45-.041L5.577 10.77a.597.597 0 0 1-.246-.205.648.648 0 0 1-.082-.315V6.654L.725 1.294a.719.719 0 0 1-.11-.177.743.743 0 0 1-.027-.205.54.54 0 0 1 .164-.396.563.563 0 0 1 .41-.178h11.676Z"/>
            </svg>
            {{ 'collections.toolbar.filter' | t }}
          </btn-filter>
        {%- endif -%}
        {%- if number_products_per_page -%}
          <div class="toolbar-amount hidden block-1025 relative" id="toolbar-amount">
            {%- if collection.results_count -%}
              {{ 'templates.search.results_with_count' | t: terms: collection.terms, count: collection.results_count }}
            {%- elsif collection.products_count == collection.all_products_count -%}
              {{ 'products.facets.product_count_simple' | t: count: collection.products_count }}
            {%- else -%}
              {{
                'products.facets.product_count'
                | t: product_count: collection.products_count, count: collection.all_products_count
              }}
            {%- endif -%}
          </div>
        {%- endif -%}
      </div>
    {% else %}
      {%- if enable_filtering -%}
        <btn-filter class="btn-filter btn-primary inline-flex content-center gap-5 subheading_weight rounded-3 px-10 py-5 {{ filter_layout }} overlay{% if enable_filtering and filter_layout != 'drawer' %} inline-flex hidden-1025{% endif %}">
          <svg width="14" height="13" fill="none">
            <path fill="#fff" d="M11.58 1.5H2.42l3.855 4.553a.474.474 0 0 1 .096.191.482.482 0 0 1 .041.192v3.459l1.176.574V6.436c0-.073.009-.142.027-.206a.72.72 0 0 1 .11-.177L11.58 1.5ZM12.838.338c.064 0 .128.014.191.04.064.02.123.051.178.097.128.1.196.232.205.396a.533.533 0 0 1-.137.424L8.75 6.655v4.757a.695.695 0 0 1-.014.137.587.587 0 0 1-.383.424.598.598 0 0 1-.45-.041L5.577 10.77a.597.597 0 0 1-.246-.205.648.648 0 0 1-.082-.315V6.654L.725 1.294a.719.719 0 0 1-.11-.177.743.743 0 0 1-.027-.205.54.54 0 0 1 .164-.396.563.563 0 0 1 .41-.178h11.676Z"/>
          </svg>
          {{ 'collections.toolbar.filter' | t }}
        </btn-filter>
      {%- endif -%}
    {% endif %}
  {%- endif -%}
  {%- if grid_list -%}
    <div class="show-type-products flex grow-1 gap-5{% if number_products_per_page and sorting  %} justify-content-center{% endif %}{% if number_products_per_page and sorting == false  %} justify-content-end{% endif %}">
      {%- if pageurl == 'list' -%}
        <view-mode class="grid-mode view-mode pointer tooltip relative w-30 h-30 inline-flex content-center" data-view="grid">
          <svg xmlns="http://www.w3.org/2000/svg" width="15" height="14" fill="none">
            <g fill="currentColor"><path d="M2.073 4a2 2 0 1 0 0-4 2 2 0 0 0 0 4ZM2.073 9a2 2 0 1 0 0-4 2 2 0 0 0 0 4ZM2.073 14a2 2 0 1 0 0-4 2 2 0 0 0 0 4ZM7.073 4a2 2 0 1 0 0-4 2 2 0 0 0 0 4ZM7.073 9a2 2 0 1 0 0-4 2 2 0 0 0 0 4ZM7.073 14a2 2 0 1 0 0-4 2 2 0 0 0 0 4ZM12.073 4a2 2 0 1 0 0-4 2 2 0 0 0 0 4ZM12.073 9a2 2 0 1 0 0-4 2 2 0 0 0 0 4ZM12.073 14a2 2 0 1 0 0-4 2 2 0 0 0 0 4Z"/></g>
          </svg>
          <span class="visually-hidden">{{ 'collections.toolbar.progress_bar.grid' | t }}</span>
          <span class="tooltip-content invisible rounded-3 absolute pointer-none">
            {{- 'collections.toolbar.progress_bar.grid' | t -}}
          </span>
        </view-mode>
        <view-mode
          class="grid-mode view-mode pointer active tooltip relative w-30 h-30 inline-flex content-center"
          data-view="list"
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="14" fill="none">
            <g fill="currentColor"><path d="M2.073 4a2 2 0 1 0 0-4 2 2 0 0 0 0 4ZM2.073 9a2 2 0 1 0 0-4 2 2 0 0 0 0 4ZM2.073 14a2 2 0 1 0 0-4 2 2 0 0 0 0 4ZM19.073 2a1 1 0 0 1-1 1h-12a1 1 0 0 1 0-2h12a1 1 0 0 1 1 1ZM19.073 7a1 1 0 0 1-1 1h-12a1 1 0 0 1 0-2h12a1 1 0 0 1 1 1ZM19.073 12a1 1 0 0 1-1 1h-12a1 1 0 0 1 0-2h12a1 1 0 0 1 1 1Z"/></g>
          </svg>
          <span class="visually-hidden">{{ 'collections.toolbar.progress_bar.list' | t }}</span>
          <span class="tooltip-content invisible rounded-3 absolute pointer-none">
            {{- 'collections.toolbar.progress_bar.list' | t -}}
          </span>
        </view-mode>
      {%- else -%}
        <grid-mode
          class="grid-mode pointer hover-heading-color transition w-30 h-30 hidden inline-flex-1025 content-center tooltip relative grid-mode-2{% if products_column == 2 %} active{% endif %}"
          data-grid-mode="2"
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="15" height="14" fill="none">
            <g fill="currentColor"><path d="M11.073 6a3 3 0 1 0 0-6 3 3 0 0 0 0 6ZM4.07 6a3 3 0 1 0 0-6 3 3 0 0 0 0 6ZM11.073 14a3 3 0 1 0 0-6 3 3 0 0 0 0 6ZM3.927 14a3 3 0 1 0 0-6 3 3 0 0 0 0 6Z"/></g>
          </svg>
          <span class="visually-hidden">{{ 'collections.toolbar.progress_bar.columns' | t: amount: 2 }}</span>
          <span class="tooltip-content invisible rounded-3 absolute pointer-none">
            {{- 'collections.toolbar.progress_bar.columns' | t: amount: 2 -}}
          </span>
        </grid-mode>
        <grid-mode
          class="grid-mode pointer hover-heading-color transition w-30 h-30 hidden inline-flex-1025 content-center tooltip relative grid-mode-3{% if products_column == 3 %} active{% endif %}"
          data-grid-mode="3"
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="15" height="14" fill="none">
            <g fill="currentColor"><path d="M2.073 4a2 2 0 1 0 0-4 2 2 0 0 0 0 4ZM2.073 9a2 2 0 1 0 0-4 2 2 0 0 0 0 4ZM2.073 14a2 2 0 1 0 0-4 2 2 0 0 0 0 4ZM7.073 4a2 2 0 1 0 0-4 2 2 0 0 0 0 4ZM7.073 9a2 2 0 1 0 0-4 2 2 0 0 0 0 4ZM7.073 14a2 2 0 1 0 0-4 2 2 0 0 0 0 4ZM12.073 4a2 2 0 1 0 0-4 2 2 0 0 0 0 4ZM12.073 9a2 2 0 1 0 0-4 2 2 0 0 0 0 4ZM12.073 14a2 2 0 1 0 0-4 2 2 0 0 0 0 4Z"/></g>
          </svg>
          <span class="visually-hidden">{{ 'collections.toolbar.progress_bar.columns' | t: amount: 3 }}</span>
          <span class="tooltip-content invisible rounded-3 absolute pointer-none">
            {{- 'collections.toolbar.progress_bar.columns' | t: amount: 3 -}}
          </span>
        </grid-mode>
        <grid-mode
          class="grid-mode pointer hover-heading-color transition w-30 h-30 inline-flex content-center tooltip relative grid-mode-4{% if products_column == 4 %} active{% endif %}"
          data-grid-mode="4"
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="14" fill="none">
            <g fill="currentColor"><path d="M2.073 4a2 2 0 1 0 0-4 2 2 0 0 0 0 4ZM2.073 9a2 2 0 1 0 0-4 2 2 0 0 0 0 4ZM2.073 14a2 2 0 1 0 0-4 2 2 0 0 0 0 4ZM7.073 4a2 2 0 1 0 0-4 2 2 0 0 0 0 4ZM7.073 9a2 2 0 1 0 0-4 2 2 0 0 0 0 4ZM7.073 14a2 2 0 1 0 0-4 2 2 0 0 0 0 4ZM12.073 4a2 2 0 1 0 0-4 2 2 0 0 0 0 4ZM12.073 9a2 2 0 1 0 0-4 2 2 0 0 0 0 4ZM12.073 14a2 2 0 1 0 0-4 2 2 0 0 0 0 4ZM17.073 4a2 2 0 1 0 0-4 2 2 0 0 0 0 4ZM17.073 9a2 2 0 1 0 0-4 2 2 0 0 0 0 4ZM17.073 14a2 2 0 1 0 0-4 2 2 0 0 0 0 4Z"/></g>
          </svg>
          <span class="visually-hidden">{{ 'collections.toolbar.progress_bar.columns' | t: amount: 4 }}</span>
          <span class="tooltip-content invisible rounded-3 absolute pointer-none">
            {{- 'collections.toolbar.progress_bar.columns' | t: amount: 4 -}}
          </span>
        </grid-mode>
        <view-mode
          class="grid-mode view-mode pointer hover-heading-color transition tooltip relative w-30 h-30 inline-flex content-center"
          data-view="list"
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="14" fill="none">
            <g fill="currentColor"><path d="M2.073 4a2 2 0 1 0 0-4 2 2 0 0 0 0 4ZM2.073 9a2 2 0 1 0 0-4 2 2 0 0 0 0 4ZM2.073 14a2 2 0 1 0 0-4 2 2 0 0 0 0 4ZM19.073 2a1 1 0 0 1-1 1h-12a1 1 0 0 1 0-2h12a1 1 0 0 1 1 1ZM19.073 7a1 1 0 0 1-1 1h-12a1 1 0 0 1 0-2h12a1 1 0 0 1 1 1ZM19.073 12a1 1 0 0 1-1 1h-12a1 1 0 0 1 0-2h12a1 1 0 0 1 1 1Z"/></g>
          </svg>
          <span class="visually-hidden">{{ 'collections.toolbar.progress_bar.list' | t }}</span>
          <span class="tooltip-content invisible rounded-3 absolute pointer-none">
            {{- 'collections.toolbar.progress_bar.list' | t -}}
          </span>
        </view-mode>
      {%- endif -%}
    </div>
  {%- endif -%}
  {%- if sorting -%}
    <div class="toolbar-sorter sorter flex gap-10 align-center relative flex-1 flex-end">
      <div class="select-custom pointer user-select-none">
        {%- assign sort_by = collection.sort_by | default: collection.default_sort_by -%}
        <select-sorter class="select-selected user-select-none inline-flex align-center gap-10 gap-lg-20 lh-normal pointer overlay">
          <label class="border border-1025-0 rounded-3 pointer inline-flex align-center lh-normal gap-5 py-5 px-10">
            {{- 'products.facets.sort_by_label' | t }}
            <svg class="icon-down transition active-rotated block hidden-1025" width="10" height="6">
              <use href="#icon-arrow-down"></use>
            </svg>
          </label>
          <span class="heading-color subheading_weight hidden inline-block-1025">
            {%- for option in collection.sort_options -%}
              {% if option.value == sort_by %}{{ option.name | escape }}{% endif %}
            {%- endfor -%}
          </span>
          <svg class="icon-down transition active-rotated hidden block-1025" width="10" height="6">
            <use href="#icon-arrow-down"></use>
          </svg>
        </select-sorter>
        <div
          class="select-custom__content facet-filters__sort invisible transition rounded-5 select__select absolute shadow list-none z-2 py-20 px-25 color-default{% if enable_rtl %} left-0{% else %} right-0{% endif %}"
          id="SortBy"
          aria-describedby="a11y-refresh-page-message"
        >
          <icon-close class="pointer-none border bg-white inline-flex hidden-sm mx-auto rounded-50 overflow-hidden absolute top-0 inset-x-0 w-50 h-50 content-center heading-color">
            <svg width="13" height="13" viewBox="0 0 13 13" fill="none" class="transition">
              <use href="#icon-close"></use>
            </svg>
          </icon-close>
          <div class="h-full overflow-auto">
            {%- for option in collection.sort_options -%}
              <filter-sort
                class="block py-4 text-color hover-heading-color transition body_weight"
                value="{{ option.value | escape }}"
                {% if option.value == sort_by %}
                  selected="selected"
                {% endif %}
              >
                {{ option.name | escape }}
              </filter-sort>
            {%- endfor -%}
          </div>
        </div>
      </div>
    </div>
  {%- endif -%}
</div>
